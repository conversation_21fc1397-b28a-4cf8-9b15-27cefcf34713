<template>
  <ContentWrap>
    <div class="pc-view" v-if="!mobile">
      <vxe-toolbar custom ref="toolbarRef" size="mini">
        <template #buttons>
          <el-button
            type="primary"
            size="small"
            @click="openForm('create')"
            v-hasPermi="['production:day:create']"
          >
            新增
          </el-button>
          <el-button
            @click="handleAudit()"
            type="success"
            v-hasPermi="['production:day:audit']"
            v-if="queryParams.workType === 0"
            style="margin-left: 30px"
            size="small"
            >审核</el-button
          >
          <el-button
            @click="handleExport()"
            type="warning"
            v-hasPermi="['production:day:export']"
            style="margin-left: 30px"
            size="small"
            >导出</el-button
          >
          <el-button
            @click="handleAttendance()"
            type="warning"
            v-hasPermi="['production:day:export']"
            style="margin-left: 30px"
            size="small"
            >导出出勤工时</el-button
          >
          <el-button @click="handleType()" style="margin-left: 30px" size="small">{{
            queryParams.workType === 0 ? '查看异常工时' : '查看生产日报'
          }}</el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100vh-260px)]">
        <vxe-table
          :key="'tableKey-' + queryParams.workType"
          :row-config="{ height: 30, keyField: 'id' }"
          ref="tableRef"
          @scroll="handleScroll"
          @rendered="handleRendered"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          @cell-dblclick="cellClickEvent"
          :filter-config="{ showIcon: false }"
          border
          stripe
          align="center"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :menu-config="menuConfig"
          @menu-click="menuClickEvent"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
          height="100%"
          max-height="100%"
          :cell-style="cellStyleMethod"
          :footer-cell-config="{ height: 30 }"
          :footer-data="footerData"
          show-footer
          :footer-cell-style="{ padding: 0, background: '#dcefdc', border: '1px solid #ebeef5' }"
        >
          <vxe-column type="checkbox" min-width="40" field="id" />
          <vxe-column field="teamLeader" min-width="100">
            <template #header>
              <div>班组长</div>
              <el-input
                v-model="queryParams.teamLeader"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column field="productionLine" min-width="100">
            <template #header>
              <div>产线</div>
              <el-select
                v-model="queryParams.productionLine"
                @change="handleList"
                placeholder="选择产线"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              {{ formattedProductionLine(row.productionLine) }}
            </template>
          </vxe-column>
          <vxe-column field="dateStr" min-width="100">
            <template #header>
              <div style="display: flex; flex-direction: column; gap: 8px">
                <el-date-picker
                  v-model="queryParams.startDate"
                  @change="handleList"
                  type="date"
                  placeholder="开始日期"
                  size="small"
                  clearable
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
                <el-date-picker
                  v-model="queryParams.endDate"
                  @change="handleList"
                  type="date"
                  placeholder="结束日期"
                  size="small"
                  clearable
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </div>
            </template>
            <template #default="{ row }">
              {{ row.dateStr && formatToDate(row.dateStr) }}
            </template>
          </vxe-column>
          <vxe-column
            title="时间段"
            min-width="120"
            field="productionTime"
            v-if="queryParams.workType === 0"
          >
            <template #default="{ row }">
              {{ row.productionTime && formatTime(row.productionTime) }}
            </template>
          </vxe-column>
          <vxe-column
            title="异常时间段"
            min-width="120"
            field="abnormalTime"
            v-if="queryParams.workType === 1"
          >
            <template #default="{ row }">
              {{ row.abnormalTime && formatTime(row.abnormalTime) }}
            </template>
          </vxe-column>
          <!-- 支持查询的字段 - 生产工单号 -->
          <vxe-column field="productionOrderCode" min-width="150">
            <template #header>
              <div>生产工单号</div>
              <el-input
                v-model="queryParams.productionOrderCode"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <!-- 支持查询的字段 - 销售订单号 -->
          <vxe-column field="salesOrderCode" min-width="150">
            <template #header>
              <div>销售订单号</div>
              <el-input
                v-model="queryParams.salesOrderCode"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <!-- 支持查询的字段 - 品号 -->
          <vxe-column field="productNo" min-width="120">
            <template #header>
              <div>品号</div>
              <el-input
                v-model="queryParams.productNo"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column field="isRework" min-width="50" v-if="queryParams.workType === 0">
            <template #header>
              <div>返工</div>
              <el-select
                v-model="queryParams.isRework"
                @change="handleList"
                placeholder="选中回车筛选"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </template>
            <template #default="{ row }">
              {{ formattedIsRework(row.isRework) }}
            </template>
          </vxe-column>
          <vxe-column title="机型/颜色(品名)" min-width="160" field="modelsOrColor" />
          <vxe-column
            title="工单数量"
            min-width="70"
            field="workOrderNum"
            v-if="queryParams.workType === 0"
          />
          <vxe-column title="单位" min-width="50" field="units" />
          <vxe-column
            title="生产数"
            min-width="70"
            field="hoursReportNum"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="累计完成数量"
            min-width="90"
            field="totalReportNum"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="实际工时"
            min-width="70"
            field="actualWork"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="标准工时"
            min-width="70"
            field="standardWork"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="平均工时"
            min-width="70"
            field="avgWork"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="系统总工时"
            min-width="90"
            field="systemTotal"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="组装总工时(时/天)"
            min-width="130"
            field="assembledTotal"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="工时差额"
            min-width="90"
            field="workHourDiff"
            v-if="queryParams.workType === 0"
          />
          <vxe-column field="number" min-width="100" v-if="queryParams.workType === 0">
            <template #header>
              <div>行生产人数</div>
              <el-input
                v-model.number="queryParams.number"
                @change="handleList"
                clearable
                placeholder="输入人数"
                style="width: 100%"
                size="small"
                type="number"
                :min="0"
              />
            </template>
          </vxe-column>

          <!-- 支持查询的字段 -工序 -->
          <vxe-column field="type" width="90" v-if="queryParams.workType === 0">
            <template #header>
              <div>工序</div>
              <el-select
                v-model="queryParams.type"
                @change="handleList"
                placeholder="选择工序"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PRODUCTION_REPORT_TYPE" :value="row.type" />
            </template>
          </vxe-column>
          <vxe-column title="审核状态" width="100" v-if="queryParams.workType === 0">
            <template #default="{ row }">
              <el-tag :type="row.audit === 0 ? 'danger' : 'success'">
                {{ row.audit === 0 ? '未审核' : '已审核' }}
              </el-tag>
            </template>
          </vxe-column>
          <!-- 不支持查询的字段 - 只显示 -->
          <vxe-column
            title="应出勤人数"
            width="100"
            field="requiredAttendanceNum"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="实际出勤人数"
            width="100"
            field="actualAttendanceNum"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="组装人数"
            min-width="100"
            field="assembledNum"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="需求分类号"
            min-width="240"
            field="demand"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="PMC交货备注"
            min-width="240"
            field="pmcRemark"
            v-if="queryParams.workType === 0"
          />
          <vxe-column
            title="异常数量"
            min-width="100"
            field="abnormalReportNum"
            v-if="queryParams.workType === 1"
          />
          <vxe-column
            title="异常人数"
            min-width="100"
            field="abnormalNum"
            v-if="queryParams.workType === 1"
          />
          <vxe-column
            title="异常工时"
            min-width="100"
            field="abnormalWork"
            v-if="queryParams.workType === 1"
          />
          <vxe-column
            title="异常问题点"
            min-width="120"
            field="abnormalRemark"
            v-if="queryParams.workType === 1"
          />
          <vxe-column
            title="异常对策"
            min-width="120"
            field="abnormalCountermeasures"
            v-if="queryParams.workType === 1"
          />
          <!-- <vxe-column title="组装总工时" width="120" field="assembledTotal" /> -->

          <vxe-column
            title="最后活动时间"
            min-width="160"
            field="createTime"
            v-if="queryParams.workType === 0"
          >
            <template #default="{ row }">
              {{ row.createTime && formatToDateTime(row.createTime) }}
            </template>
          </vxe-column>
          <vxe-column title="操作" min-width="100">
            <template #default="{ row }">
              <div v-if="row.audit === 0">
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['production:day:delete']"
                  >删除</el-button
                >
              </div>
              <div v-if="row.audit === 1">
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['production:day:auditdelete']"
                  >删除</el-button
                >
              </div>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 移动端视图 -->
    <div class="h-[calc(100vh-110px)] mobile-view" v-else v-loading="viewSwitching">
      <!-- 移动端搜索栏 -->
      <div class="mobile-search-bar">
        <el-input
          v-model="queryParams.all"
          :suffix-icon="Search"
          placeholder="全域查询"
          @change="handleList"
          class="mobile-search-input"
          size="default"
        />
      </div>

      <!-- 移动端操作按钮 -->
      <div class="mobile-action-bar">
        <el-button
          type="primary"
          @click="openForm('create')"
          v-hasPermi="['production:day:create']"
          class="mobile-action-btn mobile-add-btn"
        >
          <Icon icon="ep:plus" class="btn-icon" />
          <span class="btn-text">新增</span>
        </el-button>
        <el-button
          type="success"
          @click="handleAuditItem()"
          class="mobile-action-btn mobile-audit-btn"
        >
          <Icon icon="ep:check" class="btn-icon" />
          <span class="btn-text">审核</span>
        </el-button>
        <el-button
          :type="isTableView ? 'warning' : 'info'"
          @click="toggleViewMode"
          class="mobile-action-btn mobile-view-btn"
          :loading="viewSwitching"
        >
          <Icon :icon="isTableView ? 'ep:list' : 'ep:grid'" class="btn-icon" />
          <span class="btn-text">{{ isTableView ? '卡片' : '表格' }}</span>
        </el-button>
      </div>

      <!-- 卡片视图 -->
      <div
        v-if="!isTableView"
        class="mobile-card-container"
        v-infinite-scroll="load"
        :infinite-scroll-distance="20"
        :infinite-scroll-immediate="disabled"
        v-loading="loading"
      >
        <div v-for="item in list" :key="item.id" class="mobile-data-card">
          <div class="mobile-card-header">
            <div class="card-title-main">
              <Icon icon="ep:user" class="title-icon" />
              <span>{{ item.teamLeader }}</span>
              <span v-if="item.audit === 0">
                <el-checkbox
                  v-model="item.checked"
                  @change="(checked) => handleCheckboxChange(checked, item)"
                  style="margin-right: 8px"
              /></span>
            </div>
            <div class="card-date">
              <Icon icon="ep:calendar" class="date-icon" />
              <span>{{ item.dateStr && formatToDate(item.dateStr) }}</span>
            </div>
          </div>
          <!-- 基本信息区域 -->
          <div class="mobile-card-section">
            <div class="section-title">
              <Icon icon="ep:info-filled" class="section-icon" />
              <span>基本信息</span>
            </div>
            <div class="info-grid">
              <div class="info-item full-width">
                <span class="info-label">产线</span>
                <span class="info-value">{{ formattedProductionLine(item.productionLine) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">应出勤</span>
                <span class="info-value">{{ item.requiredAttendanceNum || 0 }}人</span>
              </div>
              <div class="info-item">
                <span class="info-label">实际出勤</span>
                <span class="info-value">{{ item.actualAttendanceNum || 0 }}人</span>
              </div>
              <div class="info-item">
                <span class="info-label">组装人数</span>
                <span class="info-value">{{ item.assembledNum || 0 }}人</span>
              </div>
              <div class="info-item">
                <span class="info-label">时间段</span>
                <span class="info-value">{{
                  item.productionTime && formatTime(item.productionTime)
                }}</span>
              </div>
            </div>
          </div>
          <!-- 订单信息区域 -->
          <div class="mobile-card-section">
            <div class="section-title">
              <Icon icon="ep:document" class="section-icon" />
              <span>订单信息</span>
            </div>
            <div class="info-grid">
              <div class="info-item full-width">
                <span class="info-label">生产工单号</span>
                <span class="info-value">{{ item.productionOrderCode || '暂无' }}</span>
              </div>
              <div class="info-item full-width">
                <span class="info-label">销售订单号</span>
                <span class="info-value">{{ item.salesOrderCode }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">品号</span>
                <span class="info-value">{{ item.productNo }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">品名</span>
                <span class="info-value">{{ item.modelsOrColor }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">单位</span>
                <span class="info-value">{{ item.units }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">工序</span>
                <span class="info-value"
                  ><dict-tag :type="DICT_TYPE.PRODUCTION_REPORT_TYPE" :value="item.type"
                /></span>
              </div>
            </div>
          </div>

          <!-- 生产数据区域 -->
          <div class="mobile-card-section">
            <div class="section-title">
              <Icon icon="ep:data-analysis" class="section-icon" />
              <span>生产数据</span>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">数量</span>
                <span class="info-value">{{ item.workOrderNum }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">人数</span>
                <span class="info-value">{{ item.number || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">生产数</span>
                <span class="info-value">{{ item.hoursReportNum }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">累计完成</span>
                <span class="info-value">{{ item.totalReportNum }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">实际工时</span>
                <span class="info-value">{{ item.actualWork }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">标准工时</span>
                <span class="info-value">{{ item.standardWork }}</span>
              </div>
            </div>
          </div>

          <!-- 状态信息区域 -->
          <div class="mobile-card-section">
            <div class="section-title">
              <Icon icon="ep:flag" class="section-icon" />
              <span>状态信息</span>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">试产</span>
                <el-tag
                  :type="item.isTrial === 0 ? 'primary' : 'danger'"
                  size="small"
                  style="width: 50px"
                >
                  {{ item.isTrial === 0 ? '否' : '是' }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">返工</span>
                <el-tag
                  :type="item.isRework === 0 ? 'primary' : 'danger'"
                  size="small"
                  style="width: 50px"
                >
                  {{ item.isRework === 0 ? '否' : '是' }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">审核状态</span>
                <el-tag
                  :type="item.audit === 0 ? 'danger' : 'success'"
                  size="small"
                  style="width: 50px"
                >
                  {{ item.audit === 0 ? '未审核' : '已审核' }}
                </el-tag>
              </div>
               <div class="info-item">
                <span class="info-label">异常工时</span>
                <span class="info-value">{{ item.abnormalWork ||0 }}</span>
              </div>
              <div class="info-item full-width" v-if="item.pmcRemark">
                <span class="info-label">PMC备注</span>
                <el-tooltip effect="dark" :content="item.pmcRemark" placement="top">
                  <span class="info-value text-truncate">{{ item.pmcRemark }}</span>
                </el-tooltip>
              </div>
            </div>
          </div>
          <!-- 操作按钮区域 -->
          <div class="mobile-card-actions">
            <template v-if="item.audit === 0">
              <el-button
                type="primary"
                plain
                @click="openForm('update', item.batchesId)"
                class="mobile-card-btn mobile-edit-btn"
                size="small"
                v-hasPermi="['production:day:update']"
              >
                <Icon icon="ep:edit" class="btn-icon" />
                <span>编辑</span>
              </el-button>
              <el-button
                type="danger"
                plain
                @click="handleDelete(item.id)"
                class="mobile-card-btn mobile-delete-btn"
                size="small"
                v-hasPermi="['production:day:delete']"
              >
                <Icon icon="ep:delete" class="btn-icon" />
                <span>删除</span>
              </el-button>
            </template>
            <template v-if="item.audit === 1">
              <el-button
                type="primary"
                plain
                @click="openForm('update', item.batchesId)"
                class="mobile-card-btn mobile-edit-btn"
                v-hasPermi="['production:day:auditupdate']"
              >
                <Icon icon="ep:edit" class="btn-icon" />
                <span>编辑</span>
              </el-button>
              <el-button
                type="danger"
                plain
                @click="handleDelete(item.id)"
                class="mobile-card-btn mobile-delete-btn"
                v-hasPermi="['production:day:auditdelete']"
              >
                <Icon icon="ep:delete" class="btn-icon" />
                <span>删除</span>
              </el-button>
            </template>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else class="table-view-wrapper">
        <div class="table-scroll">
          <vxe-table
            @cell-dblclick="cellClickEvent"
            ref="tableRef"
            :row-config="{ height: 30, isCurrent: true }"
            :data="list"
            :header-cell-style="{ padding: 0 }"
            :cell-style="{ padding: 0, height: '30px', color: '#232323' }"
            :virtual-y-config="{ enabled: true, gt: 0 }"
            border
            stripe
            show-overflow
            align="center"
            height="85%"
          >
            <vxe-column field="teamLeader" width="80" title="班组长" />
            <vxe-column field="productionLine" width="100" title="产线">
              <template #header>
                <div>产线</div>
                <el-select
                  v-model="queryParams.productionLine"
                  @change="handleList"
                  placeholder="选择产线"
                  style="width: 100%"
                  size="small"
                  clearable
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
              <template #default="{ row }">
                {{ formattedProductionLine(row.productionLine) }}
              </template>
            </vxe-column>
            <vxe-column field="dateStr" width="100" title="日期">
              <template #header>
                <div>日期</div>
                <el-date-picker
                  v-model="queryParams.dateStr"
                  @change="handleList"
                  type="date"
                  placeholder="开始日期"
                  size="small"
                  clearable
                  value-format="YYYY-MM-DD"
                  class="!w-100%"
                />
              </template>
              <template #default="{ row }">
                {{ row.dateStr && formatToDate(row.dateStr) }}
              </template>
            </vxe-column>
            <vxe-column field="productionTime" width="100" title="时间段" />
            <vxe-column field="productionOrderCode" width="150" title="生产工单号" />
            <vxe-column field="salesOrderCode" width="150" title="销售订单号" />
            <vxe-column field="productNo" width="100" title="品号" />
            <vxe-column field="modelsOrColor" width="120" title="机型/颜色" />
            <vxe-column field="workOrderNum" width="80" title="工单数量" />
            <vxe-column field="units" width="80" title="单位" />
            <vxe-column field="number" width="80" title="人数" />
            <vxe-column field="type" width="80" title="工序">
              <template #default="{ row }">
                <dict-tag :type="DICT_TYPE.PRODUCTION_REPORT_TYPE" :value="row.type" />
              </template>
            </vxe-column>
            <vxe-column field="hoursReportNum" width="80" title="小时完成" />
            <vxe-column field="totalReportNum" width="80" title="累计完成" />
            <vxe-column field="avgWork" width="80" title="平均工时" />
            <vxe-column field="actualWork" width="80" title="实际工时" />
            <vxe-column field="standardWork" width="80" title="标准工时" />
            <!-- <vxe-column field="remark" width="180" title="备注" /> -->
            <vxe-column title="异常人数" width="120" field="abnormalNum" />
            <vxe-column title="异常工时" width="120" field="abnormalWork" />
            <vxe-column title="异常问题点" width="120" field="abnormalRemark" />
            <vxe-column title="异常对策" width="120" field="abnormalCountermeasures" />
            <vxe-column title="操作" width="140">
              <template #default="{ row }">
                <div v-if="row.audit === 0">
                  <el-button
                    @click="openForm('update', row.batchesId)"
                    link
                    v-hasPermi="['production:day:update']"
                    >编辑</el-button
                  >
                  <el-button
                    @click="handleDelete(row.id)"
                    link
                    type="danger"
                    v-hasPermi="['production:day:delete']"
                    >删除</el-button
                  >
                </div>
                <div v-if="row.audit === 1">
                  <el-button
                    @click="openForm('update', row.batchesId)"
                    link
                    v-hasPermi="['production:day:auditupdate']"
                    >编辑</el-button
                  >
                  <el-button
                    @click="handleDelete(row.id)"
                    link
                    type="danger"
                    v-hasPermi="['production:day:auditdelete']"
                    >删除</el-button
                  >
                </div>
              </template>
            </vxe-column>
          </vxe-table>
          <div style="margin-top: 10px">
            <el-pagination
              v-model:current-page="queryParams.pageNo"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20]"
              :total="total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              small
            />
          </div>
        </div>
      </div>
      <div v-if="!isTableView" class="h-30px leading-30px border-t-#a8a8a8"
        >共计：{{ total }}条记录</div
      >
    </div>
    <!-- 批量删除确认对话框 -->
    <Dialog title="批量删除确认" v-model="batchDeleteVisible">
      <div>当前选中：{{ selectionData.length }} 条数据，确认要删除吗？</div>
      <template #footer>
        <el-button type="danger" @click="confirmBatchDelete()">确认删除</el-button>
        <el-button @click="batchDeleteVisible = false">取消</el-button>
      </template>
    </Dialog>
    <!-- 表单弹窗：添加/修改 -->
    <DayForm ref="formRef" @success="getList(true)" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { formatToDateTime, formatToDate } from '@/utils/dateUtil'
import { DayApi, DayVO } from '@/api/report/technology/production'
import DayForm from './DayForm.vue'
import { cloneDeep, last } from 'lodash-es'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import download from '@/utils/download'
import { useAppStore } from '@/store/modules/app'
import { Search } from '@element-plus/icons-vue'
import { checkPermission } from '@/store/modules/permission'
import type { VxeTablePropTypes } from 'vxe-table'
import { debounce } from 'lodash-es'
import { ref } from 'vue'
/** 生产日报 列表 */
defineOptions({ name: 'Day' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const appStore = useAppStore()

const tableRef = ref()
const toolbarRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref<DayVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

// 滚动位置管理
const TABLE_SCROLL_KEY = 'productionDayTableScrollPosition'
const savedScrollPosition = ref({ scrollTop: 0, scrollLeft: 0 })
const shouldRestoreScroll = ref(false)

// 更精确的滚动位置保存方法
const handleScroll = (params: any) => {
  const { scrollTop, scrollLeft } = params

  // 只有在有意义的滚动位置时才保存（避免保存重置后的0值）
  if (scrollLeft > 0 || scrollTop > 0) {
    // 添加防抖，避免频繁保存
    clearTimeout((window as any).scrollSaveTimer)
    ;(window as any).scrollSaveTimer = setTimeout(() => {
      savedScrollPosition.value = { scrollTop, scrollLeft }
      localStorage.setItem(TABLE_SCROLL_KEY, JSON.stringify({ scrollTop, scrollLeft }))
      console.log('保存滚动位置:', { scrollTop, scrollLeft })
    }, 100) // 100ms防抖
  }
}


// 恢复滚动位置的通用方法 - 改进版
const restoreScrollPosition = () => {
  if (!tableRef.value) return false

  const { scrollTop, scrollLeft } = savedScrollPosition.value
  if (scrollLeft <= 0 && scrollTop <= 0) return false

  console.log('尝试恢复滚动位置:', { scrollTop, scrollLeft })

  try {
    // 方法1: 使用 VXE Table 的 scrollTo 方法（主要方法）
    if (tableRef.value.scrollTo) {
      tableRef.value.scrollTo(scrollLeft, scrollTop)
      console.log('使用 VXE scrollTo 方法恢复')
      
      // 验证滚动位置是否正确设置
      nextTick(() => {
        const tableBody = tableRef.value.$el?.querySelector('.vxe-table--body-wrapper')
        if (tableBody) {
          console.log('实际滚动位置:', {
            scrollLeft: tableBody.scrollLeft,
            scrollTop: tableBody.scrollTop
          })
        }
      })
      return true
    }

    // 方法2: 直接操作 DOM 元素（备用方法）
    const tableBody = tableRef.value.$el?.querySelector('.vxe-table--body-wrapper')
    if (tableBody) {
      // 先确保滚动容器可以滚动
      tableBody.scrollLeft = scrollLeft
      tableBody.scrollTop = scrollTop
      console.log('使用 DOM 操作恢复滚动位置')
      return true
    }
  } catch (error) {
    console.error('恢复滚动位置失败:', error)
  }

  return false
}

// 添加一个方法来获取当前精确的滚动位置
const getCurrentScrollPosition = () => {
  const tableBody = tableRef.value.$el?.querySelector('.vxe-table--body-wrapper')
  if (tableBody) {
    return {
      scrollLeft: tableBody.scrollLeft,
      scrollTop: tableBody.scrollTop
    }
  }
  return { scrollLeft: 0, scrollTop: 0 }
}

// 添加一个方法来验证滚动位置是否正确恢复
const verifyScrollPosition = () => {
  const currentPos = getCurrentScrollPosition()
  const savedPos = savedScrollPosition.value
  
  const isCloseEnough = 
    Math.abs(currentPos.scrollLeft - savedPos.scrollLeft) < 5 &&
    Math.abs(currentPos.scrollTop - savedPos.scrollTop) < 5
    
  console.log('滚动位置验证:', {
    current: currentPos,
    saved: savedPos,
    isCloseEnough
  })
  
  return isCloseEnough
}

// 改进的恢复滚动位置方法
const handleRendered = () => {
  console.log(
    '表格渲染完成，shouldRestoreScroll:',
    shouldRestoreScroll.value,
    'savedPosition:',
    savedScrollPosition.value
  )

  // 只有在需要恢复滚动位置时才执行
  if (shouldRestoreScroll.value && tableRef.value) {
    const { scrollTop, scrollLeft } = savedScrollPosition.value

    // 只有在有有效滚动位置时才恢复
    if (scrollLeft > 0 || scrollTop > 0) {
      console.log('开始恢复滚动位置:', { scrollTop, scrollLeft })

      // 使用多重延迟和多种方法尝试恢复
      setTimeout(() => {
        if (restoreScrollPosition()) {
          console.log('第一次恢复尝试成功')
        }
      }, 50)

      // 再次尝试恢复（防止第一次失败）
      setTimeout(() => {
        if (restoreScrollPosition()) {
          console.log('第二次恢复尝试成功')
        }
      }, 150)

      // 第三次尝试（更长延迟，确保表格完全渲染）
      setTimeout(() => {
        if (restoreScrollPosition()) {
          console.log('第三次恢复尝试成功')
        }
        
        // 最后验证滚动位置
        const tableBody = tableRef.value.$el?.querySelector('.vxe-table--body-wrapper')
        if (tableBody) {
          console.log('最终滚动位置:', {
            scrollLeft: tableBody.scrollLeft,
            scrollTop: tableBody.scrollTop
          })
        }
      }, 300)
    }

    shouldRestoreScroll.value = false // 恢复后重置标志
  }
}

// 初始化滚动位置（从localStorage恢复）
const initScrollPosition = () => {
  try {
    const saved = localStorage.getItem(TABLE_SCROLL_KEY)
    if (saved) {
      const position = JSON.parse(saved)
      savedScrollPosition.value = position
      console.log('从localStorage恢复滚动位置:', position)
    }
  } catch (error) {
    console.warn('恢复滚动位置失败:', error)
  }
}
// 移动端相关
const mobile = computed(() => appStore.getMobile)
const disabled = ref(false)

// 处理移动端屏幕旋转问题
const handleOrientationChange = () => {
  if (mobile.value) {
    // 延迟处理，确保旋转完成
    setTimeout(() => {
      // 重新设置viewport以修复缩放问题
      const viewport = document.querySelector('meta[name="viewport"]')
      if (viewport) {
        const content = viewport.getAttribute('content')
        viewport.setAttribute(
          'content',
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        )
        // 立即恢复原始设置
        setTimeout(() => {
          viewport.setAttribute('content', content || 'width=device-width, initial-scale=1.0')
        }, 100)
      }

      // 触发resize事件重新计算布局
      window.dispatchEvent(new Event('resize'))
    }, 300)
  }
}

// 批量删除相关
const batchDeleteVisible = ref(false)
const selectionData = ref<any[]>([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  workType: 0,
  // 支持查询的字段
  teamLeader: undefined,
  productionOrderCode: undefined,
  salesOrderCode: undefined,
  productNo: undefined,
  dateStr: '',
  startDate: undefined, // 新增：开始日期
  endDate: undefined, // 新增：结束日期
  type: undefined,
  productionLine: undefined,
  number: undefined,
  isRework: undefined,
  // 移动端全域搜索
  all: undefined
})
const queryFormRef = ref() // 搜索的表单
const menuConfig = reactive<any>({
  body: {
    options: [[{ code: 'batchDelete', name: '批量删除' }]]
  }
})

const productionLineLabels = {
  1: 'L1',
  2: 'L2',
  3: 'L3',
  4: 'L4',
  5: 'L5',
  6: 'L6',
  7: 'L7',
  8: 'L8',
  9: 'L9',
  10: 'L10',
  11: 'L11',
  12: '小包装线',
  13: '包装1线',
  14: '包装2线',
  15: 'L15'
}
// 计算属性：将数字映射为中文或 Lx 格式
const formattedProductionLine = computed(() => {
  return (value: number) => productionLineLabels[value] || value
})

const isReworkLabels = {
  1: '是',
  0: '否'
}
const formattedIsRework = computed(() => {
  return (value: number) => isReworkLabels[value] || value
})
// 右键菜单点击事件
const menuClickEvent = ({ menu }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'batchDelete':
      const rows = $table.getCheckboxRecords()
      if (rows.length === 0) {
        message.alertError('请选择要删除的数据')
        selectionData.value = []
        return
      }
      selectionData.value = rows
      batchDeleteVisible.value = true
      break
  }
}

//实际工时
const totalActualWork = ref()

//生产数
const totalWorkNum = ref(0)

const footerData = ref<VxeTablePropTypes.FooterData>([
  { productionLine: '合计总和', actualWork: totalActualWork, hoursReportNum: totalWorkNum }
])
//双击编辑
const cellClickEvent: any = ({ row, column }) => {
  if (column.title === '操作') return
  // 判断是否是已审核的数据
  if (row.audit === 1) {
    if (!checkPermission(['production:day:auditupdate'])) {
      message.alertError('您没有权限操作已审核数据')
      return
    }
  }
  openForm('update', row.batchesId)
}

// 筛选处理
const handleList = () => {
  queryParams.pageNo = 1
  // 筛选时保持滚动位置
  getList(true)
}

// 移动端无限滚动加载
const load = async () => {
  loading.value = true
  try {
    const maxSize = Math.ceil(total.value / queryParams.pageSize)
    if (queryParams.pageNo >= maxSize) {
      disabled.value = true
      return
    }
    queryParams.pageNo += 1
    let query = cloneDeep(queryParams)
    // 处理查询参数
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const data = await DayApi.getDayPage(query)
    list.value = list.value.concat(data.list)
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 时间格式化（用于显示具体时间点）
const formatTime = (timeValue: any) => {
  if (!timeValue) return ''
  // 如果是时间戳，转换为时间格式
  if (typeof timeValue === 'number') {
    const date = new Date(timeValue)
    return `${date.getHours().toString().padStart(2, '0')}:${date
      .getMinutes()
      .toString()
      .padStart(2, '0')}`
  }
  // 如果已经是时间格式，直接返回
  return timeValue
}

/** 查询列表 */
const getList = async (preserveScroll = false) => {
  loading.value = true

  // 如果需要保持滚动位置，先记录当前位置
  let currentPosition = null
  if (preserveScroll && tableRef.value) {
    currentPosition = getCurrentScrollPosition()
    shouldRestoreScroll.value = true
    console.log('记录当前滚动位置:', currentPosition)
  }

  try {
    let query = cloneDeep(queryParams)
    // 处理查询参数
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const data = await DayApi.getDayPage(query)
    list.value = data.list
    total.value = data.total
    totalActualWork.value = list.value
      .reduce((total, item) => total + item.actualWork, 0)
      .toFixed(3)
    totalWorkNum.value = list.value.reduce((total, item) => total + item.hoursReportNum, 0)

    // 如果需要恢复滚动位置
    if (preserveScroll && currentPosition) {
      // 使用保存的位置而不是可能已过期的位置
      savedScrollPosition.value = currentPosition
      nextTick(() => {
        setTimeout(() => {
          console.log('使用记录的位置恢复滚动:', currentPosition)
          if (restoreScrollPosition()) {
            console.log('数据加载完成后恢复成功')
          }
        }, 100)
      })
    }
  } finally {
    loading.value = false
  }
}

// 添加窗口大小变化监听，重新计算列宽
const handleResize = debounce(() => {
  // 清除保存的滚动位置，因为列宽可能已变化
  if (tableRef.value) {
    const tableBody = tableRef.value.$el?.querySelector('.vxe-table--body-wrapper')
    if (tableBody) {
      // 保存当前相对位置而不是绝对位置
      const relativeScrollLeft = tableBody.scrollLeft
      const relativeScrollTop = tableBody.scrollTop
      
      savedScrollPosition.value = { 
        scrollTop: relativeScrollTop, 
        scrollLeft: relativeScrollLeft 
      }
    }
  }
}, 300)

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const disabledDate = (time: Date) => {
  if (!queryParams.startDate) return false
  return time.getTime() < new Date(queryParams.startDate).setHours(0, 0, 0, 0)
}
// 批量删除确认
const confirmBatchDelete = async () => {
  loading.value = true
  try {
    let isAudit = true
    selectionData.value.map((item) => {
      if (item.audit === 1) {
        //判断是否有操作已审核数据的权限
        if (!checkPermission(['production:day:auditdelete'])) {
          isAudit = false
          return
        }
      }
    })
    if (isAudit) {
      const ids = selectionData.value.map((item) => item.id)
      await DayApi.deleteDays(ids)
      message.success('批量删除成功')
      batchDeleteVisible.value = false
      selectionData.value = []
    } else {
      message.error('选中的数据中包含已审核数据，请重新选择！')
    }
    // 删除后保持滚动位置
    getList(true)
  } catch {
    message.error('批量删除失败')
  } finally {
    loading.value = false
  }
}

const cellStyleMethod = ({ row, column }) => {
  //只处理工时差列
  if (column.field === 'workHourDiff') {
    if (row.workHourDiff > 1) {
      return {
        color: 'red'
      }
    }
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, batchesId?: any) => {
  formRef.value.open(type, batchesId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DayApi.deleteDay(id)
    message.success(t('common.delSuccess'))
    // 刷新列表并保持滚动位置
    await getList(true)
  } catch {}
}

/** 审核按钮操作 */
const handleAudit = async () => {
  const $table = tableRef.value
  if (!$table) return
  const selectRecords = $table.getCheckboxRecords()
  const selectReserveRecords = $table.getCheckboxReserveRecords()

  // 合并两个数组，并根据 batchesId 去重
  const mergedMap = new Map()

  ;[...selectRecords, ...selectReserveRecords].forEach((row) => {
    mergedMap.set(row.batchesId, row) // 使用 batchesId 作为 key 去重
  })
  const uniqueBatchesIds = Array.from(mergedMap.keys()) // 只取 batchesId
  if (uniqueBatchesIds.length === 0) {
    message.alertError('请选择要审核的数据')
    selectionData.value = []
    return
  }
  const total = selectRecords.length + selectReserveRecords.length
  // 审核的二次确认
  message.confirm('当前已经选中' + total + '条数据，确定要审核通过吗？').then(async () => {
    await DayApi.auditDay(uniqueBatchesIds)
    message.success('审核成功')
    // 刷新列表并保持滚动位置
    await getList(true)
  })
}

const handleAuditItem = async () => {
  if (selectedItems.value.length === 0) {
    message.warning('请选择要审核的行')
    return
  }
  const uniqueBatchesIds = selectedItems.value.map((item) => item.batchesId).filter((item) => item)
  // 审核的二次确认
  message
    .confirm('当前已经选中' + uniqueBatchesIds.length + '批数据，确定要审核通过吗？')
    .then(async () => {
      await DayApi.auditDay(uniqueBatchesIds)
      message.success('审核成功')
      // 刷新列表并保持滚动位置
      await getList(true)
      selectedItems.value = []
    })
}

// 视图模式控制
const isTableView = ref(false)

const viewSwitching = ref(false)

// 切换视图模式
const toggleViewMode = async () => {
  // 立即显示loading状态
  viewSwitching.value = true

  // 使用nextTick确保loading状态先显示
  await nextTick()

  // 延迟一点时间让用户看到loading效果
  await new Promise((resolve) => setTimeout(resolve, 100))

  isTableView.value = !isTableView.value
  queryParams.pageNo = 1

  try {
    await getList()
  } finally {
    // 确保loading状态至少显示300ms，提供更好的视觉反馈
    setTimeout(() => {
      viewSwitching.value = false
    }, 200)
  }
}

const exportLoading = ref(false) // 导出的加载中
const currentDate = formatToDate(new Date())
/** 导出日报或异常工时按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DayApi.exportDay(queryParams)
    download.excel(
      data,
      queryParams.workType === 0
        ? `生产日报+临时工出勤_${currentDate}.xls`
        : `异常日报_${currentDate}.xls`
    )
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleAttendance = async () => {
  try {
    // 导出的二次确认
    const $table = tableRef.value
    const rows = $table.getCheckboxRecords()
    if (rows.length === 0) {
      message.alertError('请选择要导出的数据')
      selectionData.value = []
      return
    }
    const uniqueBatchesIds = Array.from(new Set(rows.map((item) => item.batchesId)))
    console.log(uniqueBatchesIds)
    await message.exportConfirm('确认要导出出勤工时吗？')
    // 发起导出
    exportLoading.value = true
    const data = await DayApi.exportAttendance(uniqueBatchesIds)
    download.excel(data, `出勤工时_${currentDate}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const handleType = async () => {
  queryParams.workType = queryParams.workType === 0 ? 1 : 0
  getList()
}

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  queryParams.pageNo = 1
  // 改变每页条数时不保持滚动位置（因为数据结构变化）
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  // 翻页时不保持滚动位置（用户主动翻页）
  getList()
}

// 监听表格数据变化，在数据更新后恢复滚动位置
watch(
  () => list.value,
  (newList) => {
    if (shouldRestoreScroll.value && newList && newList.length > 0) {
      console.log('监听到数据变化，准备恢复滚动位置')
      nextTick(() => {
        setTimeout(() => {
          if (savedScrollPosition.value.scrollLeft > 0 || savedScrollPosition.value.scrollTop > 0) {
            const { scrollTop, scrollLeft } = savedScrollPosition.value
            console.log('通过数据监听恢复滚动位置:', { scrollTop, scrollLeft })
            if (restoreScrollPosition()) {
              console.log('数据监听恢复成功')
              shouldRestoreScroll.value = false
            }
          }
        }, 150)
      })
    }
  },
  { deep: false }
)

// 在组件的data部分添加
const selectedItems = ref<any[]>([]) // 存储选中的项

const handleCheckboxChange = (checked: boolean, item: any) => {
  if (checked) {
    // 添加选中项
    if (!selectedItems.value.some((selected) => selected.batchesId === item.batchesId)) {
      selectedItems.value.push(item)
    }
  } else {
    // 移除取消选中的项
    selectedItems.value = selectedItems.value.filter(
      (selected) => selected.batchesId !== item.batchesId
    )
  }
}

/** 初始化 **/
onMounted(() => {
  // 初始化滚动位置
  initScrollPosition()

  getList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })

  // 只在移动端监听屏幕旋转
  if (mobile.value) {
    window.addEventListener('orientationchange', handleOrientationChange)
  }

    // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  if (mobile.value) {
    window.removeEventListener('orientationchange', handleOrientationChange)
  }

  window.removeEventListener('resize', handleResize)

    // 清理防抖定时器
  if ((window as any).scrollSaveTimer) {
    clearTimeout((window as any).scrollSaveTimer)
  }
})
</script>

<style lang="scss" scoped>
// 移动端屏幕旋转优化 - 仅针对此页面
@media screen and (max-width: 768px) {
  .mobile-view {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    // 修复iOS Safari的viewport问题
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    padding: 0 8px;
  }

  .h-\[calc\(100vh-110px\)\] {
    height: calc(100vh - 110px) !important;
    min-height: calc(100vh - 110px) !important;

    // iOS Safari兼容
    @supports (-webkit-touch-callout: none) {
      height: calc(-webkit-fill-available - 110px) !important;
    }
  }

  // 移动端搜索栏样式
  .mobile-search-bar {
    margin-bottom: 12px;

    .mobile-search-input {
      .el-input__wrapper {
        border-radius: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e4e7ed;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.is-focus {
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          border-color: #409eff;
        }
      }

      .el-input__inner {
        height: 44px;
        line-height: 44px;
        font-size: 16px;
      }
    }
  }

  // 移动端操作按钮栏样式
  .mobile-action-bar {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    padding: 0 4px;

    .mobile-action-btn {
      flex: 1;
      height: 33px;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .btn-icon {
        font-size: 16px;
      }

      .btn-text {
        font-size: 13px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .mobile-add-btn {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #337ecc 0%, #5aa3e6 100%);
      }
    }

    .mobile-audit-btn {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #529b2e 0%, #6bb042 100%);
      }
    }

    .mobile-view-btn {
      background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #73767a 0%, #9a9ca1 100%);
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #e6a23c 0%, #f0b659 100%);

        &:hover {
          background: linear-gradient(135deg, #cf9236 0%, #d9a441 100%);
        }
      }
    }
  }

  // 移动端卡片容器
  .mobile-card-container {
    height: calc(100% - 125px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 4px;
  }

  // 移动端数据卡片样式
  .mobile-data-card {
    background: white;
    border-radius: 12px;
    margin-bottom: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:last-child {
      margin-bottom: 80px; // 为底部留出空间
    }
  }

  // 移动端卡片头部
  .mobile-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;

    .card-title-main {
      margin-top: -8px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;

      .title-icon {
        color: #409eff;
        font-size: 18px;
      }
    }

    .card-date {
      margin-top: -8px;
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      color: #606266;

      .date-icon {
        color: #67c23a;
        font-size: 14px;
      }
    }
  }

  // 移动端卡片区域
  .mobile-card-section {
    padding: 6px;
    border-bottom: 1px solid #f5f5f5;

    &:last-of-type {
      border-bottom: none;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 15px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 3px;
      .section-icon {
        color: #409eff;
        font-size: 16px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;

      .info-item {
        display: flex;
        flex-direction: column;
        gap: 2px;
        &.full-width {
          grid-column: 1 / -1;
        }

        .info-label {
          font-size: 12px;
          color: #909399;
          font-weight: 500;
        }

        .info-value {
          font-size: 14px;
          color: #303133;
          font-weight: 500;

          &.text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 移动端卡片操作按钮
  .mobile-card-actions {
    padding: 8px 12px;
    background: #fafafa;
    display: flex;
    gap: 8px;

    .mobile-card-btn {
      flex: 1;
      height: 33px;
      border-radius: 10px;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      transition: all 0.3s ease;

      .btn-icon {
        font-size: 14px;
      }

      &:hover {
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .mobile-edit-btn {
      border-color: #409eff;
      color: #409eff;

      &:hover {
        background: #409eff;
        color: white;
      }
    }

    .mobile-delete-btn {
      border-color: #f56c6c;
      color: #f56c6c;

      &:hover {
        background: #f56c6c;
        color: white;
      }
    }
  }
}

:deep(.vxe-header--column .vxe-cell) {
  padding: 0 !important;
}

:deep(.row--stripe) {
  background-color: #f9f9f9 !important;
}

:deep(.vxe-cell--edit-icon) {
  display: none !important;
}

@media (max-width: 768px) {
  :deep(.el-card__body) {
    padding: 0 !important;
  }

  .data-item {
    padding-right: 30px;
    // 弱化阴影参数
    box-shadow: 0 2px 6px rgba(0, 85, 255, 0.1); // 更淡的蓝紫色阴影
    transition: box-shadow 0.2s ease; // 缩短过渡时间

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      padding: 8px 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
      text-align: center;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 5px;
  }

  :deep(.el-form-item__label) {
    color: #a8a8a8 !important;
  }

  :deep(.el-form-item__content) {
    display: block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.table-view-wrapper {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  min-height: 0;
  background: #fff;
  position: relative; // 关键
}

.table-scroll {
  flex: 1 1 0%;
  min-height: 0;
  overflow-y: auto;
  overflow-x: auto;
}

.table-total {
  color: #888;
  font-size: 14px;
}

:deep(.vxe-table--render-default .vxe-body--row.row--current) {
  background-color: #e6f7ff !important;
}
</style>
