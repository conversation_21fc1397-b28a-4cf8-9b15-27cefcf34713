<template>
  <el-dialog
    :model-value="show"
    title="继续认领"
    width="40%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="可认领金额">
            <el-input :value="props.maxAmount"  readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 订单明细 -->
      <div>
        <el-divider class="p-2px">订单明细</el-divider>
        <div class="mt--8px">
          <el-button type="primary" plain @click="addOrder" size="small">+ 添加订单</el-button>
        </div>
        <el-table
          :data="orders"
          border
          :max-height="150"
          height="150"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column label="订单号" min-width="200">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.orderNo"
                readonly
                @click="openOrderDialog($index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="订单金额" min-width="100">
            <template #default="{ row }">
              <span>{{ row.orderAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余认款余额" min-width="100">
            <template #default="{ row }">
              <span>{{ row.remainingAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="认款金额" min-width="100">
            <template #default="{ row }">
              <el-input
                v-model="row.amount"
                type="number"
                :step="1"
                :min="0"
                :max="row.orderAmount"
                controls-position="right"
                style="width: 100%"
                @change="handleAmountChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeOrder($index)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 费用类别 -->
        <el-divider class="p-2px">费用类别</el-divider>
        <div class="mt--8px">
          <el-button type="primary" plain @click="addExpense" size="small">+ 添加费用</el-button>
        </div>
        <el-table
          :data="expenses"
          border
          :max-height="150"
          height="150"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column label="费用类别" min-width="40">
            <template #default="{ row }">
              <el-select v-model="row.expenseType" placeholder="请选择费用类别" style="width: 100%">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="金额" min-width="120">
            <template #default="{ row }">
              <el-input
                v-model="row.amount"
                :precision="2"
                type="number"
                :min="0"
                :step="1"
                controls-position="right"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeExpense($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting"> 确认提交 </el-button>
    </template>
  </el-dialog>

  <!-- 订单选择对话框 -->
  <el-dialog v-model="orderDialogVisible" title="选择订单" width="40%" append-to-body>
    <div class="mb-15px">
      <el-input
        v-model="orderKeyword"
        placeholder="输入订单号检索"
        clearable
        @input="fetchOrderData"
      >
        <template #append>
          <el-button @click="fetchOrderData">搜索</el-button>
        </template>
      </el-input>
    </div>
    <el-table
      :data="orderList"
      v-loading="orderLoading"
      height="300"
      highlight-current-row
      @current-change="handleOrderSelect"
    >
      <el-table-column prop="DocNo" label="订单号" width="150" />
      <el-table-column prop="currency" label="币种" width="60" />
      <el-table-column prop="salesPrice" label="订单总金额" width="110" />
      <el-table-column prop="shipPrice" label="已出货金额" width="110" />
      <el-table-column prop="claimedAmount" label="已认领金额" width="110" />
      <el-table-column prop="remainingAmount" label="剩余认款余额" width="110" />
    </el-table>
    <template #footer>
      <el-button @click="orderDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmOrder">确认</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const message = useMessage() // 消息弹窗

const props = defineProps<{
  show: boolean
  maxAmount: number
  id: number
  customerCode: string
  claimDetail: any
}>()

const emit = defineEmits(['update:show', 'success'])

// 基本数据
const formRef = ref()
const formLoading = ref(false)
const submitting = ref(false)

const currentOrderIndex = ref(-1)

// 订单和费用数据
const orders = ref<any[]>([])
const expenses = ref<any[]>([])

// 订单相关
const orderDialogVisible = ref(false)
const orderKeyword = ref('')
const orderLoading = ref(false)
const orderList = ref<any[]>([])
const selectedOrder = ref<any>(null)

// 添加订单
const addOrder = () => {
  orders.value.push({
    type: 1,
    orderNo: '',
    orderAmount: 0,
    amount: 0
  })
}

// 删除订单
const removeOrder = (index: number) => {
  orders.value.splice(index, 1)
}

// 打开订单选择对话框
const openOrderDialog = (index: number) => {
  currentOrderIndex.value = index
  orderKeyword.value = ''
  orderDialogVisible.value = true
  fetchOrderData()
}

// 获取订单数据
const fetchOrderData = async () => {
  try {
    orderLoading.value = true
    const res: any = await ClaimApi.getOrders({
      code: props.customerCode,
      DocNo: orderKeyword.value
    })
    orderList.value = res || []
  } catch (err) {
    console.error('获取订单数据失败:', err)
    message.error('获取订单数据失败')
  } finally {
    orderLoading.value = false
  }
}

// 处理订单选择
const handleOrderSelect = (row: any) => {
  selectedOrder.value = row
}

// 确认订单选择
const confirmOrder = () => {
  if (!selectedOrder.value || currentOrderIndex.value < 0) {
    message.error('请选择订单')
    return
  }

  const row = orders.value[currentOrderIndex.value]
  row.orderNo = selectedOrder.value.DocNo
  row.orderAmount = selectedOrder.value.salesPrice
  row.remainingAmount = selectedOrder.value.remainingAmount
  row.amount = selectedOrder.value.remainingAmount
  orderDialogVisible.value = false
}

// 添加费用
const addExpense = () => {
  expenses.value.push({
    type: 2,
    expenseType: '',
    amount: 0
  })
}

// 删除费用
const removeExpense = (index: number) => {
  expenses.value.splice(index, 1)
}

// 处理认款金额变化
const handleAmountChange = (row: any) => {
  if (row.amount > row.remainingAmount) {
    row.amount = row.remainingAmount
    message.warning('认款金额不能超过剩余可认款金额')
  }
}

// 计算当前总金额
const currentTotalAmount = computed(() => {
  const orderAmount = orders.value.reduce((sum, order) => sum + Number(order.amount || 0), 0)
  const expenseAmount = expenses.value.reduce((sum, expense) => sum + Number(expense.amount || 0), 0)
  return orderAmount + expenseAmount
})

// 提交
const handleSubmit = async () => {
  if (submitting.value) return

  // 校验总金额是否等于可认领金额
  const total = currentTotalAmount.value
  if (Math.abs(total - props.maxAmount) > 0.01) {
    message.error(`订单金额与费用金额之和必须等于可认领金额 ${props.maxAmount}`)
    return
  }

  // 校验必填项
  for (let i = 0; i < orders.value.length; i++) {
    const order = orders.value[i]
    if (!order.orderNo) {
      message.error(`第${i + 1}条订单明细的订单号不能为空`)
      return
    }
    if (order.amount <= 0) {
      message.error(`第${i + 1}条订单明细的认款金额必须大于0`)
      return
    }
  }

  for (let i = 0; i < expenses.value.length; i++) {
    console.log(expenses.value[i])
    const expense = expenses.value[i]
    if (expense.expenseType === '' || expense.expenseType === null || expense.expenseType === undefined) {
      message.error(`第${i + 1}条费用明细的费用类别不能为空`)
      return
    }
    if (expense.amount <= 0) {
      message.error(`第${i + 1}条费用明细的金额必须大于0`)
      return
    }
  }
  
  // 清理原有的 detailList，去除 id 和 claimId 字段
  const cleanedDetailList = (props.claimDetail?.detailList || [])
    .filter((item: any) => item.type != '3') // 排除 type 为 3 的项
    .map((item: any) => {
      // 创建新对象，排除 id 和 claimId 字段
      const { id, claimId, ...cleanedItem } = item
      return cleanedItem
    })

  const payload: any = {
    id: props.id || undefined,
    claimDate: props.claimDetail.claimDate,
    type: props.claimDetail.type,
    status: props.claimDetail.status, // 暂存状态
    salesmanName: props.claimDetail.salesmanName,
    customerName: props.claimDetail.customerName,
    customerCode: props.claimDetail.customerCode,
    currency: props.claimDetail.currency,
    currencyCode: props.claimDetail.currencyCode,
    totalAmount: props.claimDetail.totalAmount,
        detailList: [
      // 保留原有的detailList（排除type为3的项）
      ...cleanedDetailList,
      // 添加新的订单明细
      ...orders.value.map((o) => ({
        type: 1,
        orderNo: o.orderNo,
        orderAmount: o.orderAmount,
        amount: Number(o.amount || 0),
        remainingAmount: Number(o.remainingAmount || 0),
        shipAmount: Number(o.shipAmount || 0)
      })),
      // 添加新的费用明细
      ...expenses.value.map((e) => ({
        type: 2,
        expenseType: e.expenseType,
        amount: Number(e.amount || 0),
        remainingAmount: Number(e.remainingAmount || 0)
      }))
    ],
    collectionList: props.claimDetail.collectionList
  }

  try {
    submitting.value = true
    await ClaimApi.createClaim(payload)
    message.success('继续认领成功')
    emit('success')
    handleClose()
  } catch (err) {
    console.error('认领失败:', err)
    message.error('认领失败')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:show', false)
  // 重置表单数据
  resetForm()
}

// 重置表单
const resetForm = () => {
  orders.value = []
  expenses.value = []
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (newShow) => {
    if (!newShow) {
      resetForm()
    }
  }
)
</script>

<style lang="css" scoped>
:deep(.el-form-item) {
  margin-bottom: 5px;
}

:deep(.el-row) {
  margin-bottom: 0;
}
</style>