import request from '@/config/axios'

/** 获取合思用户列 */
export const getTableColumn = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/user/get-columns' })
}

/** 获取U9所有的业务员信息 */
export const getErpAllSeller = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/user/get-erp-all-seller' })
}

/** 获取U9所有的员工记录信息 */
export const getErpAllEmployee = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/user/get-erp-all-employee' })
}

/** 获取合思用户分页数据 */
export const page = async (params: any) => {
  return await request.get({ url: '/butt-joint/ekuaibao/user/page', params })
}

/** 更新用户 */
export const save = async (data:any)=>{
  return await request.post({url:'/butt-joint/ekuaibao/user/save',data})
}