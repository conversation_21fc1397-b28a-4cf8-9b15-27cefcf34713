<template>
  <div class="overflow-hidden">
    <van-tabs v-model:active="currentTab" @change="handleList">
      <van-tab title="余额明细" name="balance" />
      <van-tab title="收款单" name="rec" />
      <van-tab title="出货单" name="ship" />
      <van-tab title="退货单" name="rma" />
    </van-tabs>
    <van-cell title="查询方案" is-link @click="queryShow = true" class="mt-5px mb-5px" />
    <van-popup
      v-model:show="queryShow"
      position="bottom"
      :style="{ height: '60%', padding: '10px' }"
      :close-on-click-overlay="false"
    >
      <div class="flex justify-between">
        <van-button type="warning" size="small" @click="queryShow = false"> 取消 </van-button>
        <div>确定查询内容</div>
        <van-button type="primary" size="small" @click="handleList"> 确定 </van-button>
      </div>
      <div class="h-[calc(100%-52px)]">
        <van-form>
          <van-field label="组织">
            <template #input>
              <el-select v-model="queryParams.orgName" multiple>
                <el-option
                  v-for="(dict, index) in FilterTemplate.erpOrg"
                  :key="index"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </van-field>
          <van-cell
            title="日期"
            :value="businessDateText"
            placeholder="选择日期范围"
            @click="businessDateShow = true"
          />
          <van-calendar
            v-model:show="businessDateShow"
            type="range"
            switch-mode="year-month"
            :default-date="defaultBusinessDate"
            :min-date="moment().add(-2, 'years').toDate()"
            :max-date="moment().endOf('months').toDate()"
            @confirm="onBusinessDateConfirm"
          />
          <template v-if="['rec', 'ship', 'rma'].includes(currentTab)">
            <van-field v-model="queryParams.docNo" label="单号" placeholder="单号" />
            <van-field v-model="queryParams.srcDocNo" label="来源单号" placeholder="来源单号" />
            <van-field v-model="queryParams.customerCode" label="客户编码" placeholder="客户编码" />
          </template>
          <van-field v-model="queryParams.customerName" label="客户名称" placeholder="客户名称" />
          <van-field v-model="queryParams.sellerName" label="业务员" placeholder="业务员" />
          <van-field name="switch" label="统计">
            <template #input>
              <van-switch v-model="queryParams.hasStatistics" />
            </template>
          </van-field>
        </van-form>
      </div>
    </van-popup>
    <div class="h-[calc(100vh-300px)] overflow-auto">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多数据了"
        @load="load"
      >
        <van-cell v-for="(item, index) in list" :key="index" class="mb-5px">
          <el-form class="!w-100%" size="small" label-width="60">
            <el-row class="!w-100%">
              <el-col :span="24" v-if="['rec', 'ship', 'rma'].includes(currentTab)">
                <el-form-item label="单号" class="bg-[var(--el-color-primary-light-9)]">{{
                  item.docNo
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组织">{{ item.orgName }}</el-form-item>
              </el-col>
              <template v-if="['rec', 'ship', 'rma'].includes(currentTab)">
                <el-col :span="12">
                  <el-form-item label="业务日期">{{ item.businessDate }}</el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item label="来源单号">{{ item.srcDocNo }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客户编码">{{ item.customerCode }}</el-form-item>
                </el-col>
              </template>
              <el-col :span="24">
                <el-form-item label="客户名称">{{ item.customerName }}</el-form-item>
              </el-col>
              <template v-if="currentTab === 'rec'">
                <el-col :span="12">
                  <el-form-item label="用途">{{ item.property }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="币种">{{ item.currency }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态">{{ item.status }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="金额">{{ item.money }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="本币">{{ item.domesticMoney }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="汇率">{{ item.exchangeRate }}</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="收款账号">{{ item.bankAccount }}</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注">{{ item.remark }}</el-form-item>
                </el-col>
              </template>
              <template v-else-if="['ship', 'rma'].includes(currentTab)">
                <el-col :span="12">
                  <el-form-item label="品号">{{ item.itemCode }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="品名">{{ item.itemName }}</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="规格">{{ item.spec }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="数量">{{ item.qty }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="单位">{{ item.unit }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="单价">{{ item.price }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="金额">{{ item.money }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态">{{ item.status }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="本币">{{ item.domesticMoney }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="汇率">{{ item.exchangeRate }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="税组合">{{ item.taxName }}</el-form-item>
                </el-col>
              </template>

              <template v-else-if="['balance'].includes(currentTab)">
                <el-col :span="12">
                  <el-form-item label="业务员">{{ item.sellerName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="收款金额">{{ item.recpaymentMoney }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="出货金额">{{ item.shipMoney }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="退货金额">{{ item.rmaMoney }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="余额">{{ item.totalMoney }}</el-form-item>
                </el-col>
              </template>
            </el-row>
          </el-form>
        </van-cell>
      </van-list>
    </div>
    <van-grid class="bg-white mt-5px" :column-num="5">
      <van-grid-item>
        <template #icon> 总数 </template>
        <template #text> {{ total }}条 </template>
      </van-grid-item>
      <template v-if="queryParams.hasStatistics">
        <template v-if="['rec', 'ship', 'rma'].includes(currentTab)">
          <van-grid-item>
            <template #icon> 金额 </template>
            <template #text> {{ statistics?.money }} </template>
          </van-grid-item>
          <van-grid-item>
            <template #icon> 本币 </template>
            <template #text> {{ statistics?.domesticMoney }} </template>
          </van-grid-item>
        </template>
        <template v-else>
          <van-grid-item>
            <template #icon> 收款金额 </template>
            <template #text> {{ statistics?.recpaymentMoney }} </template>
          </van-grid-item>
          <van-grid-item>
            <template #icon> 出货金额 </template>
            <template #text> {{ statistics?.shipMoney }} </template>
          </van-grid-item>
          <van-grid-item>
            <template #icon> 退货金额 </template>
            <template #text> {{ statistics?.rmaMoney }} </template>
          </van-grid-item>
          <van-grid-item>
            <template #icon> 余额 </template>
            <template #text> {{ statistics?.totalMoney }} </template>
          </van-grid-item>
        </template>
      </template>
    </van-grid>
  </div>
</template>

<script lang="ts" setup>
import { RecPaymentApi } from '@/api/sales/receiving-payment'
import * as FilterTemplate from '@/utils/Filter'
import moment from 'moment'

const currentTab = ref('balance')

const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 10,
  hasStatistics: true
})

const queryShow = ref(false)
const total = ref(0)
const statistics = ref<any>({})
const list = ref<any[]>([])
const loading = ref(false)
const finished = ref(false)

const businessDateShow = ref(false)
const businessDateText = ref('')
const defaultBusinessDate = ref<Date[]>([])

const onBusinessDateConfirm = (values) => {
  const [start, end] = values
  businessDateShow.value = false
  const startDate = moment(start).format('YYYY-MM-DD')
  const endDate = moment(end).format('YYYY-MM-DD')
  businessDateText.value = startDate + '至' + endDate
  queryParams.value['businessDate'] = [startDate, endDate]
}
const onList = async () => {
  loading.value = true
  try {
    if (currentTab.value === 'rec') {
      const res = await RecPaymentApi.getRecPaymentPage(queryParams.value)
      total.value = res.total
      list.value = list.value.concat(res.list)
      statistics.value = res.statistics
    } else if (currentTab.value === 'ship') {
      const res = await RecPaymentApi.getShipPage(queryParams.value)
      total.value = res.total
      list.value = list.value.concat(res.list)
      statistics.value = res.statistics
    } else if (currentTab.value === 'rma') {
      const res = await RecPaymentApi.getRmaPage(queryParams.value)
      total.value = res.total
      list.value = list.value.concat(res.list)
      statistics.value = res.statistics
    } else if (currentTab.value === 'balance') {
      const res = await RecPaymentApi.getTotalPage(queryParams.value)
      total.value = res.total
      list.value = list.value.concat(res.list)
      statistics.value = res.statistics
    }
  } finally {
    loading.value = false
  }
}

const load = () => {
  queryParams.value.pageNo++
  if (queryParams.value.pageNo > Math.ceil(total.value / queryParams.value.pageSize)) {
    finished.value = true
    return
  }
  onList()
}

const handleList = () => {
  queryParams.value.pageNo = 1
  list.value = []
  onList()
  queryShow.value = false
  finished.value = false
}

onMounted(() => {
  const startDate = moment().startOf('years')
  const endDate = moment()
  queryParams.value.businessDate = [startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')]
  defaultBusinessDate.value = [startDate.toDate(), endDate.toDate()]
  businessDateText.value = startDate.format('YYYY-MM-DD') + '至' + endDate.format('YYYY-MM-DD')
  onList()
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0px !important;

  .el-form-item__content {
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

:deep(.van-grid-item) {
  font-size: 12px;
}

:deep(.el-select__wrapper) {
  box-shadow: none;
}
</style>
