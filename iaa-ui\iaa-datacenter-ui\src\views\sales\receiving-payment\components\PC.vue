<template>
  <ContentWrap>
    <div class="h-[calc(100vh-135px)] overflow-auto">
      <vxe-toolbar custom ref="toolbarRef" size="mini">
        <template #buttons>
          <span class="text-#000">单据类型：</span>
          <el-radio-group v-model="currentTable" @change="onTableChange" size="small">
            <el-radio-button label="余额明细" value="balance" />
            <el-radio-button label="收款单" value="rec" />
            <el-radio-button label="出货单" value="ship" />
            <el-radio-button label="退货单" value="rma" />
          </el-radio-group>

          <span class="ml-30px text-#000">开启统计：</span>
          <el-switch v-model="queryParams.hasStatistics" @change="onList" size="small" />

          <template v-if="currentTable === 'balance'">
            <el-radio-group class="ml-30px" v-model="queryParams.dateType" size="small">
              <el-radio-button label="全部" value="all" />
              <el-radio-button label="时间范围" value="range" />
            </el-radio-group>

            <div v-if="queryParams.dateType === 'range'">
              <span class="ml-30px text-#000">业务日期：</span>
              <el-date-picker
                type="daterange"
                value-format="YYYY-MM-DD"
                v-model="queryParams.businessDate"
                class="!w-240px"
                :clearable="false"
                @change="handleList"
                size="small"
              />
            </div>
          </template>
          <div class="ml-30px">
            <el-button type="primary" @click="handleList" size="small">刷新</el-button>
          </div>
        </template>
        <template #tools>
          <el-button
            type="primary"
            link
            class="mr-10px"
            @click="permissionFormRef?.openForm()"
            v-hasPermi="['receiving:payment:permission']"
            size="small"
          >
            权限管理
          </el-button>
          <el-button
            circle
            class="mr-10px"
            v-hasPermi="['receiving:payment:download']"
            @click="handleExport"
            :loading="exportLoading"
            size="small"
          >
            <Icon icon="ep:download" />
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-100px)]">
        <vxe-table
          :custom-config="customConfig"
          :header-cell-config="{ height: 24 }"
          :cell-config="{ height: 24 }"
          :column-config="{ resizable: true, maxFixedSize: 0 }"
          :data="list"
          :loading="loading"
          :show-footer="queryParams.hasStatistics"
          :footer-data="[statistics]"
          :footer-cell-config="{ height: 30 }"
          :footer-cell-style="{
            background: 'var(--el-color-warning-light-9)',
            border: '1px solid #ebeef5'
          }"
          :filter-config="{ remote: true }"
          :sort-config="{ remote: true, multiple: true }"
          align="center"
          border
          show-overflow
          show-footer-overflow
          stripe
          height="100%"
          ref="tableRef"
          id="ReceivingPaymentTable"
          @filter-change="handleFilterChange"
          @sort-change="sortChangeEvent"
          size="mini"
        >
          <template v-if="currentTable === 'rec'">
            <vxe-column title="组织" field="orgName" width="80" :filters="FilterTemplate.erpOrg" />
            <vxe-column
              title="单号"
              field="docNo"
              width="140"
              :filters="fileterValue.docNo"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="业务日期"
              field="businessDate"
              width="100"
              :filters="fileterValue.businessDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
            />
            <vxe-column
              title="来源单据"
              field="srcDocNo"
              width="140"
              :filters="fileterValue.srcDocNo"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="客户编码"
              field="customerCode"
              width="100"
              :filters="fileterValue.customerCode"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="客户名称"
              field="customerName"
              min-width="200"
              align="left"
              :filters="fileterValue.customerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="用途"
              field="property"
              width="80"
              :filters="FilterTemplate.erpRecPaymentProperty"
            />
            <vxe-column
              title="币种"
              field="currency"
              width="80"
              :filters="FilterTemplate.erpCurrency"
            />
            <vxe-column title="状态" field="status" width="80" />
            <vxe-column
              title="备注"
              field="备注"
              min-width="200"
              :filters="fileterValue.remark"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="金额"
              field="money"
              width="100"
              sortable
              :filters="fileterValue.money"
              :filter-render="FilterTemplate.numberFilterRender"
            />
            <vxe-column
              title="本币"
              field="domesticMoney"
              width="100"
              sortable
              :filters="fileterValue.domesticMoney"
              :filter-render="FilterTemplate.numberFilterRender"
            />
            <vxe-column title="汇率" field="exchangeRate" width="80" sortable />
            <vxe-column
              title="收款账号"
              field="bankAccount"
              width="200"
              :filters="fileterValue.bankAccount"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="业务员"
              field="sellerName"
              width="100"
              :filters="fileterValue.sellerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
          </template>
          <template v-else-if="['ship', 'rma'].includes(currentTable)">
            <vxe-column title="组织" field="orgName" width="80" :filters="FilterTemplate.erpOrg" />
            <vxe-column
              title="单号"
              field="docNo"
              width="140"
              :filters="fileterValue.docNo"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="业务日期"
              field="businessDate"
              width="100"
              :filters="fileterValue.businessDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
            />
            <vxe-column
              title="来源单据"
              field="srcDocNo"
              width="140"
              :filters="fileterValue.docNo"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="客户编码"
              field="customerCode"
              width="100"
              :filters="fileterValue.customerCode"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="客户名称"
              field="customerName"
              min-width="200"
              align="left"
              :filters="fileterValue.customerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="品号"
              field="itemCode"
              width="120"
              :filters="fileterValue.itemCode"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="品名"
              field="itemName"
              width="150"
              align="left"
              :filters="fileterValue.itemName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="规格"
              field="spec"
              width="200"
              align="left"
              :filters="fileterValue.spec"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column title="数量" field="qty" width="100" sortable />
            <vxe-column title="单位" field="unit" width="80" />
            <vxe-column title="单价" field="price" width="100" sortable />
            <vxe-column
              title="金额"
              field="money"
              width="100"
              sortable
              :filters="fileterValue.money"
              :filter-render="FilterTemplate.numberFilterRender"
            />
            <vxe-column title="状态" field="status" width="100" />
            <vxe-column
              title="本币"
              field="domesticMoney"
              width="100"
              sortable
              :filters="fileterValue.domesticMoney"
              :filter-render="FilterTemplate.numberFilterRender"
            />
            <vxe-column title="汇率" field="exchangeRate" width="100" sortable />
            <vxe-column title="税组合" field="taxName" width="100" />
            <vxe-column
              title="业务员"
              field="sellerName"
              width="100"
              :filters="fileterValue.sellerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
          </template>
          <template v-else-if="currentTable === 'balance'">
            <vxe-column title="组织" field="orgName" :filters="FilterTemplate.erpOrg" />
            <vxe-column
              title="业务员"
              field="sellerName"
              :filters="fileterValue.sellerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="客户"
              field="customerName"
              :filters="fileterValue.customerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="收款金额"
              field="recpaymentMoney"
              sortable
              :filters="fileterValue.recpaymentMoney"
              :filter-render="FilterTemplate.numberFilterRender"
            >
              <template #default="{ row }">
                <el-link type="primary" @click="showDetailList(row, 'rec')">
                  {{ row.recpaymentMoney }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              title="出货金额"
              field="shipMoney"
              sortable
              :filters="fileterValue.shipMoney"
              :filter-render="FilterTemplate.numberFilterRender"
            >
              <template #default="{ row }">
                <el-link type="primary" @click="showDetailList(row, 'ship')">
                  {{ row.shipMoney }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              title="退货金额"
              field="rmaMoney"
              sortable
              :filters="fileterValue.rmaMoney"
              :filter-render="FilterTemplate.numberFilterRender"
            >
              <template #default="{ row }">
                <el-link type="primary" @click="showDetailList(row, 'rma')">
                  {{ row.rmaMoney }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              title="余额"
              field="totalMoney"
              sortable
              :filters="fileterValue.totalMoney"
              :filter-render="FilterTemplate.numberFilterRender"
            />
          </template>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="onList"
        size="small"
      />
    </div>
    <PermissionForm ref="permissionFormRef" />
    <Dialog title="详情" v-model="detailObj.visible" width="80%">
      <vxe-table
        :data="detailObj.list"
        :filter-config="{}"
        :header-cell-config="{ height: 30 }"
        :cell-config="{ height: 30 }"
        :footer-data="[detailObj.statistics]"
        :footer-cell-config="{ height: 30 }"
        :footer-cell-style="{
          background: 'var(--el-color-warning-light-9)',
          border: '1px solid #ebeef5'
        }"
        show-footer
        height="500px"
        show-overflow
        align="center"
        show-footer-overflow
      >
        <template v-if="'rec' === detailObj.type">
          <vxe-column title="组织" field="orgName" width="80" :filters="FilterTemplate.erpOrg" />
          <vxe-column
            title="单号"
            field="docNo"
            width="140"
            :filters="fileterValue.docNo"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="业务日期"
            field="businessDate"
            width="100"
            :filters="fileterValue.businessDate"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column
            title="来源单据"
            field="srcDocNo"
            width="140"
            :filters="fileterValue.srcDocNo"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="客户编码"
            field="customerCode"
            width="100"
            :filters="fileterValue.customerCode"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="客户名称"
            field="customerName"
            min-width="200"
            align="left"
            :filters="fileterValue.customerName"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="用途"
            field="property"
            width="80"
            :filters="FilterTemplate.erpRecPaymentProperty"
          />
          <vxe-column
            title="币种"
            field="currency"
            width="80"
            :filters="FilterTemplate.erpCurrency"
          />
          <vxe-column title="状态" field="status" width="80" />
          <vxe-column
            title="备注"
            field="备注"
            min-width="200"
            :filters="fileterValue.remark"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="金额"
            field="money"
            width="100"
            sortable
            :filters="fileterValue.money"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column
            title="本币"
            field="domesticMoney"
            width="100"
            sortable
            :filters="fileterValue.domesticMoney"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column title="汇率" field="exchangeRate" width="80" sortable />
          <vxe-column
            title="收款账号"
            field="bankAccount"
            width="200"
            :filters="fileterValue.bankAccount"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="业务员"
            field="sellerName"
            width="100"
            :filters="fileterValue.sellerName"
            :filter-render="FilterTemplate.textFilterRender"
          />
        </template>
        <template v-else-if="['ship', 'rma'].includes(detailObj.type)">
          <vxe-column title="组织" field="orgName" width="80" :filters="FilterTemplate.erpOrg" />
          <vxe-column
            title="单号"
            field="docNo"
            width="140"
            :filters="fileterValue.docNo"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="业务日期"
            field="businessDate"
            width="100"
            :filters="fileterValue.businessDate"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column
            title="来源单据"
            field="srcDocNo"
            width="140"
            :filters="fileterValue.docNo"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="客户编码"
            field="customerCode"
            width="100"
            :filters="fileterValue.customerCode"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="客户名称"
            field="customerName"
            min-width="200"
            align="left"
            :filters="fileterValue.customerName"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="品号"
            field="itemCode"
            width="120"
            :filters="fileterValue.itemCode"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="品名"
            field="itemName"
            width="150"
            align="left"
            :filters="fileterValue.itemName"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column
            title="规格"
            field="spec"
            width="200"
            align="left"
            :filters="fileterValue.spec"
            :filter-render="FilterTemplate.textFilterRender"
          />
          <vxe-column title="数量" field="qty" width="100" sortable />
          <vxe-column title="单位" field="unit" width="80" />
          <vxe-column title="单价" field="price" width="100" sortable />
          <vxe-column
            title="金额"
            field="money"
            width="100"
            sortable
            :filters="fileterValue.money"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column title="状态" field="status" width="100" />
          <vxe-column
            title="本币"
            field="domesticMoney"
            width="100"
            sortable
            :filters="fileterValue.domesticMoney"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column title="汇率" field="exchangeRate" width="100" sortable />
          <vxe-column title="税组合" field="taxName" width="100" />
          <vxe-column
            title="业务员"
            field="sellerName"
            width="100"
            :filters="fileterValue.sellerName"
            :filter-render="FilterTemplate.textFilterRender"
          />
        </template>
      </vxe-table>
    </Dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { RecPaymentApi } from '@/api/sales/receiving-payment'
import { customConfig } from '@/utils/vxeCustom'
import * as FilterTemplate from '@/utils/Filter'
import PermissionForm from './PermissionForm.vue'
import moment from 'moment'
import download from '@/utils/download'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'

const fileterValue = ref({
  docNo: [{ data: '' }],
  srcDocNo: [{ data: '' }],
  customerCode: [{ data: '' }],
  customerName: [{ data: '' }],
  sellerName: [{ data: '' }],
  remark: [{ data: '' }],
  bankAccount: [{ data: '' }],
  businessDate: [{ data: [] as any }],
  itemCode: [{ data: '' }],
  itemName: [{ data: '' }],
  spec: [{ data: '' }],
  recpaymentMoney: [{ data: { condition: '10', value: undefined } }],
  shipMoney: [{ data: { condition: '10', value: undefined } }],
  rmaMoney: [{ data: { condition: '10', value: undefined } }],
  totalMoney: [{ data: { condition: '10', value: undefined } }],
  money: [{ data: { condition: '10', value: undefined } }],
  domesticMoney: [{ data: { condition: '10', value: undefined } }]
})
const currentTable = ref('balance')
const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 30,
  hasStatistics: true,
  sorting: [] as any
})
const total = ref(0)
const statistics = ref<any>({})
const list = ref<any[]>([])
const toolbarRef = ref()
const tableRef = ref()
const loading = ref(false)
const permissionFormRef = ref()

const detailObj = ref({
  visible: false,
  type: 'rec',
  list: [] as any[],
  statistics: {} as any
})

const showDetailList = async (row: any, type: string) => {
  detailObj.value.type = type
  let query = cloneDeep(queryParams.value)
  query['org'] = row.org
  query['seller'] = row.seller
  query['customer'] = row.customer
  query['pageSize'] = -1
  switch (type) {
    case 'rec':
      const recRes = await RecPaymentApi.getRecPaymentPage(query)
      detailObj.value.list = recRes.list
      detailObj.value.statistics = recRes.statistics
      break
    case 'ship':
      const shipRes = await RecPaymentApi.getShipPage(query)
      detailObj.value.list = shipRes.list
      detailObj.value.statistics = shipRes.statistics
      break
    case 'rma':
      const rmaRes = await RecPaymentApi.getRmaPage(query)
      detailObj.value.list = rmaRes.list
      detailObj.value.statistics = rmaRes.statistics
  }
  detailObj.value.visible = true
}

const onTableChange = async () => {
  setTimeout(() => {
    queryParams.value = {
      pageNo: 1,
      pageSize: 30,
      hasStatistics: true,
      sorting: [] as any
    }
    fileterValue.value = {
      docNo: [{ data: '' }],
      srcDocNo: [{ data: '' }],
      customerCode: [{ data: '' }],
      customerName: [{ data: '' }],
      sellerName: [{ data: '' }],
      remark: [{ data: '' }],
      bankAccount: [{ data: '' }],
      businessDate: [{ data: [] as any }],
      itemCode: [{ data: '' }],
      itemName: [{ data: '' }],
      spec: [{ data: '' }],
      recpaymentMoney: [{ data: { condition: '10', value: undefined } }],
      shipMoney: [{ data: { condition: '10', value: undefined } }],
      rmaMoney: [{ data: { condition: '10', value: undefined } }],
      totalMoney: [{ data: { condition: '10', value: undefined } }],
      money: [{ data: { condition: '10', value: undefined } }],
      domesticMoney: [{ data: { condition: '10', value: undefined } }]
    }
    tableRef.value?.clearFilter()
    setDefaultFilter()
    list.value = []
    total.value = 0
    statistics.value = undefined

    handleList()
  }, 500)
}

const onList = async () => {
  loading.value = true
  try {
    if (currentTable.value === 'rec') {
      const res = await RecPaymentApi.getRecPaymentPage(queryParams.value)
      total.value = res.total
      list.value = res.list
      statistics.value = res.statistics
    } else if (currentTable.value === 'ship') {
      const res = await RecPaymentApi.getShipPage(queryParams.value)
      total.value = res.total
      list.value = res.list
      statistics.value = res.statistics
    } else if (currentTable.value === 'rma') {
      const res = await RecPaymentApi.getRmaPage(queryParams.value)
      total.value = res.total
      list.value = res.list
      statistics.value = res.statistics
    } else if (currentTable.value === 'balance') {
      const res = await RecPaymentApi.getTotalPage(queryParams.value)
      total.value = res.total
      list.value = res.list
      statistics.value = res.statistics
    }
  } finally {
    loading.value = false
  }
}
const exportLoading = ref(false)
const message = useMessage()
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    if (currentTable.value === 'rec') {
      const res = await RecPaymentApi.exportRecPayment(queryParams.value)
      download.excel(res, `收款明细表${dayjs().unix()}.xls`)
    } else if (currentTable.value === 'ship') {
      const res = await RecPaymentApi.exportShip(queryParams.value)
      download.excel(res, `出货明细表${dayjs().unix()}.xls`)
    } else if (currentTable.value === 'rma') {
      const res = await RecPaymentApi.exportRma(queryParams.value)
      download.excel(res, `退货明细表${dayjs().unix()}.xls`)
    } else if (currentTable.value === 'balance') {
      const res = await RecPaymentApi.exportTotal(queryParams.value)
      download.excel(res, `收款状况表${dayjs().unix()}.xls`)
    }
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const handleList = () => {
  queryParams.value.pageNo = 1
  onList()
}
// 远程筛选方法
const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['orgName', 'property', 'currency']

  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams.value).forEach((key) => {
    if (
      !['pageNo', 'pageSize', 'hasStatistics', 'sorting', 'businessDate'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams.value[key] = undefined
    }
  })
  // 更新 queryParams
  Object.assign(queryParams.value, filters)

  // 调用后端接口获取数据
  handleList()
}
// 远程排序方法
const sortChangeEvent: any = ({ field, order }) => {
  if (!order) {
    queryParams.value.sorting = queryParams.value.sorting.filter((item) => item.field != field)
  } else {
    const item = queryParams.value.sorting.find((item) => item.field == field)
    if (item) {
      item.order = order
    } else {
      queryParams.value.sorting.push({ field, order })
    }
  }
  handleList()
}

const setDefaultFilter = () => {
  const businessDate = unref(tableRef)
    .getColumns()
    .find((item) => item.field === 'businessDate')?.filters[0]
  if (businessDate) {
    businessDate.data = [
      moment().startOf('years').format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD')
    ]
    unref(tableRef).updateFilterOptionStatus(businessDate, true)
    queryParams.value['businessDate'] = businessDate.data
  } else {
    if (currentTable.value === 'balance') {
      queryParams.value['dateType'] = 'all'
    } else {
      queryParams.value['businessDate'] = [
        moment().startOf('years').format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD')
      ]
    }
  }
  if (currentTable.value == 'balance') {
    const totalMoney = unref(tableRef)
      .getColumns()
      .find((item) => item.field === 'totalMoney')?.filters[0]
    if (totalMoney) {
      totalMoney.data = { condition: '15', value: 0 }
      unref(tableRef).updateFilterOptionStatus(totalMoney, true)
      queryParams.value['totalMoney'] = totalMoney.data
    }
  }
}

onMounted(() => {
  setTimeout(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
    setDefaultFilter()
    onList()
  }, 500)
})
</script>
