<template>
  <div class="paste-parser">
    <div class="flex justify-between items-center mb-4">
      <div class="flex gap-2">
        <el-button size="small" @click="clearData">清空</el-button>
        <el-button type="primary" size="small" @click="parseData" :disabled="!rawData.trim()">
          解析数据
        </el-button>
      </div>
    </div>

    <!-- 原始数据输入区域 -->
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        粘贴数据 (支持从Excel/表格直接复制粘贴):
      </label>
      <el-input
        v-model="rawData"
        type="textarea"
        :rows="4"
        placeholder="请粘贴表格数据，支持制表符分隔的数据格式..."
        @paste="handlePaste"
        @input="handleInput"
      />
      <div class="text-xs text-gray-500 mt-1"> 检测到 {{ dataRows.length }} 行数据 </div>
    </div>

    <!-- 字段映射配置 -->
    <div class="mb-4" v-if="dataRows.length > 0">
      <label class="block text-sm font-medium text-gray-700 mb-2">字段映射配置:</label>
      <div class="grid grid-cols-6 md:grid-cols-6 lg:grid-cols-6 gap-4 p-4 bg-gray-50 rounded">
        <div v-for="(field, index) in fieldMapping" :key="index" class="flex flex-col">
          <label class="text-xs text-gray-600 mb-1">列 {{ index + 1 }}</label>
          <el-select v-model="field.target" size="small" clearable>
            <el-option
              v-for="option in props.targetFields"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <div class="text-xs text-gray-400 mt-1 truncate" :title="field.sample">
            示例: {{ field.sample }}
          </div>
        </div>
      </div>
    </div>

    <!-- 解析结果预览 -->
    <div v-if="parsedData.length > 0" class="mb-4">
      <div class="flex justify-between items-center mb-2">
        <label class="text-sm font-medium text-gray-700">解析结果预览:</label>
        <div class="flex gap-2">
          <el-button size="small" @click="exportData">导出数据</el-button>
          <el-button type="success" size="small" @click="confirmData">
            确认导入 ({{ parsedData.length }} 条)
          </el-button>
        </div>
      </div>

      <div class="max-h-96 overflow-auto border rounded">
        <el-table :data="parsedData" size="small" stripe>
          <el-table-column
            v-for="field in props.targetFields"
            :key="field.value"
            :prop="field.value"
            :label="field.label"
            show-overflow-tooltip
          />
        </el-table>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <div v-if="errors.length > 0" class="mb-4">
      <label class="text-sm font-medium text-red-600 mb-2 block">解析错误:</label>
      <div class="bg-red-50 border border-red-200 rounded p-3">
        <ul class="text-sm text-red-600">
          <li v-for="(error, index) in errors" :key="index" class="mb-1">
            第 {{ error.row }} 行: {{ error.message }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { propTypes } from '@/utils/propTypes'

// 定义数据接口
interface BOMItem {
  itemCode: string
  version?: string
  itemName: string
  drawingNo?: string
  level?: string
  attribute?: string
  category?: string
  spec: string
  remark?: string
  unit?: string
  quantity?: number
  requireDate?: string
}

interface FieldMapping {
  target: string
  sample: string
}

interface ParseError {
  row: number
  message: string
}

interface Field {
  label: string
  value: string
}

// 定义emits
const emits = defineEmits<{
  (e: 'data-parsed', data: BOMItem[]): void
}>()

const props = defineProps({
  targetFields: propTypes.oneOfType<Field[]>([]).isRequired,
  mappings: propTypes.oneOfType<string[]>([]).isRequired
})

// 响应式数据
const rawData = ref('')
const parsedData = ref<BOMItem[]>([])
const errors = ref<ParseError[]>([])
const fieldMapping = ref<FieldMapping[]>([])

// 计算属性：数据行
const dataRows = computed(() => {
  if (!rawData.value.trim()) return []
  return rawData.value
    .trim()
    .split('\n')
    .filter((line) => line.trim())
})

// 监听数据变化，自动更新字段映射
watch(
  dataRows,
  (newRows) => {
    if (newRows.length > 0) {
      updateFieldMapping(newRows[0])
    }
  },
  { immediate: true }
)

// 更新字段映射
const updateFieldMapping = (firstRow: string) => {
  const columns = firstRow.split('\t')
  fieldMapping.value = columns.map((col, index) => ({
    target: props.mappings[index] || '',
    sample: col.trim().substring(0, 20) + (col.trim().length > 20 ? '...' : '')
  }))
}

// 处理粘贴事件
const handlePaste = () => {
  // 延迟处理，确保数据已经粘贴到输入框
  setTimeout(() => {
    if (dataRows.value.length > 0) {
      ElMessage.success(`检测到 ${dataRows.value.length} 行数据`)
    }
  }, 100)
}

// 处理输入事件
const handleInput = () => {
  // 清空之前的解析结果
  parsedData.value = []
  errors.value = []
}

// 解析数据
const parseData = () => {
  errors.value = []
  parsedData.value = []

  if (dataRows.value.length === 0) {
    ElMessage.warning('请先输入数据')
    return
  }

  const results: BOMItem[] = []

  dataRows.value.forEach((row, rowIndex) => {
    try {
      const columns = row.split('\t')
      const item: any = {}

      // 根据字段映射解析数据
      fieldMapping.value.forEach((mapping, colIndex) => {
        if (mapping.target && columns[colIndex] !== undefined) {
          const value = columns[colIndex].trim()

          if (mapping.target === 'quantity') {
            // 数量字段转换为数字
            const num = parseFloat(value)
            if (!isNaN(num)) {
              item[mapping.target] = num
            }
          } else if (mapping.target === 'requireDate') {
            // 日期字段格式化
            if (value && value !== '') {
              item[mapping.target] = formatDate(value)
            }
          } else {
            // 其他字段直接赋值
            if (value && value !== '') {
              item[mapping.target] = value
            }
          }
        }
      })

      // 验证必填字段
      if (!item.itemCode) {
        errors.value.push({
          row: rowIndex + 1,
          message: '品号不能为空'
        })
        return
      }

      if (!item.itemName) {
        errors.value.push({
          row: rowIndex + 1,
          message: '品名不能为空'
        })
        return
      }

      results.push(item as BOMItem)
    } catch (error) {
      errors.value.push({
        row: rowIndex + 1,
        message: `解析失败: ${error}`
      })
    }
  })

  parsedData.value = results

  if (results.length > 0) {
    ElMessage.success(`成功解析 ${results.length} 条数据`)
  }

  if (errors.value.length > 0) {
    ElMessage.warning(`有 ${errors.value.length} 条数据解析失败`)
  }
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  // 尝试解析各种日期格式
  const date = new Date(dateStr)
  if (!isNaN(date.getTime())) {
    return date.toISOString().split('T')[0] // YYYY-MM-DD格式
  }
  return dateStr // 如果无法解析，返回原始字符串
}

// 清空数据
const clearData = () => {
  rawData.value = ''
  parsedData.value = []
  errors.value = []
  fieldMapping.value = []
}

// 导出数据
const exportData = () => {
  if (parsedData.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  // 转换为CSV格式
  const headers = ['品号', '品名', '规格', '类别', '属性', '数量', '单位', '需求日期', '备注']
  const csvContent = [
    headers.join(','),
    ...parsedData.value.map((item) =>
      [
        item.itemCode || '',
        item.itemName || '',
        item.spec || '',
        item.category || '',
        item.attribute || '',
        item.quantity || '',
        item.unit || '',
        item.requireDate || '',
        item.remark || ''
      ]
        .map((field) => `"${field}"`)
        .join(',')
    )
  ].join('\n')

  // 下载文件
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `BOM数据_${new Date().toISOString().split('T')[0]}.csv`
  link.click()

  ElMessage.success('数据导出成功')
}

// 确认数据
const confirmData = () => {
  if (parsedData.value.length === 0) {
    ElMessage.warning('没有可确认的数据')
    return
  }

  emits('data-parsed', parsedData.value)
  ElMessage.success(`已确认导入 ${parsedData.value.length} 条数据`)
}
</script>

<style scoped>
.paste-parser {
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 70vh;
  overflow: auto;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
</style>
