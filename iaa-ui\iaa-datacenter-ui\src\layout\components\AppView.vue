<script lang="ts" setup>
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useAppStore } from '@/store/modules/app'
import { Footer } from '@/layout/components/Footer'
import { getMenuSizeByUserId } from '@/api/system/menu'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import { handleTree, treeToList } from '@/utils/tree'
import { ElMessageBox } from 'element-plus'

const { wsCache } = useCache()

const message = useMessage()

defineOptions({ name: 'AppView' })

const appStore = useAppStore()

const layout = computed(() => appStore.getLayout)

const fixedHeader = computed(() => appStore.getFixedHeader)

const footer = computed(() => appStore.getFooter)

const tagsViewStore = useTagsViewStore()

const getCaches = computed((): string[] => {
  return tagsViewStore.getCachedViews
})

const tagsView = computed(() => appStore.getTagsView)

//region 无感刷新
const routerAlive = ref(true)
// 无感刷新，防止出现页面闪烁白屏
const reload = () => {
  routerAlive.value = false
  nextTick(() => (routerAlive.value = true))
}
// 为组件后代提供刷新方法
provide('reload', reload)
//endregion

onMounted(async () => {
  const user = wsCache.get(CACHE_KEY.USER)
  if (user?.user?.id === 1) {
    return
  }
  const res = await getMenuSizeByUserId()
  const routers = wsCache.get(CACHE_KEY.ROLE_ROUTERS)

  const menuList = treeToList(routers)
  if (res?.menu != menuList.length || res?.permission != user.permissions?.length) {
    await ElMessageBox.confirm('检测到本地权限与服务器不一致，请确认重新获取', '权限刷新', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
      showCancelButton: false
    })
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.USER)
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
    // 刷新浏览器
    location.reload()
  }
})
</script>

<template>
  <section
    :class="[
      'p-[var(--app-content-padding)] w-full bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]',
      {
        '!min-h-[calc(100vh-var(--logo-height)-var(--tags-view-height)-var(--app-footer-height))] pb-0':
          footer
      }
    ]"
  >
    <router-view v-if="routerAlive">
      <template #default="{ Component, route }">
        <keep-alive :include="getCaches">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </template>
    </router-view>
  </section>
  <!-- <Footer v-if="footer" /> -->
</template>
