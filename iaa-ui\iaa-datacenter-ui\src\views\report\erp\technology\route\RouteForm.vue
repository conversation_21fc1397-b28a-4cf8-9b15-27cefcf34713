<template>
  <Dialog title="工艺路线" v-model="visiable" width="60%">
    <el-form :model="formData" :rules="formRules" ref="formRef" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="编码" prop="code">
            <el-input
              v-model="formData.code"
              :maxlength="64"
              show-word-limit
              :disabled="formData.id"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="名称" prop="name">
            <el-input v-model="formData.name" :maxlength="255" show-word-limit :disabled="!edit" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="版本">
            <el-input :disabled="true" v-model="formData.version" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="描述">
        <el-input
          type="textarea"
          :rows="4"
          v-model="formData.description"
          :maxlength="500"
          show-word-limit
          :disabled="!edit"
        />
      </el-form-item>
    </el-form>
    <vxe-table
      height="300px"
      :header-cell-style="{ padding: 0, height: '30px' }"
      :cell-style="{ padding: 0, height: '30px' }"
      :data="formData.body"
      :loading="loading"
      align="center"
      :row-config="{ drag: true,height: 30 }"
    >
      <vxe-column type="seq" width="80" />
      <vxe-column title="工艺组" field="defineId">
        <template #default="{ row }">
          <el-select v-model="row.defineId" :disabled="!edit">
            <el-option
              v-for="item in porps.defineList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </template>
      </vxe-column>
      <vxe-column title="标准工时(分)">
        <template #default="{ row }">
          <el-input-number v-model="row.timeConsuming" :min="0" :precision="2" :disabled="!edit" />
        </template>
      </vxe-column>
      <vxe-column title="备注">
        <template #default="{ row }">
          <el-input v-model="row.reamrks" :maxlength="255" show-word-limit :disabled="!edit" />
        </template>
      </vxe-column>
      <vxe-column title="操作" width="80" :disabled="edit">
        <template #default="{ rowIndex }">
          <el-button type="text" @click="formData.body.splice(rowIndex, 1)" :disabled="!edit"
            >移除</el-button
          >
        </template>
      </vxe-column>
    </vxe-table>
    <el-button
      type="primary"
      plain
      class="w-full"
      size="small"
      :loading="loading"
      @click="
        formData.body.push({
          id: undefined,
          headId: undefined,
          defineId: undefined,
          timeConsuming: undefined,
          reamrks: undefined
        })
      "
      :disabled="!edit"
    >
      添加行
    </el-button>
    <template #footer>
      <el-button type="primary" @click="onSave" :loading="loading" v-if="edit">保存</el-button>
      <el-button type="warning" @click="edit = !edit" :loading="loading" v-if="!edit"
        >修改</el-button
      >
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { RouteApi } from '@/api/report/technology/route'
import { propTypes } from '@/utils/propTypes'
import { cloneDeep } from 'lodash-es'
const visiable = ref(false)

interface Body {
  id?: number
  headId?: number
  defineId?: number
  sort?: number
  timeConsuming?: number
  reamrks?: string
}

const porps = defineProps({
  defineList: propTypes.oneOfType<any[]>([]).isRequired
})

const formData = ref({
  id: undefined,
  code: undefined,
  name: undefined,
  version: 0,
  description: undefined,
  body: [] as Body[]
})
const formRules = reactive({
  code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
})
const formRef = ref()
const loading = ref(false)
const message = useMessage()
const emits = defineEmits(['success'])
const edit = ref(true)

const onSave = async () => {
  await unref(formRef)?.validate()
  loading.value = true
  try {
    formData.value.body = formData.value.body.filter((item) => !!item.defineId)
    const data = cloneDeep(formData.value)
    if (!data.body || data.body.length === 0) {
      message.error('请添加工艺路线行')
      return
    }
    let no = 1
    data.body.forEach((item) => (item.sort = no++))
    await RouteApi.saveRoute(data)
    message.success('保存成功')
    emits('success')
    refresh()
    visiable.value = false
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    name: undefined,
    version: 0,
    description: undefined,
    body: [] as Body[]
  }
}

const openForm = async (id?: number) => {
  loading.value = true
  try {
    edit.value = true
    refresh()
    visiable.value = true
    if (id) {
      const data = await RouteApi.getRouteById(id)
      formData.value = data
      edit.value = false
    }
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
