<template>
  <ContentWrap>
    <!-- 添加标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="mb-20px">
      <el-tab-pane label="待认领" name="pending" />
      <el-tab-pane label="我的" name="mine" />
    </el-tabs>

    <vxe-toolbar custom ref="toolbarRef" size="mini">
      <template #buttons>
        <el-button
          plain
          type="success"
          class="mr-10px"
          size="small"
          v-hasPermi="['collection:information:create']"
          @click="databaseUploadFormRef?.open()"
          title="上传"
          v-if="activeTab === 'pending'"
        >
          上传
        </el-button>
        <el-button
          type="info"
          plain
          class="mr-10px"
          size="small"
          @click="handleExport()"
          title="导出"
          :loading="exportLoading"
        >
          导出
        </el-button>
        <el-button
          type="primary"
          plain
          class="mr-10px"
          size="small"
          v-hasPermi="['collection:information:claim']"
          @click="handleClaim()"
          title="认领"
          v-if="activeTab === 'pending'"
        >
          认领
        </el-button>
      </template>

      <template #tools>
          <el-button
            type="primary"
            link
            class="mr-10px"
            @click="permissionFormRef?.openForm()"
            v-hasPermi="['receiving:payment:permission']"
            size="small"
          >
            权限管理
          </el-button>
      </template>
    </vxe-toolbar>

    <div class="h-[calc(100vh-220px)]">
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          v-if="activeTab === 'pending'"
          :row-config="{ height: 25, keyField: 'id' }"
          ref="tableRef"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :filter-config="{}"
          show-footer
          keep-source
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          :mouse-config="{ selected: true }"
          @filter-change="handleFilterChange"
          tabindex="0"
          size="mini"
        >
          <vxe-column type="checkbox" width="40" field="id" fixed="left" />
          <vxe-column
            field="dateStr"
            width="200"
            title="收款日期"
            :filters="dateStr"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column field="collectionAccount" title="收款账号" min-width="200" />
          <vxe-column field="payer" title="付款人" min-width="200" />
          <vxe-column field="collectionAmount" title="收款金额" width="120" />
          <vxe-column field="currency" title="币种" width="120" />

          <vxe-column title="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                @click="handleClaim(row.id)"
                link
                type="primary"
                collection:information:claim
                v-hasPermi="['collection:information:claim']"
                v-if="activeTab === 'pending'"
              >
                认领
              </el-button>
              <el-button
                v-if="row.status !== 1"
                v-hasPermi="['collection:information:delete']"
                @click="handleDeleteInformation([row.id])"
                link
                type="danger"
              >
                删除
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <vxe-table
          v-else
          :row-config="{ height: 25, keyField: 'id' }"
          ref="tableRef1"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :filter-config="{}"
          show-footer
          keep-source
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          :mouse-config="{ selected: true }"
          @filter-change="handleFilterChange"
          tabindex="0"
          size="mini"
        >
          <vxe-column type="checkbox" width="40" field="id" fixed="left" />
          <vxe-column
            field="claimDate"
            width="200"
            title="收款日期"
            :filters="claimDate"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column field="collectionAccount" title="关联的收款信息" min-width="200">
            <template #default="{ row }">
              <span>{{ getCollectionTitle(row) }}</span>
            </template>
          </vxe-column>
          <vxe-column field="totalAmount" title="总金额" width="120" />
          <vxe-column field="currency" title="币种" width="120" />
          <!-- 添加状态列，仅在"我的"标签页显示 -->
          <vxe-column field="status" title="状态" width="120" v-if="activeTab === 'mine'">
            <template #default="{ row }">
              <el-tag v-if="row.status === 2" type="primary">认领中</el-tag>
              <el-tag v-else-if="row.status === 0" type="success">已认领</el-tag>
            </template>
          </vxe-column>

          <vxe-column field="salesmanName" title="业务员" width="120" />
          <vxe-column title="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                @click="handleContinueClaim(row.id)"
                link
                type="primary"
                v-if="activeTab === 'mine' && row.status === 2"
                v-hasPermi="['collection:information:claim']"
              >
                继续
              </el-button>
              <el-button
                v-if="row.status === 0"
                @click="handleEdit(row.id)"
                link
                type="warning"
                v-hasPermi="['record:money:admin']"
              >
                修改
              </el-button>
              <el-button
                @click="handleViewClaim(row.id)"
                link
                type="info"
                v-if="activeTab === 'mine' && row.status === 0"
              >
                查看
              </el-button>
              <el-button
                @click="handleDelete(row.id)"
                link
                type="danger"
                v-if="activeTab === 'mine'"
                v-hasPermi="['collection:information:cancel']"
              >
                取消
              </el-button>

            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        size="small"
      />
    </div>
  </ContentWrap>

  <PermissionForm ref="permissionFormRef" />
  <DatabaseUploadForm ref="databaseUploadFormRef" @success="getList()" />
  <!-- 批量删除确认对话框 -->
  <Dialog title="批量删除确认" v-model="batchDeleteVisible">
    <div>当前选中：{{ selectionData.length }} 条数据，确认要删除吗？</div>
    <template #footer>
      <el-button type="danger" @click="confirmBatchDelete()">确认删除</el-button>
      <el-button @click="batchDeleteVisible = false">取消</el-button>
    </template>
  </Dialog>

  <!-- 添加认领弹窗组件 -->
  <ClaimDialog
    v-model:show="claimDialogVisible"
    :ids="selectedIds"
    :prefill="prefillData"
    :read-only="readOnlyMode"
    :isEdit="isEdit"
    @success="handleClaimSuccess"
  />
</template>

<script lang="ts" setup>
import { InformationApi } from '@/api/foreign-trade/collectionInformation/index'
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import DatabaseUploadForm from '@/views/foreign-trade/collectionInformation/components/DatabaseUploadForm.vue'
import * as FilterValue from '@/utils/Filter'
import PermissionForm from '@/views/sales/receiving-payment/components/PermissionForm.vue'
import * as FilterTemplate from '@/utils/Filter'
import download from '@/utils/download'
import { checkPermission } from '@/store/modules/permission'
import ClaimDialog from './ClaimDialogPC.vue'

const claimDate = ref([{ data: [] }])
const dateStr = ref([{ data: [] }])
const databaseUploadFormRef = ref()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

const permissionFormRef = ref()

// 添加标签页相关
const activeTab = ref<'pending' | 'mine'>('pending')

const queryParams = reactive({
  pageNo: 1,
  pageSize: 30,
  isMe: false,
  collectionAccount: undefined,
  dateStr: undefined,
  claimDate: undefined,
  status: [0] // 0:待认领, 1:暂存, 2:已认领
})

const exportLoading = ref(false) // 导出的加载中

// 认领相关
const claimDialogVisible = ref(false)
const selectedIds = ref<number[]>([])
const prefillData = ref<any>(null)
const readOnlyMode = ref(false)
const isEdit = ref(false)

//筛选时间
const handleFilterChange = (params: any) => {
  console.log(params)

  // 特定字段列表
  const specialFields = ['classification']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize', 'status', 'isMe'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

const tableRef = ref()
const tableRef1 = ref()
const toolbarRef = ref()
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    let data: any
    if (activeTab.value === 'mine') {
      data = await ClaimApi.getClaimPage({
        ...queryParams,
        type: 1
      })
    } else {
      data = await InformationApi.getInformationPage(queryParams)
    }

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 标签页切换 */
const handleTabChange = (tabName: string) => {
  if (tabName === 'mine') {
    queryParams.isMe = true
    queryParams.status = [1, 2] // 暂存和已认领
    nextTick(() => {
      unref(tableRef1)?.connect(unref(toolbarRef))
    })
  } else {
    queryParams.isMe = false
    queryParams.status = [0] // 待认领
    nextTick(() => {
      unref(tableRef)?.connect(unref(toolbarRef))
    })
  }
  // 重置分页
  queryParams.pageNo = 1
  getList()
}

/** 认领按钮操作 */
const handleClaim = async (rowId?: number) => {
  let ids: number[] = []
  let selectedRows: any[] = []

  if (rowId) {
    ids = [rowId]
    // 获取选中行的数据
    selectedRows = list.value.filter((item) => item.id === rowId)
  } else {
    const $table = tableRef.value
    if ($table) {
      selectedRows = $table.getCheckboxRecords() || []
      ids = selectedRows.map((r: any) => r.id)
    }
  }

  if (!ids.length) {
    message.alertError('请选择要认领的数据')
    return
  }

  // 检查币种是否一致
  const currencies = [...new Set(selectedRows.map((row) => row.currency))]
  if (currencies.length > 1) {
    message.alertError('所选收款记录币种不一致，无法一起认领')
    return
  }

  // 打开认领对话框
  selectedIds.value = ids
  prefillData.value = null
  readOnlyMode.value = false
  claimDialogVisible.value = true
}

/** 继续认领按钮操作 */
const handleContinueClaim = async (rowId: number) => {
  try {
    // 获取第一条记录的详细信息用于继续认领
    const detail = await InformationApi.getSuspended(rowId)
    selectedIds.value = [rowId]
    prefillData.value = detail || null
    readOnlyMode.value = false
    claimDialogVisible.value = true
  } catch (e) {
    console.error(e)
    getList()
  }
}

const handleEdit = async (rowId: number) => {
  try {
    const detail = await InformationApi.getSuspended(rowId)
    selectedIds.value = [rowId]
    prefillData.value = detail || null
    readOnlyMode.value = false
    claimDialogVisible.value = true
    isEdit.value = true
  } catch (e) {
    console.error(e)
    getList()
  }
}

/** 取消按钮操作 */
const handleDelete = async (id?: number) => {
  message.confirm('是否取消认领?').then(async () => {
    loading.value = true
    await ClaimApi.deleteClaim([id])
    loading.value = false
    message.success('取消认领成功')
    await getList()
  })
}

const handleDeleteInformation = async (id?: any) => {
  message.confirm('是否删除收款信息?').then(() => {
    loading.value = true
    ClaimApi.deleteClaimInformation(id)
    loading.value = false
    message.success('删除收款信息成功')
    getList()
  })
}
/** 查看详情按钮操作 */
const handleViewClaim = async (rowId?: number) => {
  let ids: number[] = []
  let selectedRows: any[] = []

  if (rowId) {
    ids = [rowId]
    selectedRows = list.value.filter((item) => item.id === rowId)
  } else {
    const $table = tableRef.value
    if ($table) {
      // 只选择状态为2(已认领)的记录
      const allSelectedRows = $table.getCheckboxRecords() || []
      selectedRows = allSelectedRows.filter((row: any) => row.status === 2)
      ids = selectedRows.map((r: any) => r.id)
    }
  }

  if (!ids.length) {
    message.alertError('请选择状态为"已认领"的数据进行查看')
    return
  }

  // 检查币种是否一致
  const currencies = [...new Set(selectedRows.map((row) => row.currency))]
  if (currencies.length > 1) {
    message.alertError('所选收款记录币种不一致，无法一起查看')
    return
  }

  try {
    // 获取第一条记录的详细信息用于查看
    const detail = await InformationApi.getSuspended(ids[0])
    selectedIds.value = [ids[0]]
    prefillData.value = {
      ...detail,
      readOnly: true
    }
    readOnlyMode.value = true
    claimDialogVisible.value = true
  } catch (e) {
    console.error(e)
    getList()
  }
}

const form = reactive({
  dateStr: '',
  collectionAccount: '',
  collectionId: ''
})

// 批量删除相关
const batchDeleteVisible = ref(false)
const selectionData = ref<any[]>([])

// 批量删除确认
const confirmBatchDelete = async () => {
  loading.value = true
  try {
    if (!checkPermission(['collection:information:delete'])) {
      message.error('您没有删除数据的权限，请联系管理员！')
      return
    } else {
      const $table = tableRef.value
      if (!$table) return
      // 分离临时新增的数据和已保存的数据
      const savedIds: number[] = [] // 已保存数据的ID（数字类型）
      selectionData.value.forEach((item) => {
        savedIds.push(item.id)
      })
      // 删除已保存的数据
      if (savedIds.length > 0) {
        await InformationApi.deleteInformation(savedIds)
      }

      batchDeleteVisible.value = false
      selectionData.value = []
      await getList()
      message.success('批量删除成功')
    }
  } catch {
    message.error('批量删除失败')
  } finally {
    loading.value = false
  }
}

// 添加计算收款信息标题的方法
const getCollectionTitle = (item: any) => {
  let title = '' // 默认为 customerName

  if (item.collectionList && item.collectionList.length > 0) {
    const collectionInfo = item.collectionList
      .map((collection) => `${collection.collectionAccount}-${collection.collectionAmount}`)
      .join(', ')

    // 如果 collectionInfo 不为空，则将其添加到 title 中
    if (collectionInfo) {
      title += ` ${collectionInfo}`
    }
  }

  return title
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InformationApi.exportInformation(queryParams)
    download.excel(data, `收款信息.xlsx`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 认领成功回调 */
const handleClaimSuccess = () => {
  claimDialogVisible.value = false
  selectedIds.value = []
  prefillData.value = null
  getList() // 重新加载列表数据
}

/** 初始化 **/
onMounted(() => {
  getList()
  // 确保表格可以获取焦点
  nextTick(() => {
    if (tableRef.value) {
      tableRef.value.$el.setAttribute('tabindex', '0')
    }
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})

// 暴露方法给父组件使用
defineExpose({
  getList
})
</script>
