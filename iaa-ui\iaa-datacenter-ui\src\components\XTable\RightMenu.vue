<template>
  <div id="right-menu" ref="rightMenuRef">
    <ul class="table-right-menu">
      <li v-for="item in props.items" :key="item.text" @click="fn(item)">
        {{ item.text }}
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  items: propTypes.arrayOf<any>(Array<any>).isRequired,
  clickXY: propTypes.any.isRequired
})

const rightMenuRef= ref<HTMLElement|null>(null)

const hide = (e: any) => {
  if (e.button === 0) {
    let positionXY = rightMenuRef.value!
    positionXY.style.display = 'none'
    document.removeEventListener('mouseup', hide)
  }
}

const fn = (item: any) => {
  item.fn(item?.params?.row, item?.params?.column)
}

watch(
  () => props.clickXY.position,
  (val) => {
    console.log(1)
    let x = val.x // x轴坐标
    let y = val.y // y轴坐标
    let innerWidth = window.innerWidth // 浏览器可视区域宽度
    let innerHeight = window.innerHeight // 浏览器可视区域高度
    let menuHeight = props.items.length * 30 // 右键菜单高度
    let menuWidth = 100 // 右键菜单宽度

    let positionXY = rightMenuRef.value!
    positionXY.style.display = 'block'
    // 判断右键菜单是否超出浏览器可视区域
    positionXY.style.top = (y + menuHeight > innerHeight ? innerHeight - menuHeight - 10 : y) + 'px'
    positionXY.style.left = (x + menuWidth > innerWidth ? innerWidth - menuWidth - 10 : x) + 'px'
    // 监听点击事件，隐藏菜单
    document.addEventListener('mouseup', hide, false)
  }
)
</script>

<style lang="scss" scoped>
#right-menu {
  min-width: 108px;
  position: fixed;
  z-index: 999;
  display: none;
  background-color: #fafafa;
  box-shadow: 0 0 5px #d7f3ff;
  border: 1px solid #ccc;
}

.table-right-menu {
  line-height: 30px;
  text-align: center;
  font-size: 12px;
  padding-left: 0;
  list-style-type: none;
}

li {
  height: 30px;
  display: flex;
  justify-content: space-evenly;
  padding: 0 15px;
  align-items: center;
  cursor: pointer;
  border-bottom: #f1f1f1 1px solid;
}

li:hover{
  background-color: #f7f7f7;
}
</style>
