<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <el-form inline class="custom-form" size="small">
      <el-form-item label="日期">
        <el-radio-group v-model="queryParams.dateType" @change="onList">
          <el-radio-button label="年" value="year" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
        <el-date-picker
          class="!w-100px"
          :type="queryParams.dateType"
          v-model="queryParams.sTime"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="onList"
        />
      </el-form-item>
    </el-form>
    <el-row>
      <el-col
        v-for="dict in getIntDictOptions('information_person')"
        :key="dict.value"
        :span="6"
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        :xl="6"
      >
        <div
          :id="`person-${dict.value}-line`"
          class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
        ></div>
      </el-col>
    </el-row>
    <div class="h-[calc(100%-200px-60px)]">
      <vxe-table
        height="100%"
        align="center"
        border
        :loading="loading"
        :data="dataList"
        show-overflow
        stripe
      >
        <vxe-column title="日期" field="dateTime">
          <template #default="{ row }">
            {{ formatToDateTime(row.dateTime) }}
          </template>
        </vxe-column>
        <vxe-column title="标题" field="type">
          <template #default="{ row }">
            【{{ `${row.endDept}-${row.endUser}` }}】收到了来自【{{
              `${row.startDept}-${row.startUser}`
            }}】的
            <el-tag :type="row.type === '小红花' ? 'danger' : 'warning'">
              {{ row.type }}
            </el-tag>
          </template>
        </vxe-column>
        <vxe-column title="事件" field="title" />
        <vxe-column title="内容" field="content" />
        <vxe-column title="总计红花" field="countRed">
          <template #default="{ row }">
            <el-tag type="danger">{{ row.countRed }}</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="总计黄花" field="countBlack">
          <template #default="{ row }">
            <el-tag type="warning">{{ row.countBlack }}</el-tag>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { getIntDictOptions } from '@/utils/dict'
import { formatToDateTime } from '@/utils/dateUtil'
import { RedApi } from '@/api/butt-joint/red'
import * as echarts from 'echarts'
import { markRaw } from 'vue'

const queryParams = reactive({
  dateType: 'year' as any,
  sTime: moment().format('YYYY-MM-DD')
})
const loading = ref(false)
const dataList = ref<any[]>([])

// 图表实例存储
const chartInstances = ref<Map<string, any>>(new Map())

// 红花数据接口
interface RedFlowerData {
  endDept: string
  endUser: string
  startUser: string
  startDept: string
  content: string
  title: string
  type: string
  countRed: number
  countBlack: number
  dateTime: string
}

const onList = async () => {
  loading.value = true
  try {
    const res = await RedApi.getFlowerList(queryParams)
    dataList.value = res
    // 数据加载完成后渲染图表
    await nextTick()
    renderCharts()
  } finally {
    loading.value = false
  }
}

// 处理数据，按收花人和月份分组统计
const processDataByPersonAndMonth = () => {
  const result: Record<string, Record<string, { red: number; yellow: number }>> = {}

  // 获取所有负责人
  const persons = getIntDictOptions('information_person')

  // 初始化结果结构
  persons.forEach(person => {
    result[person.value] = {}
  })

  // 处理每条红花数据
  dataList.value.forEach((flower: RedFlowerData) => {
    if (!flower.endUser) return

    const flowerMonth = moment(flower.dateTime).format('YYYY-MM')

    // 查找对应的人员ID
    const person = persons.find(p => p.label === flower.endUser)
    if (!person) return

    const personKey = person.value.toString()
    if (!result[personKey]) return

    if (!result[personKey][flowerMonth]) {
      result[personKey][flowerMonth] = { red: 0, yellow: 0 }
    }

    // 根据类型累加数量
    if (flower.type === '小红花') {
      result[personKey][flowerMonth].red += 1
    } else if (flower.type === '小黄花') {
      result[personKey][flowerMonth].yellow += 1
    }
  })

  return result
}

// 渲染所有图表
const renderCharts = () => {
  const processedData = processDataByPersonAndMonth()
  const persons = getIntDictOptions('information_person')

  persons.forEach(person => {
    renderPersonChart(person.value.toString(), person.label, processedData[person.value] || {})
  })
}

// 渲染单个负责人的图表
const renderPersonChart = (personId: string, personName: string, monthlyData: Record<string, { red: number; yellow: number }>) => {
  const containerId = `person-${personId}-line`
  const container = document.getElementById(containerId)

  if (!container) {
    console.warn(`Container ${containerId} not found`)
    return
  }

  // 销毁已存在的图表实例
  if (chartInstances.value.has(containerId)) {
    chartInstances.value.get(containerId).dispose()
  }

  // 准备图表数据
  const months = Object.keys(monthlyData).sort()
  const redData = months.map(month => monthlyData[month]?.red || 0)
  const yellowData = months.map(month => monthlyData[month]?.yellow || 0)

  // 如果没有数据，显示空状态
  if (months.length === 0) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">${personName}</div>
          <div class="text-sm mt-2">暂无红黄花数据</div>
        </div>
      </div>
    `
    return
  }

  // 创建ECharts实例
  const chart = markRaw(echarts.init(container))

  const option = {
    title: {
      text: personName,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}朵<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['小红花', '小黄花'],
      bottom: 0,
      textStyle: {
        fontSize: 10
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months.map(month => moment(month).format('MM月')),
      axisLabel: {
        fontSize: 10,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '数量(朵)',
      nameTextStyle: {
        fontSize: 10
      },
      axisLabel: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '小红花',
        type: 'bar',
        data: redData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ff6b6b' },
            { offset: 1, color: '#ee5a52' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ff5252' },
              { offset: 1, color: '#d32f2f' }
            ])
          }
        }
      },
      {
        name: '小黄花',
        type: 'bar',
        data: yellowData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffd93d' },
            { offset: 1, color: '#f57f17' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ffcc02' },
              { offset: 1, color: '#ff8f00' }
            ])
          }
        }
      }
    ]
  }

  chart.setOption(option)
  chartInstances.value.set(containerId, chart)
}

// 清理所有图表实例
const disposeAllCharts = () => {
  chartInstances.value.forEach(chart => {
    chart.dispose()
  })
  chartInstances.value.clear()
}

// 监听窗口大小变化，调整所有图表
const handleResize = () => {
  chartInstances.value.forEach(chart => {
    chart.resize()
  })
}

onMounted(() => {
  onList()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeAllCharts()
})
</script>

<style scoped>
/* 图表容器样式 */
[id^="person-"][id$="-line"] {
  position: relative;
  overflow: hidden;
}

/* 空状态样式 */
[id^="person-"][id$="-line"] .flex {
  height: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  [id^="person-"][id$="-line"] {
    height: 180px !important;
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  [id^="person-"][id$="-line"] {
    height: 160px !important;
  }
}
</style>
