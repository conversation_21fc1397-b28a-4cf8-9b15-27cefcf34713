deducts
<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <!-- <ContentWrap>
      <el-form inline class="custom-form" size="small">
        <CardTitle title="部门整体指标" />
        <el-date-picker
          v-model="queryParams.year"
          value-format="YYYY"
          type="year"
          placeholder="选择年份"
          @change="onItList"
          :clearable="false"
          class="ml-10px"
        />
      </el-form>
      <el-row>
        <el-col
          v-for="dict in getStrDictOptions('IT_Indicator')?.filter(
            (item) => !item.value.includes('plan')
          )"
          :key="dict.value"
          :span="6"
          :xs="24"
          :sm="24"
          :md="12"
          :lg="6"
          :xl="6"
        >
          <div
            :id="dict.value"
            class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
          ></div>
        </el-col>
      </el-row>
    </ContentWrap> -->
    <ContentWrap>
      <div class="min-h-[calc(100vh-400px)]">
        <vxe-toolbar ref="toolbarRef" export size="mini">
          <template #buttons>
            <CardTitle title="人员指标" />
            <el-date-picker
              type="month"
              v-model="informationDate"
              value-format="YYYY-MM-DD"
              @change="onInformationList"
              size="small"
              class="ml-10px"
            />
          </template>
        </vxe-toolbar>
        <div class="h-400px">
          <vxe-table
            align="center"
            border
            :data="[itLeaderMap]"
            :header-cell-style="{ padding: 0 }"
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            show-overflow
            stripe
            ref="tableRef"
            :export-config="{}"
            :cell-class-name="cellClassName"
            :header-cell-class-name="cellHeaderClassName"
          >
            <vxe-colgroup title="基础信息" field="baseInfo">
              <vxe-column title="年" field="year" width="60">
                <template #default>
                  {{ moment(informationDate).format('YYYY') }}
                </template>
              </vxe-column>
              <vxe-column title="月" field="month" width="60">
                <template #default>
                  {{ moment(informationDate).format('M') }}
                </template>
              </vxe-column>
              <vxe-column title="人员" field="personType" width="60">
                <template #default> 桂贤明 </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="满意度" field="deliveryRate">
              <vxe-column title="基础分" field="basePerformance" width="80">
                <template #default> 40 </template>
              </vxe-column>
              <vxe-column title="得分" field="basePerformance" width="80">
                <template #default>
                  {{ getFlowScope }}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="人力投产比增长率" field="process">
              <vxe-column title="基础分" field="processBasePerformance" width="80">
                <template #default> 40 </template>
              </vxe-column>
              <vxe-column title="计划比率" field="plan_roi" width="80" />
              <vxe-column title="实际比率" field="roi" width="80" />
              <vxe-column title="得分" field="roiScore" width="80">
                <template #default="{ row }">
                  {{
                    row?.plan_roi && row?.roi
                      ? (30 * Math.min(row?.roi / row?.plan_roi, 1)).toFixed(2)
                      : '0.00'
                  }}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="领导评分" field="delivery">
              <vxe-column title="基础分" field="deliveryBasePerformance" min-width="100">
                <template #default> 20 </template>
              </vxe-column>
              <vxe-column title="评分" field="boss" min-width="200">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.boss"
                    v-if="getUser.id === 1437"
                    class="!w-100%"
                    :max="20"
                    @change="changeDeptIndicator(row)"
                  />
                  <span v-esle>{{ row?.boss }}</span>
                </template>
              </vxe-column>
              <!-- <vxe-column title="计划比率" field="plan_order_delivery" width="80" />
              <vxe-column title="实际比率" field="order_delivery" width="80" />
              <vxe-column title="得分" field="orderDeliveryScore" width="80">
                <template #default="{ row }">
                  {{
                    row?.plan_order_delivery && row?.order_delivery
                      ? (30 * Math.min(row?.order_delivery / row?.plan_order_delivery, 1)).toFixed(
                          2
                        )
                      : '0.00'
                  }}
                </template>
              </vxe-column> -->
            </vxe-colgroup>
            <!-- <vxe-colgroup title="扣分项" field="service_deducts">
              <vxe-column title="满意度" field="service_satisfaction" min-width="80" />
              <vxe-column title="扣分" field="service_deduct" min-width="80">
                <template #default="{ row }">
                  {{ 100 - row?.service_satisfaction }}
                </template>
              </vxe-column>
            </vxe-colgroup> -->
            <vxe-column title="总分" field="performanceScore" min-width="80">
              <template #default="{ row }">
                {{
                  (
                    Number(getFlowScope) +
                    (row?.plan_roi && row?.roi ? 40 * Math.min(row?.roi / row?.plan_roi, 1) : 0) +
                    // (row?.plan_order_delivery && row?.order_delivery
                    //   ? 30 * Math.min(row?.order_delivery / row?.plan_order_delivery, 1)
                    //   : 0)
                    -(100 - row?.service_satisfaction)
                  ).toFixed(2)
                }}
              </template>
            </vxe-column>
          </vxe-table>
          <vxe-toolbar ref="toolbarRef1" export size="mini" />
          <vxe-table
            height="200px"
            align="center"
            border
            :data="informationList"
            :header-cell-style="{ padding: 0 }"
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            show-overflow
            stripe
            ref="tableRef1"
            :export-config="{}"
            :cell-class-name="cellClassName"
            :header-cell-class-name="cellHeaderClassName"
          >
            <vxe-colgroup title="基础信息" field="baseInfo" key="baseInfo">
              <vxe-column title="年" field="year" width="60" key="baseInfo" />
              <vxe-column title="月" field="month" width="60" key="baseInfo" />
              <vxe-column title="人员" field="personType" width="60" key="baseInfo">
                <template #default="{ row }">
                  {{ getDictLabel('information_person', row.personType) }}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="IT项目及时交付率" field="deliveryRate">
              <vxe-column title="基础分" field="basePerformance" width="80">
                <template #default> 60 </template>
              </vxe-column>
              <vxe-column title="负责流程数" field="flowCount" width="80">
                <template #default="{ row }">
                  <el-link type="primary" @click="showFlowDetails(row, false)">{{
                    row.flowCount
                  }}</el-link>
                </template>
              </vxe-column>
              <vxe-column title="按期完成数" field="flowFinishCount" width="80">
                <template #default="{ row }">
                  <el-link type="primary" @click="showFlowDetails(row, true)">{{
                    row.flowFinishCount
                  }}</el-link>
                </template>
              </vxe-column>
              <vxe-column title="交付率" field="flowRate" width="80">
                <template #default="{ row }">
                  <span v-if="row.flowCount">
                    {{ ((row.flowFinishCount / row.flowCount) * 100).toFixed(2) }}%
                  </span>
                  <span v-else>100.00%</span>
                </template>
              </vxe-column>
              <vxe-column title="得分" field="flowScore" width="80">
                <template #default="{ row }">
                  {{ row.flowCount ? ((row.flowFinishCount / row.flowCount) * 60).toFixed(2) : 60 }}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="领导评分" field="process">
              <vxe-column title="基础分" field="processBasePerformance" width="80">
                <template #default> 20 </template>
              </vxe-column>
              <vxe-column title="领导评分" field="leaderScore" width="100" />
              <vxe-column title="评估记录" field="record" width="100">
                <template #default="{ row }">
                  <el-button type="primary" link @click="openDetail(row)"> 详情 </el-button>
                </template>
              </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup title="工单服务评分" field="flow">
              <vxe-column title="及时性" field="timeliness" width="80">
                <template #default="{ row }">
                  {{ (row.timeliness || 0).toFixed(1) }}
                </template>
              </vxe-column>
              <vxe-column title="服务态度" field="attitude" width="80">
                <template #default="{ row }">
                  {{ (row.attitude || 0).toFixed(1) }}
                </template>
              </vxe-column>
              <vxe-column title="系统稳定性" field="stability" width="80">
                <template #default="{ row }">
                  {{ (row.stability || 0).toFixed(1) }}
                </template>
              </vxe-column>
              <vxe-column title="整体评分" field="whole" width="80">
                <template #default="{ row }">
                  {{ (row.whole || 0).toFixed(1) }}
                </template>
              </vxe-column>
            </vxe-colgroup>
            <!-- <vxe-colgroup title="扣分项" field="deducts">
              <vxe-column title="黑花数" field="blackFlowerCount" min-width="100" />
              <vxe-column title="扣分" field="deduct" min-width="100">
                <template #default="{ row }">
                  {{ row.blackFlowerCount * 2 }}
                </template>
              </vxe-column>
            </vxe-colgroup> -->
            <vxe-column title="总分" field="performanceScore" min-width="100" fixed="right">
              <template #default="{ row }">
                {{
                  (
                    (row.flowCount ? (row.flowFinishCount / row.flowCount) * 60 : 60) +
                    (row.leaderScore || 0) -
                    row.blackFlowerCount * 2 +
                    row.timeliness +
                    row.attitude +
                    row.stability +
                    row.whole
                  ).toFixed(2)
                }}
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </ContentWrap>
    <Dialog v-model="flowDetailsVisible" title="负责任务详情" width="50%">
      <vxe-table :data="flowDetails" show-overflow>
        <vxe-column title="流程名称" field="flowName" min-width="300">
          <template #default="{ row }">
            <el-link type="primary" @click="toFlowDetails(row.flowId)">{{ row.flowName }}</el-link>
          </template>
        </vxe-column>
        <vxe-column title="流程创建时间" field="flowCreateTime" width="160">
          <template #default="{ row }">
            {{ formatToDateTime(row.flowCreateTime) }}
          </template>
        </vxe-column>
        <vxe-column title="流程期望完成时间" field="flowPlanDate" width="160">
          <template #default="{ row }">
            {{ formatToDate(row.flowPlanDate) }}
          </template>
        </vxe-column>
        <vxe-column title="流程实际完成时间" field="flowPublishTime" width="160">
          <template #default="{ row }">
            {{
              row.developmentDate || row.flowPublishTime
                ? formatToDateTime(row.developmentDate || row.flowPublishTime)
                : ''
            }}
          </template>
        </vxe-column>
      </vxe-table>
    </Dialog>
    <Dialog
      :title="`${moment(informationDate).format('YYYY年MM月')}评估记录`"
      v-model="visible"
      width="800px"
    >
      <el-form class="custom-form" label-position="top">
        <el-form-item label="人员">
          信息部 - {{ getDictLabel('information_person', tempRow?.personType) }}
        </el-form-item>
        <el-form-item label="本月主要工作成果及自评">
          <el-input
            type="textarea"
            :rows="5"
            v-model="tempRow.selfEvaluation"
            :disabled="!checkPermi(['report:information:self-assessment'])"
            autosize
          />
        </el-form-item>
        <el-form-item label="领导评价">
          <el-input
            type="textarea"
            :rows="5"
            v-model="tempRow.leaderEvaluation"
            :disabled="!checkPermi(['report:information:leader-assessment'])"
            autosize
          />
        </el-form-item>
        <el-form-item label="分数">
          <el-input-number
            :min="0"
            :max="40"
            v-model="tempRow.leaderScore"
            class="!w-100%"
            :disabled="!checkPermi(['report:information:leader-assessment'])"
          />
        </el-form-item>
        <el-form-item label="附件">
          <upload-file
            v-model="tempRow.attachmentIds"
            :show-file-list="false"
            :is-show-tip="false"
            plain
            result-type="id"
            @success="updateInformation"
            v-if="checkPermi(['report:information:save'])"
          />
          <div
            v-for="attachment in tempRow.attachments"
            :key="attachment.id"
            class="flex justify-between w-full"
          >
            <el-button
              type="primary"
              link
              @click="
                downloadByUrl({ url: attachment.url, target: '_blank', fileName: attachment.name })
              "
            >
              {{ attachment.name }}
            </el-button>
            <el-button
              type="danger"
              link
              @click="deleteFile(attachment.id)"
              v-if="attachment.creator === getUser.id"
              >X</el-button
            >
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button
          type="primary"
          plain
          @click="updateInformation"
          v-hasPermi="['report:information:save']"
          >保存</el-button
        >
      </template>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { DeptIndicatorApi } from '@/api/hr/deptindicator'
import { getStrDictOptions, getDictLabel } from '@/utils/dict'
import * as echarts from 'echarts'
import { markRaw } from 'vue'
import { downloadByUrl } from '@/utils/filt'
import { checkPermi } from '@/utils/permission'
import { useUserStore } from '@/store/modules/user'
import { formatToDate, formatToDateTime } from '@/utils/dateUtil'
import { FlowApi } from '@/api/report/ekp/flow'

const queryParams = reactive({
  dept: 'IT',
  year: moment().format('YYYY')
})
const informationDate = ref(moment().format('YYYY-MM-DD'))

const { getUser } = useUserStore()

const getFlowScope = computed(() => {
  if (!informationList.value || informationList.value.length === 0) return '0'

  const sum = informationList.value.reduce((acc, row) => {
    const { timeliness, attitude, stability, whole } = row
    return acc + timeliness + attitude + stability + whole
  }, 0)
  return ((sum / informationList.value.length / 20) * 40).toFixed(2) // 保留两位小数
})

const itList = ref<any[]>([])
const informationList = ref<any[]>([])
const toolbarRef = ref()
const tableRef = ref()
const toolbarRef1 = ref()
const tableRef1 = ref()
const tempRow = ref<any>()
const visible = ref(false)
const message = useMessage()
const itLeader = ref<any[]>([])
const itLeaderMap = ref<any>({})
const flowDetails = ref<any[]>([])
const flowDetailsVisible = ref(false)

// 图表实例存储
const chartInstances = ref<Map<string, any>>(new Map())

// 部门指标数据接口
interface DeptIndicatorData {
  id: number
  dept: string
  year: number
  month: number
  indicator: string
  value: number
}
const onItList = async () => {
  const res = await DeptIndicatorApi.getDeptIndicatorList(queryParams)
  itList.value = res
  // 数据加载完成后渲染图表
  await nextTick()
  renderCharts()
}

const onInformationList = async () => {
  const res = await DeptIndicatorApi.getInformationList({
    year: moment(informationDate.value).format('YYYY'),
    month: moment(informationDate.value).format('M')
  })
  informationList.value = res
  itLeader.value = await DeptIndicatorApi.getDeptIndicatorList({
    year: moment(informationDate.value).format('YYYY'),
    month: moment(informationDate.value).format('M'),
    dept: 'IT'
  })
  console.log(itLeaderMap.value)
  // 转换数据格式
  itLeaderMap.value = itLeader.value.reduce(
    (acc, item) => {
      acc[item.indicator] = item.value
      return acc
    },
    {} as Record<string, number>
  )
}

const showFlowDetails = async (row: any, overdue: boolean) => {
  const res = await FlowApi.getFlowList({
    dateType: 'month',
    date: informationDate.value,
    person: row.personType,
    overdue: overdue
  })
  flowDetails.value = res
  flowDetailsVisible.value = true
}

const toFlowDetails = (flowId: string) => {
  window.open(
    'http://oa.iaa360.cn:8686/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + flowId,
    '_blank'
  )
}

const changeDeptIndicator = async (row: any) => {
  const data = {
    year: moment(informationDate.value).format('YYYY'),
    month: moment(informationDate.value).format('M'),
    dept: 'IT',
    indicator: 'boss',
    value: row.boss
  }
  await DeptIndicatorApi.updateDeptLeader(data)
  message.success('保存成功')
  onInformationList()
}

// 处理数据，按指标分组
const processDataByIndicator = () => {
  const result: Record<string, Record<string, number>> = {}

  // 获取所有指标
  const indicators = getStrDictOptions('IT_Indicator')

  // 初始化结果结构
  indicators.forEach((indicator) => {
    result[indicator.value] = {}
  })

  // 处理每条指标数据
  itList.value.forEach((item: DeptIndicatorData) => {
    if (!item.indicator || !item.month || item.value === undefined) return

    const indicatorKey = item.indicator
    const monthKey = `${item.month}月`

    if (!result[indicatorKey]) {
      result[indicatorKey] = {}
    }

    // 累加同一月份的指标值（如果有多条记录）
    if (result[indicatorKey][monthKey]) {
      result[indicatorKey][monthKey] += Number(item.value)
    } else {
      result[indicatorKey][monthKey] = Number(item.value)
    }
  })

  return result
}

// 渲染所有图表
const renderCharts = () => {
  const processedData = processDataByIndicator()
  const indicators = getStrDictOptions('IT_Indicator')

  indicators.forEach((indicator) => {
    renderIndicatorChart(indicator.value, indicator.label, processedData[indicator.value] || {})
  })
}

// 渲染单个指标的图表
const renderIndicatorChart = (
  indicatorId: string,
  indicatorName: string,
  monthlyData: Record<string, number>
) => {
  const containerId = indicatorId
  const container = document.getElementById(containerId)

  if (!container) {
    console.warn(`Container ${containerId} not found`)
    return
  }

  // 销毁已存在的图表实例
  if (chartInstances.value.has(containerId)) {
    chartInstances.value.get(containerId).dispose()
  }

  // 准备图表数据
  const months = Object.keys(monthlyData).sort((a, b) => {
    // 按月份数字排序
    const monthA = parseInt(a.replace('月', ''))
    const monthB = parseInt(b.replace('月', ''))
    return monthA - monthB
  })
  const values = months.map((month) => monthlyData[month] || 0)

  // 如果没有数据，显示空状态
  if (months.length === 0) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">${indicatorName}</div>
          <div class="text-sm mt-2">暂无数据</div>
        </div>
      </div>
    `
    return
  }

  // 创建ECharts实例
  const chart = markRaw(echarts.init(container))

  const option = {
    title: {
      text: indicatorName,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const param = params[0]
        return `${param.axisValue}<br/>${param.marker}${indicatorName}: ${param.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        fontSize: 11,
        color: '#666',
        rotate: 0
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '指标值',
      nameTextStyle: {
        color: '#666',
        fontSize: 11
      },
      axisLabel: {
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: indicatorName,
        type: 'bar',
        data: values,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4facfe' },
            { offset: 1, color: '#00f2fe' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 10,
          color: '#666'
        }
      }
    ]
  }

  chart.setOption(option)
  chartInstances.value.set(containerId, chart)
}

// 清理所有图表实例
const disposeAllCharts = () => {
  chartInstances.value.forEach((chart) => {
    chart.dispose()
  })
  chartInstances.value.clear()
}

// 监听窗口大小变化，调整所有图表
const handleResize = () => {
  chartInstances.value.forEach((chart) => {
    chart.resize()
  })
}

const cellClassName = ({ column }) => {
  if (
    [
      'year',
      'month',
      'personType',
      'processBasePerformance',
      'leaderScore',
      'record',
      'performanceScore',
      'plan_roi',
      'roi',
      'roiScore',
      'service_deducts',
      'service_deduct',
      'service_satisfaction',
      'deducts',
      'blackFlowerCount',
      'deduct'
    ].includes(column.field)
  ) {
    return 'basics-info'
  } else {
    return 'basics-info-1'
  }
}

const cellHeaderClassName = ({ column }) => {
  if (
    [
      'baseInfo',
      'process',
      'year',
      'month',
      'personType',
      'processBasePerformance',
      'leaderScore',
      'record',
      'performanceScore',
      'plan_roi',
      'roi',
      'roiScore',
      'service_deducts',
      'service_deduct',
      'service_satisfaction',
      'deducts',
      'blackFlowerCount',
      'deduct'
    ].includes(column.field)
  ) {
    return 'basics-info-header'
  } else {
    return 'basics-info-header-1'
  }
}

const openDetail = (row: any) => {
  visible.value = true
  tempRow.value = row
}

const updateInformation = async () => {
  // console.log(tempRow.value)
  await DeptIndicatorApi.updateInformation(tempRow.value)
  message.success('修改成功')
  await onInformationList()
  tempRow.value = informationList.value.find((item) => item.id === tempRow.value.id)
}

const deleteFile = async (attachmentId: number) => {
  // 替换整个数组的响应式写法
  tempRow.value.attachmentIds = [...tempRow.value.attachmentIds.filter((id) => id !== attachmentId)]
  updateInformation()
}

onMounted(() => {
  onItList()
  window.addEventListener('resize', handleResize)
  onInformationList()

  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
    unref(tableRef1)?.connect(unref(toolbarRef1))
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeAllCharts()
})
</script>

<style scoped>
/* 图表容器样式 */
.h-200px {
  position: relative;
  overflow: hidden;
}

/* 空状态样式 */
.flex {
  height: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .h-200px {
    height: 180px !important;
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .h-200px {
    height: 160px !important;
  }
}

/* 图表标题样式优化 */
[id] {
  transition: all 0.3s ease;
}

[id]:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

:deep(.basics-info) {
  background-color: #d9d9d9 !important;
}

:deep(.basics-info-1) {
  background-color: #f2f2f2 !important;
}

:deep(.basics-info-header) {
  background-color: #808080 !important;
  color: #fff;
}

:deep(.basics-info-header-1) {
  background-color: #a0a0a0 !important;
  color: #fff;
}

:deep(.el-dialog) {
  :deep(.el-form-item__label) {
    font-weight: bold;
  }
  :deep(.el-form-item__content) {
    background-color: #eaedf5;
    border-radius: 2px;
    padding: 0 2px;
  }

  :deep(.el-textarea__inner),
  :deep(.el-input__inner) {
    box-shadow: none;
    background-color: #eaedf5;
    border-radius: 2px;
  }

  :deep(.el-input__wrapper) {
    padding: 1px 32px;
  }

  :deep(.el-dialog__body) {
    max-height: 600px;
    overflow: auto;
  }
}

:deep(.el-dialog.is-fullscreen) {
  :deep(.el-dialog__body) {
    height: calc(100vh - 120px);
    max-height: calc(100vh - 120px) !important;
  }
}

:deep(.vxe-table--body-inner-wrapper) {
  min-height: auto !important;
  height: auto !important;
}
</style>
