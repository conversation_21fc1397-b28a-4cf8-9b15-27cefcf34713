<template>
  <ContentWrap>
    <div class="h-[calc(100vh-140px)]">
      <vxe-toolbar size="small" custom ref="toolbarRef">
        <template #buttons>
          <el-radio-group
            v-model="queryParams.approveDateType"
            size="small"
            @change="handleDateTypeChange"
          >
            <el-radio-button label="年" value="year" />
            <el-radio-button label="月" value="month" />
            <el-radio-button label="周" value="week" />
            <el-radio-button label="范围" value="daterange" />
          </el-radio-group>
          <div class="ml-10px">
            <el-date-picker
              :class="[queryParams.approveDateType === 'daterange' ? '!w-240px' : '!w-120px']"
              :type="queryParams.approveDateType"
              value-format="YYYY-MM-DD"
              v-model="queryParams.approveDate"
              size="small"
              unlink-panels
              :format="dateFormart"
              :clearable="false"
              @change="handleList"
            />
          </div>
          <el-popover
            ref="popoverRef"
            width="500"
            placement="bottom-start"
            trigger="click"
            :teleported="false"
            :visible="fillterShow"
          >
            <template #reference>
              <el-button
                type="primary"
                plain
                size="small"
                class="ml-10px"
                v-click-outside="handleClickOutside"
                @click="fillterShow = true"
              >
                筛选
                <Icon icon="ep:filter" />
              </el-button>
            </template>
            <el-form label-position="top" class="custom-form" size="small">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="隐藏已完成">
                    <el-switch
                      v-model="queryParams.hasUnComplete"
                      class="ml-10px"
                      :active-value="true"
                      :inactive-value="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="BOM计划晚于排产">
                    <el-switch v-model="queryParams.hasBomAfterScheduling" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="订单BOM颜色筛选">
                    <el-select
                      v-model="queryParams.bomColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="订单审核颜色筛选">
                    <el-select
                      v-model="queryParams.approvalColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="排产颜色筛选">
                    <el-select
                      v-model="queryParams.schedulingColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="齐套颜色筛选">
                    <el-select
                      v-model="queryParams.completeSetColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="组装颜色筛选">
                    <el-select
                      v-model="queryParams.assemblyColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="包装颜色筛选">
                    <el-select
                      v-model="queryParams.packingColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="入库颜色筛选">
                    <el-select
                      v-model="queryParams.rcvColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="出货颜色筛选">
                    <el-select
                      v-model="queryParams.shipColor"
                      :teleported="false"
                      multiple
                      collapse-tags
                    >
                      <el-option
                        v-for="(item, index) in ColorStatus"
                        :label="item.label"
                        :value="item.value"
                        :key="index"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div class="text-right mt-10px">
              <el-button type="warning" size="small" @click="handleCancel"> 重置查询 </el-button>
              <el-button type="primary" size="small" @click="handleConfirm">
                保存条件并查询
              </el-button>
            </div>
          </el-popover>
          <el-button type="primary" plain size="small" class="ml-10px" @click="handleList"
            >刷新数据</el-button
          >
        </template>
        <template #tools>
          <div v-if="jobList?.[0]?.endTime" class="mr-10px">
            数据同步时间：{{ formatToDateTime(jobList?.[0]?.endTime) }}
          </div>
          <el-popover :visible="editVisible" placement="bottom-end" :width="450" trigger="click">
            <template #reference>
              <el-button circle class="mr-10px" @click="openEditPopover">
                <Icon icon="ep:setting" />
              </el-button>
            </template>
            <el-form
              label-width="250px"
              size="small"
              :disabled="!checkPermi(['order:tracking:edit-save'])"
            >
              <el-form-item label="订单审核计划日期 = 业务下单日期 +">
                <el-input-number v-model="editFormData.approveDeviation" />
                &nbsp;&nbsp;&nbsp;&nbsp;天
              </el-form-item>
              <el-form-item label="排产计划日期 = 订单审核日期 +">
                <el-input-number v-model="editFormData.schedulingDeviation" />
                &nbsp;&nbsp;&nbsp;&nbsp;天
              </el-form-item>
              <el-form-item label="物料齐套计划日期 = 工单最早开工日期 +">
                <el-input-number v-model="editFormData.completeSetDeviation" />
                &nbsp;&nbsp;&nbsp;&nbsp;天
              </el-form-item>
              <el-form-item label="入库计划日期 = 最早成品入库单创建日期 +">
                <el-input-number v-model="editFormData.rcvDeviation" /> &nbsp;&nbsp;&nbsp;&nbsp;天
              </el-form-item>
              <el-form-item label="出货计划日期 = 实际入库日期 +">
                <el-input-number v-model="editFormData.shipDeviation" /> &nbsp;&nbsp;&nbsp;&nbsp;天
              </el-form-item>
            </el-form>
            <div class="text-right" v-hasPermi="['order:tracking:edit-save']">
              <el-button size="small" @click="editVisible = false">关闭</el-button>
              <el-button type="primary" size="small" @click="saveEdit">保存</el-button>
            </div>
          </el-popover>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-100px)]">
        <vxe-table
          id="order-tracking-table"
          :header-cell-config="{ height: 24 }"
          :header-cell-style="{ fontSize: '13px' }"
          :cell-config="{ height: 24 }"
          :data="list"
          :loading="loading"
          :cell-style="cellStyle"
          :custom-config="customConfig"
          :filter-config="{ remote: true }"
          :sort-config="{ remote: true, multiple: true }"
          :column-config="{ resizable: true, maxFixedSize: 0 }"
          :row-style="rowStyle"
          :row-config="{ isCurrent: true, isHover: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          size="small"
          height="100%"
          show-overflow
          border
          align="center"
          ref="tableRef"
          @filter-change="handleFilterChange"
          @sort-change="sortChangeEvent"
          @cell-click="
            (el) => {
              let tab = ''
              if (el.column.field.includes('bom')) {
                tab = 'bom'
              } else if (el.column.field.includes('scheduling')) {
                tab = 'scheduling'
              } else if (el.column.field.includes('completeSet')) {
                tab = 'completeSet'
              } else if (el.column.field.includes('assembly')) {
                tab = 'assembly'
              } else if (el.column.field.includes('packing')) {
                tab = 'packing'
              } else if (el.column.field.includes('rcv')) {
                tab = 'rcv'
              } else if (el.column.field.includes('ship')) {
                tab = 'ship'
              } else {
                tab = 'bom'
              }
              orderTrackingDetailFormRef?.openForm(el.row, tab)
            }
          "
        >
          <vxe-colgroup title="订单信息" field="order-info">
            <vxe-column
              title="业务员"
              field="sellerName"
              width="100"
              :filters="filterValue.sellerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="客户"
              field="customerName"
              width="100"
              :filters="filterValue.customerName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="订单号"
              field="docNo"
              width="130"
              :filters="filterValue.docNo"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="品号"
              field="itemCode"
              width="90"
              :filters="filterValue.itemCode"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="品名"
              field="itemName"
              width="100"
              :filters="filterValue.itemName"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="形态属性"
              field="attribute"
              width="100"
              :filters="AttribureTypeFilter"
            >
              <template #default="{ row }">
                {{ AttributeType[row?.attribute]?.label }}
              </template>
            </vxe-column>
            <vxe-column
              title="规格"
              field="spec"
              width="200"
              :filters="filterValue.spec"
              :filter-render="FilterTemplate.textFilterRender"
            />
            <vxe-column
              title="下单数量"
              field="qty"
              width="110"
              sortable
              :filters="filterValue.qty"
              :filter-render="FilterTemplate.numberFilterRender"
            />
            <vxe-column title="下单审核日期" field="approveDate" width="120" />
            <vxe-column
              title="预出货日期"
              field="planShipDate"
              width="120"
              :filters="filterValue.planShipDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
            />
          </vxe-colgroup>
          <vxe-colgroup title="订单BOM-盘德波" field="order-bom">
            <vxe-column
              title="计划"
              field="bomPlanDate"
              width="100"
              :filters="filterValue.bomPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{ content: '当系统内有定制数据时，取BOM定制跟进计划完成时间' }"
            />
            <vxe-column
              title="实际"
              field="bomActualDate"
              width="100"
              :filters="filterValue.bomActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content:
                  '当系统内有定制数据时，取BOM定制跟进实际完成时间，无定制取ERP中BOM更新时间，成品BOM需满足完整性条件'
              }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="订单审核-陆卿" field="order-approve">
            <vxe-column
              title="计划"
              field="approvePlanDate"
              width="100"
              :filters="filterValue.approvePlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{ content: '取订单下单时间+偏移天数(可点击右侧齿轮图标查看)' }"
            />
            <vxe-column
              title="实际"
              field="approveActualDate"
              width="100"
              :filters="filterValue.approveActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content: '取最新订单实际审核时间，即打回重新审核也会取最新的审核时间'
              }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="订单排产-刘思宇" field="order-scheduling">
            <vxe-column
              title="计划"
              field="schedulingPlanDate"
              width="100"
              :filters="filterValue.schedulingPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{ content: '取订单审核日期+偏移天数(可点击右侧齿轮图标查看)' }"
            />
            <vxe-column
              title="实际"
              field="schedulingActualDate"
              width="100"
              :filters="filterValue.schedulingActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{ content: '取工单累计数量大于等于下单数量时，最晚的开工日期' }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="物料齐套-伍丽丽" field="order-complete-set">
            <vxe-column
              title="计划"
              field="completeSetPlanDate"
              width="100"
              :filters="filterValue.completeSetPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{ content: '取工单最早开工时间+偏移天数(可点击右侧齿轮图标查看)' }"
            />
            <vxe-column
              title="实际"
              field="completeSetActualDate"
              width="100"
              :filters="filterValue.completeSetActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content: '取所有工单备料表中，推式物料已全部领料时，最后一张领料单的审核日期'
              }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="生产组装-张兵古" field="order-assembly">
            <vxe-column
              title="计划"
              field="assemblyPlanDate"
              width="100"
              :filters="filterValue.assemblyPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content:
                  '取标准工时表，（当前型号单台标准工时X下单数量/每日11小时工作周期）+物料实际齐套时间'
              }"
            />
            <vxe-column
              title="实际"
              field="assemblyActualDate"
              width="100"
              :filters="filterValue.assemblyActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content:
                  '取生产报工数据，实际组装、组包、组包膜工序累计数量大于等于下单数量的最后报工日期'
              }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="生产包装-张兵古" field="order-packing">
            <vxe-column
              title="计划"
              field="packingPlanDate"
              width="100"
              :filters="filterValue.packingPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content:
                  '取标准工时表，（当前型号单台标准工时X下单数量/每日11小时工作周期）+物料实际齐套时间'
              }"
            />
            <vxe-column
              title="实际"
              field="packingActualDate"
              width="100"
              :filters="filterValue.packingActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content:
                  '取生产报工数据，实际包装、组包、组包膜工序累计数量大于等于下单数量的最后报工日期'
              }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="入库-邹家超" field="order-rcv">
            <vxe-column
              title="计划"
              field="rcvPlanDate"
              width="100"
              :filters="filterValue.rcvPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content: '取最早成品入库单创建时间+偏移天数(可点击右侧齿轮图标查看)'
              }"
            />
            <vxe-column
              title="实际"
              field="rcvActualDate"
              width="100"
              :filters="filterValue.rcvActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content: '取完工入库单完工入库数量大于等于下单数量时，最后一张完工入库单的审核时间'
              }"
            />
          </vxe-colgroup>
          <vxe-colgroup title="出货-陆卿" field="order-ship">
            <vxe-column
              title="计划"
              field="shipPlanDate"
              width="100"
              :filters="filterValue.shipPlanDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{ content: '取实际入库时间+偏移天数(可点击右侧齿轮图标查看)' }"
            />
            <vxe-column
              title="实际"
              field="shipActualDate"
              width="100"
              :filters="filterValue.shipActualDate"
              :filter-render="FilterTemplate.dateRangeFilterRender"
              :title-suffix="{
                content: '取出货单出货数量大于等于下单数量时，最后一张出货单的审核时间'
              }"
            />
          </vxe-colgroup>
        </vxe-table>
      </div>
      <div class="flex">
        <div class="w-50% flex h-50px items-center">
          <div class="flex ml-10px">
            <div class="bg-[var(--el-color-success-light-3)] w-20px h-20px"></div>
            <span class="ml-10px">正常</span>
          </div>
          <div class="flex ml-10px">
            <div class="bg-[var(--el-color-warning-light-3)] w-20px h-20px"></div>
            <span class="ml-10px">预警</span>
          </div>
          <div class="flex ml-10px">
            <div class="bg-#e60012 w-20px h-20px"></div>
            <span class="ml-10px">超期</span>
          </div>
          <div class="flex ml-10px">
            <div class="bg-#ee7959 w-20px h-20px"></div>
            <span class="ml-10px">超期完成</span>
          </div>
        </div>
        <div class="w-50%">
          <!-- 分页 -->
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
            size="small"
          />
        </div>
      </div>
    </div>
    <OrderTrackingDetailForm ref="orderTrackingDetailFormRef" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { OrderTrackingApi } from '@/api/report/erp/order-tracking'
import { checkPermi } from '@/utils/permission'
import * as FilterTemplate from '@/utils/Filter'
import moment from 'moment'
import { customConfig } from '@/utils/vxeCustom'
import { cloneDeep } from 'lodash-es'
import OrderTrackingDetailForm from './OrderTrackingDetailForm.vue'
import { formatToDateTime } from '@/utils/dateUtil'
import * as JobLogApi from '@/api/infra/jobLog'
import { ClickOutside as vClickOutside } from 'element-plus'
import { useCache } from '@/hooks/web/useCache'
import { copyValueToTarget } from '@/utils'

const editVisible = ref(false)
const editFormData = ref({
  id: undefined,
  approveDeviation: undefined,
  schedulingDeviation: undefined,
  completeSetDeviation: undefined,
  rcvDeviation: undefined,
  shipDeviation: undefined
})

const ColorStatus = [
  { label: '正常-绿色，白色', value: '10' },
  { label: '预警-橙色', value: '11' },
  { label: '超期-红色', value: '12' },
  { label: '超期完成-橙红', value: '13' }
]

const AttributeType = {
  '10': { label: '制造件', value: '10' },
  '9': { label: '采购件', value: '9' },
  '4': { label: '委外件', value: '4' },
  '6': { label: '虚拟件', value: '6' }
}

const AttribureTypeFilter = computed(() => Object.values(AttributeType))

const filterValue = ref({
  sellerName: [{ data: '' }],
  customerName: [{ data: '' }],
  docNo: [{ data: '' }],
  itemCode: [{ data: '' }],
  itemName: [{ data: '' }],
  spec: [{ data: '' }],
  qty: [{ data: { condition: '10', value: undefined } }],
  bomPlanDate: [{ data: [] }],
  bomActualDate: [{ data: [] }],
  approvePlanDate: [{ data: [] }],
  approveActualDate: [{ data: [] }],
  schedulingPlanDate: [{ data: [] }],
  schedulingActualDate: [{ data: [] }],
  completeSetPlanDate: [{ data: [] }],
  completeSetActualDate: [{ data: [] }],
  assemblyPlanDate: [{ data: [] }],
  assemblyActualDate: [{ data: [] }],
  packingPlanDate: [{ data: [] }],
  packingActualDate: [{ data: [] }],
  rcvPlanDate: [{ data: [] }],
  rcvActualDate: [{ data: [] }],
  shipPlanDate: [{ data: [] }],
  shipActualDate: [{ data: [] }],
  planShipDate: [{ data: [] }]
})

const queryParams = ref<any>({
  pageNo: 1,
  pageSize: 100,
  approveDateType: 'year',
  approveDate: moment().format('YYYY-MM-DD') as any,
  sorting: [] as any[],
  hasUnComplete: false,
  hasBomAfterScheduling: false,
  bomColor: undefined,
  approvalColor: undefined,
  schedulingColor: undefined,
  completeSetColor: undefined,
  assemblyColor: undefined,
  packingColor: undefined,
  rcvColor: undefined,
  shipColor: undefined
})
const dateFormart = ref('YYYY年')
const loading = ref(false)
const list = ref<any[]>([])
const total = ref(0)

const toolbarRef = ref()
const tableRef = ref()
const orderTrackingDetailFormRef = ref()

const message = useMessage()
/** 显示配置信息弹窗 */
const openEditPopover = async () => {
  editVisible.value = !editVisible.value
  if (!editVisible.value) return
  const res = await OrderTrackingApi.getOrderTrackingEdit()
  if (res) {
    editFormData.value = res
  }
}
/** 保存配置信息 */
const saveEdit = async () => {
  await OrderTrackingApi.saveOrderTrackingEdit(editFormData.value)
  message.success('保存成功')
  editVisible.value = false
  handleList()
}

/** 获取数据列表 */
const getList = async () => {
  loading.value = true
  try {
    const cloneQueryParams = cloneDeep(queryParams.value)
    if (
      queryParams.value.approveDate instanceof Array &&
      queryParams.value.approveDate.length > 0
    ) {
      cloneQueryParams.approveDate =
        queryParams.value.approveDate[0] + ',' + queryParams.value.approveDate[1]
    }
    const res = await OrderTrackingApi.getOrderTrackingPage(cloneQueryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
/** 重置列表 */
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

/** 单元格颜色设置 */
const cellStyle: any = ({ row, column }) => {
  let PlanDate = undefined as any
  let ActualDate = undefined as any
  let Actual = undefined as any
  if (column.field.includes('Plan')) {
    if (!row[column.field] && !row[column.field.replace('Plan', 'Actual')]) {
      return {}
    }
    PlanDate = row[column.field] ? moment(row[column.field]) : undefined
    ActualDate = row[column.field.replace('Plan', 'Actual')]
      ? moment(row[column.field.replace('Plan', 'Actual')])
      : moment()
    Actual = row[column.field.replace('Plan', 'Actual')]
  } else if (column.field.includes('Actual')) {
    if (!row[row[column.field.replace('Actual', 'Plan')]] && !row[column.field]) {
      return {}
    }
    PlanDate = row[column.field.replace('Actual', 'Plan')]
      ? moment(row[column.field.replace('Actual', 'Plan')])
      : undefined
    ActualDate = row[column.field] ? moment(row[column.field]) : moment()
    Actual = row[column.field]
  } else {
    return {}
  }
  if (!PlanDate && Actual) {
    return {
      backgroundColor: 'var(--el-color-success-light-3)',
      color: '#ffffff',
      cursor: 'pointer'
    }
  }
  const PlanStart = PlanDate.startOf('day')
  const ActualStart = ActualDate.startOf('day')

  const daysDiff = PlanStart.diff(ActualStart, 'days')
  if (!Actual) {
    // 当前日期与计划完成日期比较
    if (daysDiff >= 2) {
      return {
        cursor: 'pointer'
      }
    } else if (daysDiff >= 0) {
      return {
        backgroundColor: 'var(--el-color-warning-light-3)',
        color: '#ffffff',
        cursor: 'pointer'
      }
    } else {
      return {
        backgroundColor: '#e60012',
        color: '#ffffff',
        cursor: 'pointer'
      }
    }
  } else {
    // 实际完成日期与计划完成日期比较
    if (daysDiff >= 0) {
      return {
        backgroundColor: 'var(--el-color-success-light-3)',
        color: '#ffffff',
        cursor: 'pointer'
      }
    } else {
      return {
        backgroundColor: '#ee7959',
        color: '#ffffff',
        cursor: 'pointer'
      }
    }
  }
}
const rowStyle: any = ({ row }) => {
  // const hasBomActual = row['bomActualDate']
  // const bomDate = hasBomActual ? moment(row['bomActualDate']) : moment(row['bomPlanDate'])
  // const schedulingPlanDate = row['schedulingPlanDate'] ? moment(row['schedulingPlanDate']) : null

  // let isBomAfterScheduling = false
  // if (bomDate && schedulingPlanDate) {
  //   if (schedulingPlanDate && schedulingPlanDate.isBefore(bomDate)) {
  //     isBomAfterScheduling = true
  //   }
  // }

  // if (isBomAfterScheduling) {
  //   return {
  //     backgroundColor: 'var(--el-color-danger-light-3) !important',
  //     cursor: 'pointer'
  //   }
  // }
  if (row['shipActualDate']) {
    return {
      backgroundColor: 'var(--el-color-success-light-7) !important',
      cursor: 'pointer'
    }
  }

  return { cursor: 'pointer' }
}
/** 日期范围筛选 */
const handleDateTypeChange = () => {
  switch (queryParams.value.approveDateType) {
    case 'year':
      dateFormart.value = 'YYYY年'
      break
    case 'month':
      dateFormart.value = 'YYYY年MM月'
      break
    case 'week':
      dateFormart.value = 'YYYY年ww周'
      break
    case 'daterange':
      dateFormart.value = 'YYYY年MM月DD日'
      queryParams.value.approveDate = []
      break
  }
  if (queryParams.value.approveDateType !== 'daterange') {
    queryParams.value.approveDate = moment().format('YYYY-MM-DD')
    handleList()
  }
}

// 远程筛选方法
const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['orgName', 'property', 'currency', 'attribute']

  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams.value).forEach((key) => {
    if (
      ![
        'pageNo',
        'pageSize',
        'sorting',
        'approveDate',
        'approveDateType',
        'hasUnComplete'
      ].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams.value[key] = undefined
    }
  })
  // 更新 queryParams
  Object.assign(queryParams.value, filters)

  // 调用后端接口获取数据
  handleList()
}
// 远程排序方法
const sortChangeEvent: any = ({ field, order }) => {
  if (!order) {
    queryParams.value.sorting = queryParams.value.sorting.filter((item) => item.field != field)
  } else {
    const item = queryParams.value.sorting.find((item) => item.field == field)
    if (item) {
      item.order = order
    } else {
      queryParams.value.sorting.push({ field, order })
    }
  }
  handleList()
}

const jobList = ref<any[]>([])

const onJobList = async () => {
  const res = await JobLogApi.getJobLogPage({
    pageNo: 1,
    pageSize: 10,
    jobId: 50
  })
  jobList.value = res.list
}

const popoverRef = ref()

// 重置
const handleCancel = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 100,
    approveDateType: 'year',
    approveDate: moment().format('YYYY-MM-DD') as any,
    sorting: [] as any[],
    hasUnComplete: false,
    hasBomAfterScheduling: false,
    bomColor: undefined,
    approvalColor: undefined,
    schedulingColor: undefined,
    completeSetColor: undefined,
    assemblyColor: undefined,
    packingColor: undefined,
    rcvColor: undefined,
    shipColor: undefined
  }
  wsCache.delete('order-tracking-query')
  handleList()
  fillterShow.value = false
}

const { wsCache } = useCache()

// 确定
const handleConfirm = () => {
  // 关闭弹窗
  fillterShow.value = false

  const queryConditoins = cloneDeep(queryParams.value)
  delete queryConditoins['pageNo']
  delete queryConditoins['pageSize']
  delete queryConditoins['sorting']
  delete queryConditoins['approveDateType']
  delete queryConditoins['approveDate']

  wsCache.set('order-tracking-query', queryConditoins)
  // 调用接口
  handleList()
}
const fillterShow = ref(false)
const handleClickOutside = (event: { target: any }) => {
  const popoverContent = popoverRef.value?.popperRef?.contentRef
  if (fillterShow.value && !popoverContent?.contains(event.target)) {
    fillterShow.value = false
  }
}

onMounted(() => {
  const queryConditoins = wsCache.get('order-tracking-query')
  if (queryConditoins) {
    copyValueToTarget(queryParams.value, queryConditoins)
  }
  getList()
  onJobList()

  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin: 5px !important;
}

:deep(.el-form-item__label) {
  margin-bottom: 5px !important;
}
</style>
