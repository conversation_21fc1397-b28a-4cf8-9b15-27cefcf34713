<template>
  <el-row class="h-[calc(100vh-120px)]" :gutter="10">
    <el-col :span="5" :xs="24" class="h-100%">
      <ContentWrap>
        <div class="head-container">
          <el-input v-model="categoryName" class="mb-10px" clearable placeholder="请输入分类名称">
            <template #prefix>
              <Icon icon="ep:search" />
            </template>
          </el-input>
        </div>
        <div class="head-container h-[calc(100vh-120px-52px)] overflow-auto">
          <el-tree
            ref="treeRef"
            :data="categoryList"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :props="defaultProps"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span>{{ `${node.label}【${data.code}】` }}</span>
            </template>
          </el-tree>
        </div>
      </ContentWrap>
    </el-col>
    <el-col :span="19" :xs="24">
      <ContentWrap>
        <div class="h-[calc(100vh-130px-45px)]">
          <vxe-table
            height="100%"
            align="center"
            show-overflow
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :column-config="{ resizable: true }"
            v-if="currentRow?.id"
            border
            stripe
            :data="itemList"
            :loading="loading"
            :menu-config="{
              header: { options: [[{ code: 'refresh', name: '清空筛选' }]] },
              body: { options: [[{ code: 'refresh', name: '清空筛选' }]] }
            }"
            @menu-click="menuClickEvent"
          >
            <vxe-column title="品号" field="itemCode" width="120">
              <template #header>
                <div>品号</div>
                <el-input
                  size="small"
                  v-model="queryParams.itemCode"
                  clearable
                  @change="onFilterOnChange"
                />
              </template>
            </vxe-column>
            <vxe-column title="版本" field="itemVar" width="80">
              <template #header>
                <div>版本</div>
                <el-input
                  size="small"
                  v-model="queryParams.itemVar"
                  clearable
                  @change="onFilterOnChange"
                />
              </template>
            </vxe-column>
            <vxe-column title="品名" field="itemName" width="160" align="left">
              <template #header>
                <div>品名</div>
                <el-input
                  size="small"
                  v-model="queryParams.itemName"
                  clearable
                  @change="onFilterOnChange"
                />
              </template>
            </vxe-column>
            <vxe-column title="型号" field="model" width="80">
              <template #header>
                <div>型号</div>
                <el-input
                  size="small"
                  v-model="queryParams.model"
                  clearable
                  @change="onFilterOnChange"
                />
              </template>
              <template #default="{ row }">
                <el-link
                  type="primary"
                  link
                  @click="onClickChange({ prop: 'model', type: 'input' }, row.model)"
                >
                  {{ row.model }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              v-for="(column, index) in attributeList"
              :key="index"
              :title="column.label"
              :field="column.prop"
              :width="column.prop === 'PARTFIXEDBACK2' ?'auto':'120px'"
              :align="column.prop === 'PARTFIXEDBACK2' ? 'left' : 'center'"
            >
              <template #header>
                <div>{{ column.label }}</div>
                <el-input
                  v-model="queryParams[column.prop]"
                  v-if="column.type == 'input'"
                  size="small"
                  clearable
                  @change="onFilterOnChange"
                />
                <el-popconfirm
                  v-else
                  title="确认筛选？"
                  placement="top"
                  trigger="click"
                  :visible="activePopconfirm === column.prop"
                  @confirm="onConfirmFilter(column.prop)"
                  @cancel="onCancelFilter(column.prop)"
                >
                  <template #reference>
                    <el-select
                      v-model="queryParams[column.prop]"
                      size="small"
                      clearable
                      multiple
                      collapse-tags
                      @change="(value) => onSelectChange(column.prop, value)"
                    >
                      <el-option
                        v-for="dict in attributeMap[column.prop]"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value == '空' ? '' : dict.value"
                      />
                    </el-select>
                  </template>
                </el-popconfirm>
              </template>
              <template #default="{ row }">
                <el-link type="primary" link @click="onClickChange(column, row[column.prop])">
                  {{ row[column.prop] }}
                </el-link>
              </template>
            </vxe-column>
          </vxe-table>
          <el-empty description="请选择左侧成品分类" v-else />
        </div>
        <Pagination
          :total="total"
          size="small"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="onItemList"
        />
      </ContentWrap>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { defaultProps } from '@/utils/tree'
import { AttributeApi } from '@/api/butt-joint/plm/attribute'

const categoryList = ref<any[]>([])
const categoryName = ref('')
const treeRef = ref()

const currentRow = ref<any>({})
const attributeList = ref<any[]>([])

const attributeMap = ref<any>({})

const itemList = ref<any[]>([])

const queryParams = ref({
  pageNo: 1,
  pageSize: 100,
  itemCode: undefined,
  itemVar: undefined,
  itemName: undefined,
  model: undefined
})
const total = ref(0)

const loading = ref(false)

// 控制哪个 popconfirm 显示
const activePopconfirm = ref<string | null>(null)

/** 获取分类属性 */
const onListAttributeList = async () => {
  if (!currentRow.value?.id) return
  const res = await AttributeApi.getAttributeList(currentRow.value.id)
  attributeList.value = res
  queryParams.value = {
    pageNo: 1,
    pageSize: 100,
    itemCode: undefined,
    itemVar: undefined,
    itemName: undefined,
    model: undefined
  }
  activePopconfirm.value = null
  for (const column of res) {
    queryParams.value[column.prop] = undefined
  }
}
/** 获取属性字典值 */
const onListDict = async () => {
  const res = await AttributeApi.getAttributeDictList({
    id: currentRow.value.id,
    code: currentRow.value.code,
    ...queryParams.value
  })
  attributeMap.value = res.reduce(
    (acc, item) => {
      const { key, value } = item
      // 如果 key 不存在，初始化数组；否则将新对象加入数组
      acc[key] = [...(acc[key] || []), { label: value, value }]
      return acc
    },
    {} as Record<string, Array<{ label: string; value: string }>>
  )
}

const onItemList = async () => {
  loading.value = true
  try {
    const res = await AttributeApi.getItemList({
      id: currentRow.value.id,
      code: currentRow.value.code,
      ...queryParams.value
    })
    itemList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const onFilterOnChange = () => {
  queryParams.value.pageNo = 1
  onItemList()
  onListDict()
}

const onClickChange = (column: any, value: any) => {
  if (column.type === 'dict') {
    queryParams.value[column.prop] = [value]
  } else {
    queryParams.value[column.prop] = value
  }
  onFilterOnChange()
}

/** 处理 select 值改变 */
const onSelectChange = (prop: string, value: any) => {
  // 显示对应的 popconfirm
  activePopconfirm.value = prop
}

/** 确认筛选 */
const onConfirmFilter = (prop: string) => {
  // 隐藏 popconfirm
  activePopconfirm.value = null
  // 执行筛选
  onFilterOnChange()
}

/** 取消筛选 */
const onCancelFilter = (prop: string) => {
  // 恢复原始值
  queryParams.value[prop] = undefined
  // 隐藏 popconfirm
  activePopconfirm.value = null
}

/** 获取物料分类 */
const onListCategory = async () => {
  const res = await AttributeApi.getCategoryList()
  categoryList.value = res
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理分类被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  currentRow.value = row
  await onListAttributeList()
  onItemList()
  onListDict()
}

const menuClickEvent = ({ menu }) => {
  switch (menu.code) {
    case 'refresh':
      for (const key in queryParams.value) {
        if (['pageNo', 'pageSize'].includes(key)) continue
        queryParams.value[key] = undefined
      }
      onItemList()
      onListDict()
      break
  }
}

/** 监听deptName */
watch(categoryName, (val) => {
  treeRef.value!.filter(val)
})

onMounted(() => {
  onListCategory()
})
</script>

<style lang="scss" scoped>
:deep(.vxe-header--column .vxe-cell) {
  padding: 1px 1px 2px 1px !important;
}
:deep(.vxe-cell--title) {
  .el-input__wrapper,
  .el-select__wrapper {
    box-shadow: none;
    border-radius: 0;
  }
}
</style>
