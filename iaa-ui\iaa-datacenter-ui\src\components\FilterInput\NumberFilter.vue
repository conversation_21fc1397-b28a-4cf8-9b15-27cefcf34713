<template>
  <div v-if="currOption" class="my-filter-input" id="number-filter">
    <el-select
      v-model="currOption.data.condition"
      :clearable="false"
      class="!w-80px"
      :append-to="render"
    >
      <el-option label=">" value="10" />
      <el-option label="<" value="11" />
      <el-option label="=" value="12" />
      <el-option label=">=" value="13" />
      <el-option label="<=" value="14" />
      <el-option label="!=" value="15" />
    </el-select>
    <el-input-number
      mode="text"
      v-model="currOption.data.value"
      placeholder="支持回车筛选"
      @keyup.enter="keyupEvent"
      @input="changeOptionEvent"
    />
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { watch, ref, computed } from 'vue'
import type { VxeTableDefines } from 'vxe-table'

const props = defineProps({
  renderParams: propTypes.any.def({})
})

const render = ref<any>()

const currOption = ref<VxeTableDefines.FilterOption>()

const currField = computed(() => {
  const { column } = props.renderParams || {}
  return column ? column.field : ''
})

const load = () => {
  nextTick(() => {
    render.value = document.getElementById('number-filter') as any
  })

  const { renderParams } = props
  if (renderParams) {
    const { column } = renderParams
    const option = column.filters[0]
    currOption.value = option
  }
}

const changeOptionEvent = () => {
  const { renderParams } = props
  const option = currOption.value
  if (renderParams && option) {
    const { $table } = renderParams
    const checked = !!option.data
    $table.updateFilterOptionStatus(option, checked)
  }
}

const keyupEvent = ($event) => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.confirmFilterEvent($event)
  }
}

watch(currField, () => {
  load()
})

load()
</script>

<style scoped>
.my-filter-input {
  padding: 10px;
  position: relative;
}
</style>
