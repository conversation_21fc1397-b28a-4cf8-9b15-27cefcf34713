<template>
  <NoModalDrawer v-model="visiable" size="50%">
    <template #header>
      <div class="header-box w-full">
        <div class="pb-10px text-#000 text-16px font-bold">
          {{ `订单：${formData?.docNo} - 品号：${formData?.itemCode}` }}
        </div>
        <el-form label-width="90" class="header-form" size="small">
          <el-row>
            <el-col :span="8">
              <el-form-item label="品名：">{{ formData?.itemName }}</el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="规格：">{{ formData?.spec }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="下单日期：">{{ formData?.orderDate }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审核日期：">{{ formData?.approveDate }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务员：">{{ formData?.sellerName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="客户名称：">{{ formData?.customerName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="数量：">{{ formData?.qty }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-tabs v-model="currentTab" @tab-change="onTabChange">
          <el-tab-pane label="订单BOM" name="bom" />
          <el-tab-pane label="排产" name="scheduling" />
          <el-tab-pane label="物料齐套" name="completeSet" />
          <el-tab-pane label="生产组装" name="assembly" />
          <el-tab-pane label="生产包装" name="packing" />
          <el-tab-pane label="生产入库" name="rcv" />
          <el-tab-pane label="出货" name="ship" />
        </el-tabs>
      </div>
    </template>
    <template v-if="currentTab === 'bom'">
      <el-empty description="该订单行暂无定制BOM信息" v-if="!formData?.bomTaskId" />
      <el-form label-width="140" label-position="top" v-else>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="定制包材类">
              <el-tag type="success" v-if="!bomTaskData.packing || bomTaskData?.packing == '99'">
                无定制
              </el-tag>
              <DetailDate :data="bomTaskData.packingDetail" v-else />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定制Logo类">
              <el-tag type="success" v-if="!bomTaskData.logo || bomTaskData?.logo == '99'">
                无定制
              </el-tag>
              <DetailDate :data="bomTaskData.logoDetail" v-else />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定制说明书类">
              <el-tag
                type="success"
                v-if="!bomTaskData.instruction || bomTaskData?.instruction == '99'"
              >
                无定制
              </el-tag>
              <DetailDate :data="bomTaskData.instructionDetail" v-else />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定制程序类">
              <el-tag type="success" v-if="!bomTaskData.program || bomTaskData?.program == '99'">
                无定制
              </el-tag>
              <DetailDate :data="bomTaskData.programDetail" v-else />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定制结构类">
              <el-tag
                type="success"
                v-if="!bomTaskData.structure || bomTaskData?.structure == '99'"
              >
                无定制
              </el-tag>
              <DetailDate :data="bomTaskData.structureDetail" v-else />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM计划完成日期">
              {{ bomTaskData?.planBomCompleteDate }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM计划完成变更日期">
              {{ bomTaskData?.planBomCompleteDateChange }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际完成日期">
              {{ bomTaskData?.actualCompleteDate }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template v-if="currentTab === 'scheduling'">
      <el-empty description="该订单行暂无工单信息" v-if="moData.length === 0" />
      <template v-else>
        <CardTitle title="排产进度" />
        <el-progress
          status="success"
          :percentage="calculatePercentage(moData, (item) => Number(item.qty || 0), formData?.qty)"
          show-text
          :stroke-width="20"
          text-inside
        />
        <br />
        <CardTitle title="排产记录" />

        <div class="h-[calc(100vh-300px)]">
          <vxe-table
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :footer-cell-config="{ height: 30 }"
            :footer-cell-style="{
              backgroundColor: 'var(--el-color-warning-light-9)',
              color: 'var(--el-color-primary)',
              fontWeight: 'bold'
            }"
            :data="moData"
            max-height="100%"
            show-footer
            :footer-method="(el) => footerMethod(el, ['qty'])"
            show-overflow
            show-footer-overflow
            align="center"
          >
            <vxe-column title="工单号" field="docNo" />
            <vxe-column title="开工日期" field="startDate" />
            <vxe-column title="状态" field="status" />
            <vxe-column title="数量" field="qty" />
          </vxe-table>
        </div>
      </template>
    </template>
    <template v-if="currentTab === 'completeSet'">
      <el-empty description="该订单行暂无工单信息" v-if="moPickData.length === 0" />
      <template v-else>
        <CardTitle title="领料进度" />
        <el-progress
          status="success"
          :percentage="calculateRatioPercentage(moPickData, 'issuedQty', 'actualReqQty')"
          show-text
          :stroke-width="20"
          text-inside
        />
        <br />
        <CardTitle title="领料记录" />
        <div class="h-[calc(100vh-300px)]">
          <vxe-table
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :footer-cell-config="{ height: 30 }"
            :footer-cell-style="{
              backgroundColor: 'var(--el-color-warning-light-9)',
              color: 'var(--el-color-primary)',
              fontWeight: 'bold'
            }"
            :virtual-y-config="{
              enabled: true
            }"
            :data="moPickData"
            max-height="100%"
            show-footer
            :footer-method="(el) => footerMethod(el, ['actualReqQty', 'issuedQty'])"
            show-overflow
            show-footer-overflow
            align="center"
            border
          >
            <vxe-column title="工单号" field="docNo" width="140" align="left" />
            <vxe-column title="开工日" field="startDate" width="90" />
            <vxe-column title="工单状态" field="status" width="90" />
            <!-- <vxe-column title="工单数量" field="qty" /> -->
            <vxe-column title="需求物料" field="itemCode" width="90" />
            <vxe-column title="需求数量" field="actualReqQty" width="90" />
            <vxe-column title="已领数量" field="issuedQty" width="90" />
            <vxe-column title="领料单号" field="issueDocNo" min-width="90" align="left" />
            <vxe-column title="最后领料日" field="issueApproveDate" width="100" />
          </vxe-table>
        </div>
      </template>
    </template>
    <template v-if="['assembly', 'packing'].includes(currentTab)">
      <el-empty description="该订单行暂无报工信息" v-if="productionData.length === 0" />
      <template v-else>
        <CardTitle :title="`${currentTab === 'assembly' ? '组装' : '包装'}进度`" />
        <el-progress
          status="success"
          :percentage="
            calculatePercentage(
              productionData,
              (item) => Number(item.hoursReportNum || 0),
              formData?.qty
            )
          "
          show-text
          :stroke-width="20"
          text-inside
        />
        <br />
        <CardTitle :title="`${currentTab === 'assembly' ? '组装' : '包装'}记录`" />
        <div class="h-[calc(100vh-300px)]">
          <vxe-table
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :footer-cell-config="{ height: 30 }"
            :footer-cell-style="{
              backgroundColor: 'var(--el-color-warning-light-9)',
              color: 'var(--el-color-primary)',
              fontWeight: 'bold'
            }"
            :virtual-y-config="{
              enabled: true
            }"
            :data="productionData"
            max-height="100%"
            show-footer
            :footer-method="(el) => footerMethod(el, ['hoursReportNum'])"
            show-overflow
            show-footer-overflow
            align="center"
            border
          >
            <vxe-column title="报工日期" field="dateStr" width="90" />
            <vxe-column title="时间段" field="productionTime" width="90" />
            <vxe-column title="销售订单号" field="salesOrderCode" min-width="90" />
            <vxe-column title="工单号" field="productionOrderCode" min-width="90" />
            <vxe-column title="品号" field="productNo" width="90" />
            <vxe-column title="工序" field="workTypeName" width="90" />
            <vxe-column title="报工数量" field="hoursReportNum" width="90" />
            <vxe-column title="产线" field="lineName" width="100" />
          </vxe-table>
        </div>
      </template>
    </template>
    <template v-if="currentTab === 'rcv'">
      <el-empty description="该订单行暂无成品入库单信息" v-if="completeData.length === 0" />
      <template v-else>
        <CardTitle title="入库进度" />
        <el-progress
          status="success"
          :percentage="
            calculatePercentage(completeData, (item) => Number(item.qty || 0), formData?.qty)
          "
          show-text
          :stroke-width="20"
          text-inside
        />
        <br />
        <CardTitle title="入库记录" />

        <div class="h-[calc(100vh-300px)]">
          <vxe-table
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :footer-cell-config="{ height: 30 }"
            :footer-cell-style="{
              backgroundColor: 'var(--el-color-warning-light-9)',
              color: 'var(--el-color-primary)',
              fontWeight: 'bold'
            }"
            :data="completeData"
            max-height="100%"
            show-footer
            :footer-method="(el) => footerMethod(el, ['qty'])"
            show-overflow
            show-footer-overflow
            align="center"
          >
            <vxe-column title="入库单号" field="docNo" />
            <vxe-column title="审核日期" field="startDate" />
            <vxe-column title="状态" field="status" />
            <vxe-column title="数量" field="qty" />
          </vxe-table>
        </div>
      </template>
    </template>
    <template v-if="currentTab === 'ship'">
      <el-empty description="该订单行暂无出货单信息" v-if="shipData.length === 0" />
      <template v-else>
        <CardTitle title="出货进度" />
        <el-progress
          status="success"
          :percentage="
            calculatePercentage(shipData, (item) => Number(item.qty || 0), formData?.qty)
          "
          show-text
          :stroke-width="20"
          text-inside
        />
        <br />
        <CardTitle title="出货记录" />

        <div class="h-[calc(100vh-300px)]">
          <vxe-table
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :footer-cell-config="{ height: 30 }"
            :footer-cell-style="{
              backgroundColor: 'var(--el-color-warning-light-9)',
              color: 'var(--el-color-primary)',
              fontWeight: 'bold'
            }"
            :data="shipData"
            max-height="100%"
            show-footer
            :footer-method="(el) => footerMethod(el, ['qty'])"
            show-overflow
            show-footer-overflow
            align="center"
          >
            <vxe-column title="出货单号" field="docNo" />
            <vxe-column title="审核日期" field="startDate" />
            <vxe-column title="状态" field="status" />
            <vxe-column title="数量" field="qty" />
          </vxe-table>
        </div>
      </template>
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { CustomerApi } from '@/api/report/erp/order-bom'
import { OrderTrackingApi } from '@/api/report/erp/order-tracking'
import DetailDate from '@/views/engineering/order-bom/componets/DetailDate.vue'

const visiable = ref(false)
const formData = ref<any>({})
const currentTab = ref('bom')
const bomTaskData = ref<any>({})
const moData = ref<any[]>([])
const moPickData = ref<any[]>([])
const productionData = ref<any[]>([])
const completeData = ref<any[]>([])
const shipData = ref<any[]>([])

const onTabChange = async () => {
  await nextTick()
  if (['rcv', 'ship'].includes(currentTab.value) && ['9','4'].includes(formData.value.attribute)) {
    if (currentTab.value === 'rcv') {
      completeData.value = await OrderTrackingApi.getOutDetails(
        formData.value.docNo,
        formData.value.itemCode
      )
    } else {
      shipData.value = await OrderTrackingApi.getOutDetails(
        formData.value.docNo,
        formData.value.itemCode
      )
    }
    return
  }
  switch (currentTab.value) {
    case 'bom':
      if (formData.value?.bomTaskId) {
        bomTaskData.value = await CustomerApi.getTask(formData.value.bomTaskId)
      }
      break
    case 'scheduling':
      moData.value = await OrderTrackingApi.getMoDetails(formData.value.demand)
      break
    case 'completeSet':
      moPickData.value = await OrderTrackingApi.getMoPickDetails(formData.value.demand)
      break
    case 'assembly':
    case 'packing':
      productionData.value = await OrderTrackingApi.getProductionDetails(
        currentTab.value === 'assembly' ? 1 : 0,
        formData.value.docNo,
        formData.value.itemCode
      )
      break
    case 'rcv':
      completeData.value = await OrderTrackingApi.getCompleteDetails(formData.value.demand)
      break
    case 'ship':
      shipData.value = await OrderTrackingApi.getShipDetails(formData.value.demand)
      break
  }
}

const sumMethod = (list: any[], field: string) => {
  let num = 0
  list.forEach((row) => {
    num += Number(row[field])
  })
  return num
}

const footerMethod: any = ({ columns, data }, fields: string[]) => {
  return [
    columns.map((column, index) => {
      if (index === 0) {
        return '合计'
      }
      if (fields.includes(column.field)) {
        return sumMethod(data, column.field)
      }
      return '-'
    })
  ]
}

// 提取通用的进度计算方法
const calculatePercentage = (
  data: any[],
  getValue: (item: any) => number,
  total: number
): number => {
  if (!data || data.length === 0 || total === 0) {
    return 0
  }

  const current = data.reduce((sum, item) => sum + getValue(item), 0)
  return Math.min(100, Math.floor((current / total) * 100))
}

// 计算两个字段之间的比率
const calculateRatioPercentage = (
  data: any[],
  numeratorField: string,
  denominatorField: string
): number => {
  if (!data || data.length === 0) {
    return 0
  }

  const numerator = data.reduce((sum, item) => sum + Number(item[numeratorField] || 0), 0)
  const denominator = data.reduce((sum, item) => sum + Number(item[denominatorField] || 0), 0)

  if (denominator === 0) return 0
  return Math.min(100, Math.floor((numerator / denominator) * 100))
}

const openForm = (row: any, tab: string) => {
  visiable.value = true
  formData.value = row
  currentTab.value = tab
  onTabChange()
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.header-form .el-form-item) {
  margin-bottom: 5px !important;
}

:deep(.header-form .el-form-item__content) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-form-item__content) {
  background-color: #f9f5f8 !important;
  border-radius: 5px;
  padding: 0 5px;
  cursor: pointer;
}

:deep(.el-progress-bar__inner),
:deep(.el-progress-bar__outer) {
  border-radius: 0 !important;
}
:deep(.el-drawer__header) {
  margin-bottom: 0px !important;
}
</style>
