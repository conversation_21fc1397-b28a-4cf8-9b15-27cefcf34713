<template>
  <ContentWrap>
    <x-table
      :data="dataList"
      :total="total"
      :columns="columns"
      :page="queryParams"
      @search="onSerchPageData"
      @pagination="(page) => onSerchPageData(page, true)"
      stripe
      highlight-current
      v-loading="loading"
      height="calc(100vh - 350px)"
    >
      <template #erpUserCode="{ row }">
        <el-select v-model="row.erpUserCode" filterable size="small">
          <el-option
            v-for="item in erpEmployee"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </template>
      <template #erpSellerCode="{ row }">
        <el-select v-model="row.erpSellerCode" filterable size="small" collapse-tags multiple>
          <el-option
            v-for="item in erpEmployee"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" size="small" link @click="saveData(row)"> 保存 </el-button>
      </template>
    </x-table>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as UserApi from '@/api/butt-joint/ekuaibao/user'

const dataList = ref<any[]>([])
const total = ref(0)
const columns = ref<TableColumn[]>([])
const erpSeller = ref<any[]>([])
const erpEmployee = ref<any[]>([])
const loading = ref(false)
const message= useMessage()
const { t } = useI18n()
const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})
/** 查询表格数据 */
const onSerchPageData = (row: any, hasPage?: boolean) => {
  for (let key in row) {
    if (row[key]) {
      queryParams.value[key] = row[key]
    } else if (queryParams.value.hasOwnProperty(key)) {
      delete queryParams.value[key]
    }
  }
  if (!hasPage) {
    queryParams.value.pageNo = 1
  }
  onPageData()
}
/** 查询数据 */
const onPageData = async () => {
  loading.value = true
  try {
    const res = await UserApi.page(queryParams.value)
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 查询列信息及ERP 用户字典 */
const initUserData = async () => {
  const resColumns = await UserApi.getTableColumn()
  columns.value = resColumns
  const resErpSeller = await UserApi.getErpAllSeller()
  erpSeller.value = resErpSeller
  const resErpEmployee = await UserApi.getErpAllEmployee()
  erpEmployee.value = resErpEmployee
}

const saveData = async (row:any) =>{
  loading.value=true;
  try{
    await UserApi.save(row)
    message.success(t('common.updateSuccess'))
  }finally{
    loading.value=false;
  }
}

onMounted(() => {
  initUserData()
  onPageData()
})
</script>

