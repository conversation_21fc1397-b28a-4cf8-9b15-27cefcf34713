<template>
  <Dialog title="定义工艺" v-model="visiable">
    <el-form
      :model="formData"
      :rules="formRules"
      label-width="100"
      ref="formRef"
      v-loading="loading"
    >
      <el-form-item label="工艺编码" prop="code">
        <el-input v-model="formData.code" :maxlength="64" show-word-limit :disabled="formData.id" />
      </el-form-item>
      <el-form-item label="工艺名称" prop="name">
        <el-input v-model="formData.name" :maxlength="255" show-word-limit />
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="6"
          :maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="saveOrUpdate">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DefineApi } from '@/api/report/technology/define'
const visiable = ref(false)
const formRef = ref()
const loading = ref(false)
const message = useMessage()
const emits = defineEmits(['success'])

const formData = ref({
  id: undefined,
  code: undefined,
  name: undefined,
  remarks: undefined
})

const formRules = reactive({
  code: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
})

const refresh = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    name: undefined,
    remarks: undefined
  }
}

const saveOrUpdate = async () => {
  await unref(formRef)?.validate()
  loading.value = true
  try {
    await DefineApi.saveOrUpdate(formData.value)
    message.success('保存成功')
    emits('success')
    refresh()
    visiable.value = false
  } finally {
    loading.value = false
  }
}

const openForm = (row?: any) => {
  refresh()
  visiable.value = true
  if (row) {
    formData.value = row
  }
}

defineExpose({
  openForm
})
</script>
