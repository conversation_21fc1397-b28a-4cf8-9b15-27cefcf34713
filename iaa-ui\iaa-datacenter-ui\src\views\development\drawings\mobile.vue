<template>
  <div class="mobile-drawings-container">
    <!-- 顶部搜索栏 -->
    <van-sticky :offset-top="40">
      <div class="search-header">
        <van-search v-model="searchValue" :placeholder="currentSearchType.placeholder" show-action>
          <template #label>
            <van-button type="primary" size="small" @click="showSearchTypeSelector = true">
              {{ currentSearchType.label }}
              <van-icon name="arrow-down" />
            </van-button>
          </template>
          <template #action>
            <van-button
              type="primary"
              size="small"
              @click="onSearch"
              :disabled="!searchValue.trim()"
            >
              搜索
            </van-button>
          </template>
        </van-search>
      </div>

      <!-- 快速筛选栏 -->
      <div class="quick-filter-bar">
        <van-tabs
          v-model:active="quickFilterActive"
          type="card"
          @change="onQuickFilterChange"
          shrink
        >
          <van-tab v-for="type in quickFilterTypes" :key="type.value" :title="type.label" />
        </van-tabs>
      </div>
      <div class="quick-filter-bar">
        <!-- 分类选择 -->
        <van-field
          v-model="activeCategory"
          is-link
          readonly
          label="物料分类"
          placeholder="选择后按分类筛选"
          @click="activitiesShow = true"
        />
        <van-popup v-model:show="activitiesShow" round position="bottom">
          <van-cascader
            v-model="activeCascaderCategory"
            title="选择后按分类筛选"
            :options="treeSelectItems"
            :field-names="{
              text: 'kindName',
              value: 'id',
              children: 'children'
            }"
            @close="activitiesShow = false"
            @finish="onCheckCategory"
          />
          <div class="flex justify-around">
            <van-button
              type="danger"
              size="small"
              @click="
                () => {
                  activeCascaderCategory = ''
                  activeCategory = ''
                  activitiesShow = false
                  onCategoryList()
                }
              "
              >清空</van-button
            >
          </div>
        </van-popup>
      </div>
    </van-sticky>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 搜索结果统计 -->
      <div v-if="showSearchStats" class="search-stats">
        <van-notice-bar :text="searchStatsText" mode="closeable" @close="showSearchStats = false">
          <template #left-icon>
            <van-icon name="info-o" />
          </template>
        </van-notice-bar>
      </div>

      <!-- 物料列表 -->
      <div class="material-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <van-card
              v-for="item in list"
              :key="item.itemCode"
              :title="item.itemName"
              :desc="item.spec"
              @click="handleItemClick(item)"
            >
              <template #tags>
                <van-tag type="primary" size="medium">{{ item.itemCode }}</van-tag>
                <van-tag type="success" size="medium" v-if="item.itemVersion">
                  {{ item.itemVersion }}
                </van-tag>
              </template>
            </van-card>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 详情弹出层 -->
    <van-popup
      v-model:show="showDetail"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
    >
      <div class="detail-container">
        <div class="detail-header">
          <h3>{{ selectedItem?.itemName }}</h3>
          <p class="item-code">编码: {{ selectedItem?.itemCode }}</p>
        </div>

        <van-tabs v-model:active="activeTab">
          <van-tab title="基本信息">
            <div class="basic-info">
              <van-cell-group>
                <van-cell title="物料编码" :value="selectedItem?.itemCode" />
                <van-cell title="原件号" :value="selectedItem?.itemCode1" />
                <van-cell title="版本" :value="selectedItem?.itemVersion" />
                <van-cell title="物料名称" :value="selectedItem?.itemName" />
                <van-cell title="属性" :value="selectedItem?.attribute" />
                <van-cell title="规格" :value="selectedItem?.spec" />
              </van-cell-group>
            </div>
          </van-tab>

          <van-tab title="相关文档">
            <div class="document-list">
              <van-loading v-if="rightLoading" />
              <van-empty v-else-if="!rightList.length" description="暂无相关文档" />
              <div v-else>
                <van-card
                  v-for="doc in rightList"
                  :key="doc.documentCode"
                  :title="`${doc.documentName}.${doc.documentType}`"
                  :desc="doc.documentCode"
                  @click="handleDocClick(doc)"
                >
                  <template #tags>
                    <van-tag
                      :type="doc.status.includes('完成') ? 'success' : 'danger'"
                      size="medium"
                    >
                      {{ doc.status }}
                    </van-tag>
                    <van-tag
                      :type="doc.isSuance.includes('已发放') ? 'success' : 'warning'"
                      size="medium"
                    >
                      {{ doc.isSuance }}
                    </van-tag>
                    <van-tag :type="doc.format === '存在' ? 'success' : 'warning'" size="medium">
                      {{ doc.format }}PDF
                    </van-tag>
                  </template>
                  <template #footer>
                    <van-button
                      size="small"
                      type="primary"
                      @click.stop="previewDocument(doc)"
                      :disabled="!canPreview(doc)"
                    >
                      预览
                    </van-button>
                  </template>
                </van-card>
              </div>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </van-popup>

    <!-- PDF预览弹出层 -->
    <van-popup
      v-model:show="showPdfPreview"
      position="bottom"
      :style="{ height: '90%' }"
      round
      closeable
    >
      <div class="pdf-preview-container">
        <div class="pdf-header">
          <h4>{{ documentName }}</h4>
          <van-button size="small" type="primary" icon="down" @click="handleDownload">
            下载
          </van-button>
        </div>
        <div class="pdf-content">
          <pdf-preview :pdfUrl="pdfPreviewUrl" />
        </div>
      </div>
    </van-popup>

    <!-- 悬浮操作按钮 -->
    <van-floating-bubble axis="xy" icon="eye-o" @click="showLogPanel = true" />

    <!-- 搜索类型选择器 -->
    <van-popup v-model:show="showSearchTypeSelector" position="bottom" round closeable>
      <div class="search-type-container">
        <h3>选择搜索类型</h3>
        <van-cell-group>
          <van-cell
            v-for="type in searchTypes"
            :key="type.value"
            :title="type.label"
            :label="type.description"
            is-link
            @click="selectSearchType(type)"
            :class="{ 'selected-type': currentSearchType.value === type.value }"
          >
            <template #icon>
              <van-icon :name="type.icon" />
            </template>
            <template #right-icon>
              <van-icon
                v-if="currentSearchType.value === type.value"
                name="success"
                color="#1989fa"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>

    <!-- 高级搜索弹出层 -->
    <van-popup
      v-model:show="showAdvancedSearch"
      position="bottom"
      :style="{ height: '60%' }"
      round
      closeable
    >
      <div class="advanced-search-container">
        <h3>高级搜索</h3>
        <van-form @submit="onAdvancedSearch">
          <van-field
            v-model="advancedSearchForm.itemCode"
            name="itemCode"
            label="物料编码"
            placeholder="请输入物料编码"
            clearable
          />
          <van-field
            v-model="advancedSearchForm.itemName"
            name="itemName"
            label="物料名称"
            placeholder="请输入物料名称"
            clearable
          />
          <van-field
            v-model="advancedSearchForm.spec"
            name="spec"
            label="规格"
            placeholder="请输入规格"
            clearable
          />
          <van-field
            v-model="advancedSearchForm.attribute"
            name="attribute"
            label="属性"
            placeholder="请输入属性"
            clearable
          />
          <div class="advanced-search-actions">
            <van-button type="default" size="large" @click="clearAdvancedSearch" class="action-btn">
              清空
            </van-button>
            <van-button type="primary" size="large" native-type="submit" class="action-btn">
              搜索
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 日志弹出层 -->
    <van-popup
      v-model:show="showLogPanel"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
    >
      <div class="log-container">
        <h3>操作日志</h3>
        <DrawingsLog />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { DrawingApi } from '@/api/development/drawings/index'
import { handleTree } from '@/utils/tree'
import PdfPreview from './componets/PdfPreview.vue'
import DrawingsLog from './componets/DrawingsLog.vue'
import { downloadByUrlBolb } from '@/utils/filt'
import { showToast } from 'vant'

// 搜索相关
const searchValue = ref('')

// 搜索类型配置
const searchTypes = ref([
  {
    value: 'itemCode',
    label: '物料编码',
    description: '按物料编码搜索',
    icon: 'qr-code',
    placeholder: '请输入物料编码'
  },
  {
    value: 'itemName',
    label: '物料名称',
    description: '按物料名称搜索',
    icon: 'label-o',
    placeholder: '请输入物料名称'
  },
  {
    value: 'spec',
    label: '规格',
    description: '按规格搜索',
    icon: 'description',
    placeholder: '请输入规格'
  }
  // ,
  // {
  //   value: 'attribute',
  //   label: '属性',
  //   description: '按属性搜索',
  //   icon: 'tag-o',
  //   placeholder: '请输入属性'
  // },
  // {
  //   value: 'advanced',
  //   label: '高级搜索',
  //   description: '多条件组合搜索',
  //   icon: 'search',
  //   placeholder: '点击进行高级搜索'
  // }
])

// 当前搜索类型
const currentSearchType = ref(searchTypes.value[0])

// 搜索类型选择器显示状态
const showSearchTypeSelector = ref(false)

// 高级搜索相关
const showAdvancedSearch = ref(false)
const advancedSearchForm = reactive({
  itemCode: '',
  itemName: '',
  spec: '',
  attribute: ''
})

// 快速筛选
const quickFilterActive = ref(0)
const quickFilterTypes = ref([
  { value: 'itemCode', label: '编码' },
  { value: 'itemName', label: '名称' },
  { value: 'spec', label: '规格' }
])

// 搜索结果统计
const showSearchStats = ref(false)
const searchStatsText = computed(() => {
  const hasSearchCondition = Object.keys(queryParams).some(
    (key) => !['pageNo', 'pageSize', 'kind'].includes(key) && queryParams[key]
  )

  if (hasSearchCondition) {
    return `找到 ${total.value} 条相关记录`
  }
  return `共 ${total.value} 条记录`
})

// 快速筛选切换
const onQuickFilterChange = (index: number) => {
  const filterType = quickFilterTypes.value[index]

  if (filterType.value === 'all') {
    // 显示全部，清空搜索条件
    searchValue.value = ''
    Object.keys(queryParams).forEach((key) => {
      if (!['pageNo', 'pageSize', 'kind'].includes(key)) {
        queryParams[key] = undefined
      }
    })
    queryParams.pageNo = 1
    finished.value = false
    list.value = []
    getList()
  } else {
    // 切换搜索类型
    const searchType = searchTypes.value.find((t) => t.value === filterType.value)
    if (searchType) {
      currentSearchType.value = searchType
      searchValue.value = ''
    }
  }
}

// 分类选择相关
const activeCategory = ref('')
const activeCascaderCategory = ref('')
const activitiesShow = ref(false)
const treeSelectItems = ref<any[]>([])

// 列表相关
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const list = ref<any[]>([])
const total = ref(0)

// 详情相关
const showDetail = ref(false)
const selectedItem = ref<any>({})
const activeTab = ref(0)
const rightLoading = ref(false)
const rightList = ref<any[]>([])

// PDF预览相关
const showPdfPreview = ref(false)
const pdfPreviewUrl = ref('')
const documentName = ref('')
const documentCode = ref('')

// 日志相关
const showLogPanel = ref(false)

// 查询参数
const queryParams = reactive({
  kind: [] as string[],
  pageNo: 1,
  pageSize: 20
})

const queryRightParams = reactive({
  itemCode: '',
  itemVersion: ''
})

// 搜索功能
const onSearch = () => {
  if (!searchValue.value.trim()) {
    showToast('请输入搜索内容')
    return
  }

  // 设置对应的搜索参数
  if (currentSearchType.value.value !== 'advanced') {
    queryParams[currentSearchType.value.value] = searchValue.value
  }

  // 重置分页
  queryParams.pageNo = 1
  finished.value = false
  list.value = []

  // 执行搜索
  getList()
}

// 选择搜索类型
const selectSearchType = (type: any) => {
  if (type.value === 'advanced') {
    showSearchTypeSelector.value = false
    showAdvancedSearch.value = true
    return
  }

  currentSearchType.value = type
  showSearchTypeSelector.value = false

  // 清空当前搜索值
  searchValue.value = ''
}

// 高级搜索
const onAdvancedSearch = () => {
  const hasSearchTerm = Object.values(advancedSearchForm).some((val) => val.trim())

  if (!hasSearchTerm) {
    showToast('请至少输入一个搜索条件')
    return
  }

  // 设置高级搜索参数
  Object.keys(advancedSearchForm).forEach((key) => {
    if (advancedSearchForm[key].trim()) {
      queryParams[key] = advancedSearchForm[key].trim()
    }
  })

  // 重置分页
  queryParams.pageNo = 1
  finished.value = false
  list.value = []

  // 关闭弹窗并执行搜索
  showAdvancedSearch.value = false
  getList()
}

// 清空高级搜索
const clearAdvancedSearch = () => {
  Object.keys(advancedSearchForm).forEach((key) => {
    advancedSearchForm[key] = ''
  })
}

// 下拉刷新
const onRefresh = () => {
  queryParams.pageNo = 1
  finished.value = false
  getList().finally(() => {
    refreshing.value = false
  })
}

// 加载更多
const onLoad = () => {
  if (finished.value) return

  queryParams.pageNo++
  getList()
}

// 分类选择
const onCheckCategory = (node: any) => {
  const { selectedOptions, value } = node
  activeCategory.value = selectedOptions.map((option) => option.kindName).join('/')
  activeCascaderCategory.value = value
  activitiesShow.value = false
  onCategoryList()
}

const onCategoryList = () => {
  if (activeCascaderCategory.value) {
    queryParams['kind'] = [activeCascaderCategory.value]
  }
  // 重置分页
  queryParams.pageNo = 1
  finished.value = false
  list.value = []

  // 执行搜索
  getList()
}

// 物料项点击
const handleItemClick = (item: any) => {
  selectedItem.value = item
  queryRightParams.itemCode = item.itemCode
  queryRightParams.itemVersion = item.itemVersion
  getRightList()
  showDetail.value = true
}

const viewDetails = (item: any) => {
  handleItemClick(item)
}

// 文档点击
const handleDocClick = (doc: any) => {
  console.log('文档点击:', doc)
}

// 预览文档
const previewDocument = async (doc: any) => {
  if (!canPreview(doc)) {
    showToast('文档无法预览')
    return
  }

  try {
    rightLoading.value = true
    const res = await DrawingApi.getDocPathOrWx({
      methodName: 'getDocPathForWX',
      docId: doc.documentCode,
      docVer: doc.documentVersion,
      docName: doc.documentName,
      uuid: '1'
    })

    if (res && typeof res === 'string') {
      pdfPreviewUrl.value = res
      documentName.value = doc.documentName
      documentCode.value = doc.documentCode
      showPdfPreview.value = true

      // 记录预览日志
      await DrawingApi.addDrawingLog({
        itemCode: queryRightParams.itemCode,
        itemVersion: queryRightParams.itemVersion,
        wordName: doc.documentName,
        wordCode: doc.documentCode,
        type: 0
      })
    } else {
      showToast('获取文件地址失败')
    }
  } catch (error) {
    console.error('预览失败:', error)
    showToast('预览失败，请重试')
  } finally {
    rightLoading.value = false
  }
}

// 判断是否可以预览
const canPreview = (doc: any) => {
  return doc.format === '存在' && doc.isSuance === '已发放' && doc.status === '受控完成'
}

// 下载文档
const handleDownload = async () => {
  try {
    await DrawingApi.addDrawingLog({
      itemCode: queryRightParams.itemCode,
      itemVersion: queryRightParams.itemVersion,
      wordName: documentName.value,
      wordCode: documentCode.value,
      type: 1
    })

    downloadByUrlBolb({
      url: pdfPreviewUrl.value,
      fileName: documentName.value + '.pdf'
    })

    showToast(`文档 "${documentName.value}" 开始下载`)
  } catch (error) {
    showToast('下载失败')
  }
}

// 获取树形数据
const getTreeList = async () => {
  try {
    const res = await DrawingApi.getQueryPartKind()
    const tree = handleTree(res)
    // 转换为 van-tree-select 需要的格式
    treeSelectItems.value = tree.filter((item) => item.id == '1')
  } catch (error) {
    console.error('获取分类失败:', error)
    showToast('获取分类失败')
  }
}

// 获取物料列表
const getList = async () => {
  try {
    loading.value = true
    const res = await DrawingApi.getPage(queryParams)

    if (queryParams.pageNo === 1) {
      list.value = res.list
    } else {
      list.value.push(...res.list)
    }

    total.value = res.total

    // 判断是否还有更多数据
    if (list.value.length >= total.value) {
      finished.value = true
    }

    // 显示搜索统计（仅在有搜索条件时显示）
    const hasSearchCondition = Object.keys(queryParams).some(
      (key) => !['pageNo', 'pageSize', 'kind'].includes(key) && queryParams[key]
    )
    showSearchStats.value = hasSearchCondition && queryParams.pageNo === 1
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败')
  } finally {
    loading.value = false
  }
}

// 获取右侧文档列表
const getRightList = async () => {
  try {
    rightLoading.value = true
    const res = await DrawingApi.getQueryDocumentByItemCode(queryRightParams)
    rightList.value = res
  } catch (error) {
    console.error('查询文档失败:', error)
    showToast('查询文档失败')
  } finally {
    rightLoading.value = false
  }
}

// 监听搜索值变化
watch(searchValue, (val) => {
  // 实现搜索过滤逻辑
  if (val) {
    // 可以在这里实现本地搜索或调用API搜索
  }
})

// 组件挂载时初始化
onMounted(async () => {
  await getTreeList()
  await getList()
})
</script>

<style scoped lang="scss">
:deep(.van-card) {
  background-color: #fff;
}
:deep(.van-card__content) {
  min-height: auto !important;
}
:deep(.van-search__label),
:deep(.van-search__content) {
  padding: 0 !important;
  align-items: center;
}

.mobile-drawings-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.search-header {
  background: white;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 快速筛选栏样式 */
.quick-filter-bar {
  background: white;
  padding: 8px 16px;
  border-bottom: 1px solid #ebedf0;
}

.quick-filter-bar .van-tabs--card .van-tab {
  border: 1px solid #ebedf0;
  background: #f7f8fa;
  color: #646566;
  font-size: 12px;
  padding: 4px 12px;
  margin-right: 8px;
  border-radius: 16px;
}

.quick-filter-bar .van-tabs--card .van-tab--active {
  background: #1989fa;
  color: white;
  border-color: #1989fa;
}

.quick-filter-bar .van-tabs__nav {
  border: none;
  background: transparent;
}

.quick-filter-bar .van-tabs__line {
  display: none;
}

.main-content {
  padding: 8px;
}

/* 搜索结果统计样式 */
.search-stats {
  margin-bottom: 8px;
}

.search-stats .van-notice-bar {
  border-radius: 8px;
  background: #e8f4fd;
  border: 1px solid #b3d8ff;
}

.search-stats .van-notice-bar__content {
  color: #1989fa;
  font-size: 13px;
}

.category-tree {
  max-height: 300px;
  overflow-y: auto;
}

.material-list {
  margin-top: 8px;
}

.material-list .van-card {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.detail-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  margin-bottom: 16px;
  text-align: center;
}

.detail-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.item-code {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.basic-info {
  padding: 16px 0;
}

.document-list {
  padding: 16px 0;
  max-height: 400px;
  overflow-y: auto;
}

.document-list .van-card {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.pdf-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.pdf-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  width: 70%;
  text-emphasis: ellipsis;
  margin-right: 16px;
}

.pdf-content {
  flex: 1;
  border-radius: 8px;
  background: #f5f5f5;
}

.log-container {
  padding: 16px;
  height: 100%;
}

.log-container h3 {
  margin: 0 0 16px 0;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

/* 搜索类型选择器样式 */
.search-type-btn {
  min-width: 80px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 12px;
}

.search-type-container {
  padding: 16px;
}

.search-type-container h3 {
  margin: 0 0 16px 0;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

.selected-type {
  background-color: #f0f8ff;
}

.selected-type .van-cell__title {
  color: #1989fa;
  font-weight: bold;
}

/* 高级搜索样式 */
.advanced-search-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.advanced-search-container h3 {
  margin: 0 0 16px 0;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}

.advanced-search-actions {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  margin-top: auto;
}

.action-btn {
  flex: 1;
}

/* 搜索栏增强样式 */
.search-header .van-search {
  padding: 8px 16px;
}

.search-header .van-search__content {
  border-radius: 20px;
  background-color: #f7f8fa;
}

.search-header .van-field__control {
  font-size: 14px;
}

/* 搜索历史样式 */
.search-history {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebedf0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: bold;
  color: #323233;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-tag:hover {
  transform: scale(1.05);
}

.history-tag:active {
  transform: scale(0.95);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .search-header {
    padding: 4px;
  }

  .main-content {
    padding: 4px;
  }

  .detail-container {
    padding: 12px;
  }

  .pdf-preview-container {
    padding: 12px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-drawings-container {
    background-color: #1a1a1a;
  }

  .search-header {
    background: #2d2d2d;
  }

  .detail-header h3 {
    color: #fff;
  }

  .item-code {
    color: #999;
  }
}
</style>
