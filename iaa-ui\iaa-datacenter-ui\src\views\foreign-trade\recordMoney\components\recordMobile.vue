<template>
  <div class="h-[100%]">
    <van-nav-bar title="录款登记" fixed />

    <div class="fixed top-44px left-0 right-0 z-10 bg-white">
      <van-search
        v-model="searchValue"
        placeholder="请输入搜索关键词"
        @search="onSearch"
        @cancel="onSearch"
      />
    </div>
    <div ref="scrollEl" class="h-[calc(100vh-160px)] overflow-auto px-10px pt-45px mt-12px">
      <van-pull-refresh v-model="loading" @refresh="resetAndLoad">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多数据了"
          @load="onLoad"
        >
          <div v-for="item in list" :key="item.id" class="mb-10px">
            <van-card
              tag=""
              :price="String(`总金额：${item.totalAmount}`)"
              :thumb="item.customerList"
              currency=""
              @click="handleViewDetail(item)"
              class="rounded-lg shadow-sm"
            >
              <template #title>
                <div class="h-[12vh] w-[25vh] p-1">
                  <div class="text-xs truncate">客户：{{ item.customerName }}</div>
                  <div class="text-xs truncate">业务：{{ item.salesmanName }}</div>
                  <div class="text-xs truncate">收款：{{ item.collectionAccount }}</div>
                  <div class="text-xs truncate">币种：{{ item.currency }}</div>
                  <div class="text-xs truncate">日期：{{ item.claimDate }}</div>
                </div>
              </template>
              <template #thumb>
                <img
                  v-if="Array.isArray(item.customerList) && item.customerList.length > 0"
                  :src="item.customerList[0]"
                  alt="暂无水单"
                  class="van-card__img"
                  @click.stop="previewImage(item.customerList)"
                  style="width: 100px; height: 100px; object-fit: cover"
                />
                <div
                  v-else
                  class="van-card__img flex items-center justify-center bg-gray-100"
                  style="width: 100px; height: 100px; object-fit: cover"
                >
                  <van-icon name="photograph" size="24" color="#ccc" />
                </div>
              </template>
              <template #footer>
                <div class="flex items-center justify-between w-full mt-1 ml-2.5">
                  <van-tag v-if="item.status === 2" type="warning" size="medium">暂存</van-tag>
                  <van-tag v-else-if="item.status === 1" type="success" size="medium"
                    >财务已确认</van-tag
                  >
                  <van-tag v-else-if="item.status === 3" type="primary" size="medium"
                    >业务员已确认</van-tag
                  >

                  <div class="flex items-center gap-2">
                    <van-button
                      v-if="item.status === 2"
                      size="small"
                      type="primary"
                      v-hasPermi="['record:money:create']"
                      @click.stop="handleContinueEdit(item)"
                      class="min-w-60px"
                    >
                      继续
                    </van-button>
                    <van-button
                      v-else-if="item.status === 3"
                      size="small"
                      type="success"
                      v-hasPermi="['record:money:status']"
                      @click.stop="handleConfirmReceipt(item)"
                      class="min-w-60px"
                    >
                      确认
                    </van-button>
                    <van-button
                      v-if="item.status !== 1"
                      size="small"
                      type="danger"
                      v-hasPermi="['record:money:delete']"
                      @click.stop="handleDelete(item)"
                      class="min-w-60px"
                    >
                      删除
                    </van-button>
                    <span v-if="item.status === 1">
                    <van-button
                      size="small"
                      type="primary"
                      v-hasPermi="['record:money:admin']"
                      @click.stop="handleEdit(item)"
                      class="min-w-60px"
                    >
                      修改
                    </van-button>
                    <van-button
                      size="small"
                      type="danger"
                      v-hasPermi="['record:money:admin']"
                      @click.stop="handleDelete(item)"
                      class="min-w-60px"
                    >
                      删除
                    </van-button>
                    </span>
                  </div>
                </div>
              </template>
            </van-card>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <div
      class="fixed bottom-10 left-0 right-0 bg-white border-t p-10px flex justify-between items-center"
    >
      <div>共 {{ total }} 条</div>
      <van-button
        type="primary"
        size="small"
        v-hasPermi="['record:money:create']"
        icon="plus"
        @click="handleAdd"
      >
        新增
      </van-button>
    </div>

    <!-- 录款弹窗 -->
    <recordDialogMobile
      v-model:show="dialogVisible"
      :id="selectedId"
      :read-only="readOnlyMode"
      :is-add="isAddMode"
      :isEdit="isEditMode"
      @success="onSuccess"
    />
    <!-- 图片预览 -->
    <van-image-preview
      v-model:show="imagePreviewVisible"
      :images="previewImages"
      :closeable="true"
    />
  </div>
</template>

<script lang="ts" setup>
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import recordDialogMobile from './recordDialogMobile.vue'
import { showSuccessToast, showFailToast, showConfirmDialog } from 'vant'

const searchValue = ref('')
const list = ref<any[]>([])
const loading = ref(false)
const finished = ref(false)
const total = ref(0)
const scrollEl = ref<HTMLElement | null>(null)

const dialogVisible = ref(false)
const selectedId = ref<number | undefined>(undefined)
const readOnlyMode = ref(false)
const isAddMode = ref(false)
const isEditMode = ref(false)

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewImages = ref<string[]>([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  type: 2
})

// 图片预览方法
const previewImage = (imageUrls: string[]) => {
  if (Array.isArray(imageUrls) && imageUrls.length > 0) {
    previewImages.value = imageUrls
    imagePreviewVisible.value = true
  }
}
// 搜索处理
const onSearch = () => {
  resetAndLoad()
}

// 重置并加载数据
const resetAndLoad = () => {
  list.value = []
  total.value = 0
  queryParams.pageNo = 1
  finished.value = false
  loading.value = false
  nextTick(() => scrollEl.value?.scrollTo({ top: 0 }))
  onLoad()
}

// 加载数据
const onLoad = async () => {
  if (finished.value) return
  loading.value = true

  try {
    const res = await ClaimApi.getClaimPage({
      ...queryParams,
      global: searchValue.value
    })

    total.value = res.total
    const newData = res.list || []

    if (queryParams.pageNo === 1) {
      list.value = newData
    } else {
      list.value.push(...newData)
    }

    loading.value = false

    if (newData.length < queryParams.pageSize) {
      finished.value = true
    } else {
      queryParams.pageNo++
    }
  } catch (err) {
    loading.value = false
    finished.value = true
    console.error('加载数据出错:', err)
  }
}

// 处理新增
const handleAdd = () => {
  selectedId.value = undefined
  readOnlyMode.value = false
  isAddMode.value = true
  dialogVisible.value = true
}

// 处理继续编辑
const handleContinueEdit = (row: any) => {
  selectedId.value = row.id
  readOnlyMode.value = false
  isAddMode.value = false
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  selectedId.value = row.id
  readOnlyMode.value = false
  isAddMode.value = false
  dialogVisible.value = true
  isEditMode.value = true
}

// 处理查看详情
const handleViewDetail = (row: any) => {
  selectedId.value = row.id
  readOnlyMode.value = true
  isAddMode.value = false
  dialogVisible.value = true
}

// 处理确认收款
const handleConfirmReceipt = async (row: any) => {
  showConfirmDialog({
    title: '确认收款',
    message: '是否确认该笔款项已收款？'
  })
    .then(async () => {
      await ClaimApi.updateClaim([row.id])
      showSuccessToast('确认收款成功')
      // 无感更新：只更新当前项的状态
      const index = list.value.findIndex((item) => item.id === row.id)
      if (index !== -1) {
        list.value[index].status = 1 // 更新为财务已确认状态
      }
    })
    .catch(() => {
      // on cancel
    })
}
// 添加删除处理方法 - 无感更新
const handleDelete = async (row: any) => {
  showConfirmDialog({
    title: '删除录款',
    message: '是否确认删除该笔款项？'
  })
    .then(async () => {
      await ClaimApi.deleteClaim([row.id])
      showSuccessToast('删除成功')
      // 无感更新：从列表中移除该项
      const index = list.value.findIndex((item) => item.id === row.id)
      if (index !== -1) {
        list.value.splice(index, 1)
        total.value = Math.max(0, total.value - 1)
      }
    })
    .catch(() => {
      // on cancel
    })
}

// 处理成功回调
const onSuccess = () => {
  dialogVisible.value = false
  selectedId.value = undefined
  resetAndLoad()
}

// 初始化加载
onMounted(() => {
  onLoad()
})
</script>

<style scoped>
.border-t {
  border-top: 1px solid #f5f5f5;
}

:deep(.van-card) {
  background-color: #ffffff !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
}

:deep(.van-card__header) {
  padding: 12px 16px 8px !important;
}

:deep(.van-card__content) {
  padding: 0 16px !important;
}

:deep(.van-card__footer) {
  padding: 12px 16px !important;
  border-top: 1px solid #f2f3f5 !important;
}

.min-w-60px {
  min-width: 60px;
}
</style>
