import request from '@/config/axios'


// 生产日报表日志 API
export const DayLogApi = {
  // 查询生产日报表日志分页
  getDayLogPage: async (params: any) => {
    return await request.get({ url: `/report/production-day-log/page`, params })
  },

  // 查询生产日报表日志详情
  getDayLog: async (id: number) => {
    return await request.get({ url: `/report/production-day-log/get?id=` + id })
  },

  // 导出生产日报表日志 Excel
  exportDayLog: async (params) => {
    return await request.download({ url: `/production/day-log/export-excel`, params })
  },
}
