import request from '@/config/axios'

export const RecPaymentApi = {
  /** 获取收款单分页 */
  getRecPaymentPage: (data: any) => {
    return request.post({ url: '/sales/receiving-payment/get-rec-payment-page', data })
  },
  /** 获取出货分页 */
  getShipPage: (data: any) => {
    return request.post({ url: '/sales/receiving-payment/get-ship-page', data })
  },
  /** 获取退货分页 */
  getRmaPage: (data: any) => {
    return request.post({ url: '/sales/receiving-payment/get-rma-page', data })
  },
  /** 获取统计报表 */
  getTotalPage: (data: any) => {
    return request.post({ url: '/sales/receiving-payment/get-total-page', data })
  },
  /** 获取所有业务员 */
  getAllSellerList: () => {
    return request.get({ url: '/sales/receiving-payment/get-all-seller' })
  },
  /** 获取权限列表 */
  getPermissionList: () => {
    return request.get({ url: '/sales/receiving-payment/get-permission-list' })
  },
  /** 保存权限列表 */
  savePermission: (data: any) => {
    return request.post({ url: '/sales/receiving-payment/save-permission', data })
  },
  /** 导出收款列表 */
  exportRecPayment: (data: any) => {
    return request.downloadPost({ url: '/sales/receiving-payment/export-rec-payment', data })
  },
  /** 导出出货列表 */
  exportShip: (data: any) => {
    return request.downloadPost({ url: '/sales/receiving-payment/export-ship', data })
  },
  /** 导出退货列表 */
  exportRma: (data: any) => {
    return request.downloadPost({ url: '/sales/receiving-payment/export-rma', data })
  },
  /** 导出统计列表 */
  exportTotal: (data: any) => {
    return request.downloadPost({ url: '/sales/receiving-payment/export-total', data })
  }
}
