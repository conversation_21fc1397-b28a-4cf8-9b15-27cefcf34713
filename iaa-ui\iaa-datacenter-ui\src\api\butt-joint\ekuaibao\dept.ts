import request from '@/config/axios'

/** 获取合思部门列 */
export const getTableColumn = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/dept/get-columns' })
}

/** 获取U9所有的部门 */
export const getErpAllDept = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/dept/get-erp-all-dept' })
}
/** 获取合思部门分页 */
export const page = async (params: any) => {
  return await request.get({ url: '/butt-joint/ekuaibao/dept/page', params })
}
/** 更新部门 */
export const save = async (data:any)=>{
  return await request.post({url:'/butt-joint/ekuaibao/dept/save',data})
}
