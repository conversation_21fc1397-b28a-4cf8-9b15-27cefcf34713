<!-- src/views/foreign-trade/collectionInformation/index.vue -->
<template>
  <div v-if="!mobile">
    <PC ref="pcRef" />
  </div>

  <div v-else>
    <Mobile />
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store/modules/app'
import Mobile from '@/views/foreign-trade/collectionInformation/components/Mobile.vue'
import PC from '@/views/foreign-trade/collectionInformation/components/PC.vue'

const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)
const pcRef = ref()

/** 初始化 **/
onMounted(() => {
  // 如果需要在主页面调用PC组件的方法，可以通过 pcRef.value 来访问
})
</script>