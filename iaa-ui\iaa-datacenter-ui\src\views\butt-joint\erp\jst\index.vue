<template>
  <ContentWrap>
    <XTable
      :data="dataList"
      :columns="columns"
      :page="queryParams"
      :total="total"
      @refresh="getTableData"
      @search="getTableData"
      v-loading="loading"
    >
      <template #name="{ row }">
        <el-input v-model="row.name" />
      </template>
      <template #operation="{ row }">
        <el-button
          type="primary"
          link
          size="small"
          v-hasPermi="['ekuaibao:custom:list']"
          @click="updateJstToken(row.name)"
        >
          更新
        </el-button>
      </template>
    </XTable>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as ErpApi from '@/api/butt-joint/ekuaibao/erp'
const columns = ref<any[]>([])
const dataList = ref<any[]>([])
const total = ref(0)
const queryParams = ref<any>({})
const loading = ref(false)
const message = useMessage()

const getTableColumn = async () => {
  columns.value = await ErpApi.getProrjectTableColumn()
}

const getTableData = async () => {
  const res = await ErpApi.getJstToken()
  dataList.value = res
  total.value = res.lenght
}

const updateJstToken = async (token: string) => {
  await ErpApi.updateJstToken(token)
  message.success('更新成功')
}

onMounted(() => {
  getTableColumn()
  getTableData()
})
</script>
