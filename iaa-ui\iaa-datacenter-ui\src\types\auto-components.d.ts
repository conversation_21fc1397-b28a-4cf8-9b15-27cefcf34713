/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    78EUCH: typeof import('./../../public/cmaps/78-EUC-H.bcmap')['default']
    78EUCV: typeof import('./../../public/cmaps/78-EUC-V.bcmap')['default']
    78H: typeof import('./../../public/cmaps/78-H.bcmap')['default']
    78msRKSJH: typeof import('./../../public/cmaps/78ms-RKSJ-H.bcmap')['default']
    78msRKSJV: typeof import('./../../public/cmaps/78ms-RKSJ-V.bcmap')['default']
    78RKSJH: typeof import('./../../public/cmaps/78-RKSJ-H.bcmap')['default']
    78RKSJV: typeof import('./../../public/cmaps/78-RKSJ-V.bcmap')['default']
    78V: typeof import('./../../public/cmaps/78-V.bcmap')['default']
    83pvRKSJH: typeof import('./../../public/cmaps/83pv-RKSJ-H.bcmap')['default']
    90mspRKSJH: typeof import('./../../public/cmaps/90msp-RKSJ-H.bcmap')['default']
    90mspRKSJV: typeof import('./../../public/cmaps/90msp-RKSJ-V.bcmap')['default']
    90msRKSJH: typeof import('./../../public/cmaps/90ms-RKSJ-H.bcmap')['default']
    90msRKSJV: typeof import('./../../public/cmaps/90ms-RKSJ-V.bcmap')['default']
    90pvRKSJH: typeof import('./../../public/cmaps/90pv-RKSJ-H.bcmap')['default']
    90pvRKSJV: typeof import('./../../public/cmaps/90pv-RKSJ-V.bcmap')['default']
    AddH: typeof import('./../../public/cmaps/Add-H.bcmap')['default']
    AddRKSJH: typeof import('./../../public/cmaps/Add-RKSJ-H.bcmap')['default']
    AddRKSJV: typeof import('./../../public/cmaps/Add-RKSJ-V.bcmap')['default']
    AddV: typeof import('./../../public/cmaps/Add-V.bcmap')['default']
    AdobeCNS10: typeof import('./../../public/cmaps/Adobe-CNS1-0.bcmap')['default']
    AdobeCNS11: typeof import('./../../public/cmaps/Adobe-CNS1-1.bcmap')['default']
    AdobeCNS12: typeof import('./../../public/cmaps/Adobe-CNS1-2.bcmap')['default']
    AdobeCNS13: typeof import('./../../public/cmaps/Adobe-CNS1-3.bcmap')['default']
    AdobeCNS14: typeof import('./../../public/cmaps/Adobe-CNS1-4.bcmap')['default']
    AdobeCNS15: typeof import('./../../public/cmaps/Adobe-CNS1-5.bcmap')['default']
    AdobeCNS16: typeof import('./../../public/cmaps/Adobe-CNS1-6.bcmap')['default']
    AdobeCNS1UCS2: typeof import('./../../public/cmaps/Adobe-CNS1-UCS2.bcmap')['default']
    AdobeGB10: typeof import('./../../public/cmaps/Adobe-GB1-0.bcmap')['default']
    AdobeGB11: typeof import('./../../public/cmaps/Adobe-GB1-1.bcmap')['default']
    AdobeGB12: typeof import('./../../public/cmaps/Adobe-GB1-2.bcmap')['default']
    AdobeGB13: typeof import('./../../public/cmaps/Adobe-GB1-3.bcmap')['default']
    AdobeGB14: typeof import('./../../public/cmaps/Adobe-GB1-4.bcmap')['default']
    AdobeGB15: typeof import('./../../public/cmaps/Adobe-GB1-5.bcmap')['default']
    AdobeGB1UCS2: typeof import('./../../public/cmaps/Adobe-GB1-UCS2.bcmap')['default']
    AdobeJapan10: typeof import('./../../public/cmaps/Adobe-Japan1-0.bcmap')['default']
    AdobeJapan11: typeof import('./../../public/cmaps/Adobe-Japan1-1.bcmap')['default']
    AdobeJapan12: typeof import('./../../public/cmaps/Adobe-Japan1-2.bcmap')['default']
    AdobeJapan13: typeof import('./../../public/cmaps/Adobe-Japan1-3.bcmap')['default']
    AdobeJapan14: typeof import('./../../public/cmaps/Adobe-Japan1-4.bcmap')['default']
    AdobeJapan15: typeof import('./../../public/cmaps/Adobe-Japan1-5.bcmap')['default']
    AdobeJapan16: typeof import('./../../public/cmaps/Adobe-Japan1-6.bcmap')['default']
    AdobeJapan1UCS2: typeof import('./../../public/cmaps/Adobe-Japan1-UCS2.bcmap')['default']
    AdobeKorea10: typeof import('./../../public/cmaps/Adobe-Korea1-0.bcmap')['default']
    AdobeKorea11: typeof import('./../../public/cmaps/Adobe-Korea1-1.bcmap')['default']
    AdobeKorea12: typeof import('./../../public/cmaps/Adobe-Korea1-2.bcmap')['default']
    AdobeKorea1UCS2: typeof import('./../../public/cmaps/Adobe-Korea1-UCS2.bcmap')['default']
    AppLinkInput: typeof import('./../components/AppLinkInput/index.vue')['default']
    AppLinkSelectDialog: typeof import('./../components/AppLinkInput/AppLinkSelectDialog.vue')['default']
    B5H: typeof import('./../../public/cmaps/B5-H.bcmap')['default']
    B5pcH: typeof import('./../../public/cmaps/B5pc-H.bcmap')['default']
    B5pcV: typeof import('./../../public/cmaps/B5pc-V.bcmap')['default']
    B5V: typeof import('./../../public/cmaps/B5-V.bcmap')['default']
    Backtop: typeof import('./../components/Backtop/src/Backtop.vue')['default']
    CardTitle: typeof import('./../components/Card/src/CardTitle.vue')['default']
    CNS1H: typeof import('./../../public/cmaps/CNS1-H.bcmap')['default']
    CNS1V: typeof import('./../../public/cmaps/CNS1-V.bcmap')['default']
    CNS2H: typeof import('./../../public/cmaps/CNS2-H.bcmap')['default']
    CNS2V: typeof import('./../../public/cmaps/CNS2-V.bcmap')['default']
    CNSEUCH: typeof import('./../../public/cmaps/CNS-EUC-H.bcmap')['default']
    CNSEUCV: typeof import('./../../public/cmaps/CNS-EUC-V.bcmap')['default']
    ColorInput: typeof import('./../components/ColorInput/index.vue')['default']
    ComponentContainer: typeof import('./../components/DiyEditor/components/ComponentContainer.vue')['default']
    ComponentContainerProperty: typeof import('./../components/DiyEditor/components/ComponentContainerProperty.vue')['default']
    ComponentLibrary: typeof import('./../components/DiyEditor/components/ComponentLibrary.vue')['default']
    ConditionNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/ConditionNodeConfig.vue')['default']
    ConfigGlobal: typeof import('./../components/ConfigGlobal/src/ConfigGlobal.vue')['default']
    ContentDetailWrap: typeof import('./../components/ContentDetailWrap/src/ContentDetailWrap.vue')['default']
    ContentWrap: typeof import('./../components/ContentWrap/src/ContentWrap.vue')['default']
    CopperModal: typeof import('./../components/Cropper/src/CopperModal.vue')['default']
    CopyTaskNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/CopyTaskNode.vue')['default']
    CopyTaskNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/CopyTaskNodeConfig.vue')['default']
    CountTo: typeof import('./../components/CountTo/src/CountTo.vue')['default']
    Crontab: typeof import('./../components/Crontab/src/Crontab.vue')['default']
    Cropper: typeof import('./../components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./../components/Cropper/src/CropperAvatar.vue')['default']
    DateRange: typeof import('./../components/DateRange/index.vue')['default']
    DateRangeFilter: typeof import('./../components/FilterInput/DateRangeFilter.vue')['default']
    Descriptions: typeof import('./../components/Descriptions/src/Descriptions.vue')['default']
    DescriptionsItemLabel: typeof import('./../components/Descriptions/src/DescriptionsItemLabel.vue')['default']
    Dialog: typeof import('./../components/Dialog/src/Dialog.vue')['default']
    DictSelect: typeof import('./../components/FormCreate/src/components/DictSelect.vue')['default']
    DictTag: typeof import('./../components/DictTag/src/DictTag.vue')['default']
    DiyEditor: typeof import('./../components/DiyEditor/index.vue')['default']
    DocAlert: typeof import('./../components/DocAlert/index.vue')['default']
    Draggable: typeof import('./../components/Draggable/index.vue')['default']
    Echart: typeof import('./../components/Echart/src/Echart.vue')['default']
    Editor: typeof import('./../components/Editor/src/Editor.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElementBaseInfo: typeof import('./../components/bpmnProcessDesigner/package/penal/base/ElementBaseInfo.vue')['default']
    ElementForm: typeof import('./../components/bpmnProcessDesigner/package/penal/form/ElementForm.vue')['default']
    ElementListeners: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/ElementListeners.vue')['default']
    ElementMultiInstance: typeof import('./../components/bpmnProcessDesigner/package/penal/multi-instance/ElementMultiInstance.vue')['default']
    ElementOtherConfig: typeof import('./../components/bpmnProcessDesigner/package/penal/other/ElementOtherConfig.vue')['default']
    ElementProperties: typeof import('./../components/bpmnProcessDesigner/package/penal/properties/ElementProperties.vue')['default']
    ElementTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/ElementTask.vue')['default']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EndEventNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/EndEventNode.vue')['default']
    Error: typeof import('./../components/Error/src/Error.vue')['default']
    ETenB5H: typeof import('./../../public/cmaps/ETen-B5-H.bcmap')['default']
    ETenB5V: typeof import('./../../public/cmaps/ETen-B5-V.bcmap')['default']
    ETenmsB5H: typeof import('./../../public/cmaps/ETenms-B5-H.bcmap')['default']
    ETenmsB5V: typeof import('./../../public/cmaps/ETenms-B5-V.bcmap')['default']
    ETHKB5H: typeof import('./../../public/cmaps/ETHK-B5-H.bcmap')['default']
    ETHKB5V: typeof import('./../../public/cmaps/ETHK-B5-V.bcmap')['default']
    EUCH: typeof import('./../../public/cmaps/EUC-H.bcmap')['default']
    EUCV: typeof import('./../../public/cmaps/EUC-V.bcmap')['default']
    ExclusiveNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/ExclusiveNode.vue')['default']
    ExtH: typeof import('./../../public/cmaps/Ext-H.bcmap')['default']
    ExtRKSJH: typeof import('./../../public/cmaps/Ext-RKSJ-H.bcmap')['default']
    ExtRKSJV: typeof import('./../../public/cmaps/Ext-RKSJ-V.bcmap')['default']
    ExtV: typeof import('./../../public/cmaps/Ext-V.bcmap')['default']
    FilterInput: typeof import('./../components/FilterInput/index.vue')['default']
    FlowCondition: typeof import('./../components/bpmnProcessDesigner/package/penal/flow-condition/FlowCondition.vue')['default']
    Form: typeof import('./../components/Form/src/Form.vue')['default']
    GBEUCH: typeof import('./../../public/cmaps/GB-EUC-H.bcmap')['default']
    GBEUCV: typeof import('./../../public/cmaps/GB-EUC-V.bcmap')['default']
    GBH: typeof import('./../../public/cmaps/GB-H.bcmap')['default']
    GBK2KH: typeof import('./../../public/cmaps/GBK2K-H.bcmap')['default']
    GBK2KV: typeof import('./../../public/cmaps/GBK2K-V.bcmap')['default']
    GBKEUCH: typeof import('./../../public/cmaps/GBK-EUC-H.bcmap')['default']
    GBKEUCV: typeof import('./../../public/cmaps/GBK-EUC-V.bcmap')['default']
    GBKpEUCH: typeof import('./../../public/cmaps/GBKp-EUC-H.bcmap')['default']
    GBKpEUCV: typeof import('./../../public/cmaps/GBKp-EUC-V.bcmap')['default']
    GBpcEUCH: typeof import('./../../public/cmaps/GBpc-EUC-H.bcmap')['default']
    GBpcEUCV: typeof import('./../../public/cmaps/GBpc-EUC-V.bcmap')['default']
    GBTEUCH: typeof import('./../../public/cmaps/GBT-EUC-H.bcmap')['default']
    GBTEUCV: typeof import('./../../public/cmaps/GBT-EUC-V.bcmap')['default']
    GBTH: typeof import('./../../public/cmaps/GBT-H.bcmap')['default']
    GBTpcEUCH: typeof import('./../../public/cmaps/GBTpc-EUC-H.bcmap')['default']
    GBTpcEUCV: typeof import('./../../public/cmaps/GBTpc-EUC-V.bcmap')['default']
    GBTV: typeof import('./../../public/cmaps/GBT-V.bcmap')['default']
    GBV: typeof import('./../../public/cmaps/GB-V.bcmap')['default']
    H: typeof import('./../../public/cmaps/H.bcmap')['default']
    Hankaku: typeof import('./../../public/cmaps/Hankaku.bcmap')['default']
    Highlight: typeof import('./../components/Highlight/src/Highlight.vue')['default']
    Hiragana: typeof import('./../../public/cmaps/Hiragana.bcmap')['default']
    HKdlaB5H: typeof import('./../../public/cmaps/HKdla-B5-H.bcmap')['default']
    HKdlaB5V: typeof import('./../../public/cmaps/HKdla-B5-V.bcmap')['default']
    HKdlbB5H: typeof import('./../../public/cmaps/HKdlb-B5-H.bcmap')['default']
    HKdlbB5V: typeof import('./../../public/cmaps/HKdlb-B5-V.bcmap')['default']
    HKgccsB5H: typeof import('./../../public/cmaps/HKgccs-B5-H.bcmap')['default']
    HKgccsB5V: typeof import('./../../public/cmaps/HKgccs-B5-V.bcmap')['default']
    HKm314B5H: typeof import('./../../public/cmaps/HKm314-B5-H.bcmap')['default']
    HKm314B5V: typeof import('./../../public/cmaps/HKm314-B5-V.bcmap')['default']
    HKm471B5H: typeof import('./../../public/cmaps/HKm471-B5-H.bcmap')['default']
    HKm471B5V: typeof import('./../../public/cmaps/HKm471-B5-V.bcmap')['default']
    HKscsB5H: typeof import('./../../public/cmaps/HKscs-B5-H.bcmap')['default']
    HKscsB5V: typeof import('./../../public/cmaps/HKscs-B5-V.bcmap')['default']
    Icon: typeof import('./../components/Icon/src/Icon.vue')['default']
    IconSelect: typeof import('./../components/Icon/src/IconSelect.vue')['default']
    IFrame: typeof import('./../components/IFrame/src/IFrame.vue')['default']
    ImageViewer: typeof import('./../components/ImageViewer/src/ImageViewer.vue')['default']
    Infotip: typeof import('./../components/Infotip/src/Infotip.vue')['default']
    InputPassword: typeof import('./../components/InputPassword/src/InputPassword.vue')['default']
    InputWithColor: typeof import('./../components/InputWithColor/index.vue')['default']
    Katakana: typeof import('./../../public/cmaps/Katakana.bcmap')['default']
    KSCEUCH: typeof import('./../../public/cmaps/KSC-EUC-H.bcmap')['default']
    KSCEUCV: typeof import('./../../public/cmaps/KSC-EUC-V.bcmap')['default']
    KSCH: typeof import('./../../public/cmaps/KSC-H.bcmap')['default']
    KSCJohabH: typeof import('./../../public/cmaps/KSC-Johab-H.bcmap')['default']
    KSCJohabV: typeof import('./../../public/cmaps/KSC-Johab-V.bcmap')['default']
    KSCmsUHCH: typeof import('./../../public/cmaps/KSCms-UHC-H.bcmap')['default']
    KSCmsUHCHWH: typeof import('./../../public/cmaps/KSCms-UHC-HW-H.bcmap')['default']
    KSCmsUHCHWV: typeof import('./../../public/cmaps/KSCms-UHC-HW-V.bcmap')['default']
    KSCmsUHCV: typeof import('./../../public/cmaps/KSCms-UHC-V.bcmap')['default']
    KSCpcEUCH: typeof import('./../../public/cmaps/KSCpc-EUC-H.bcmap')['default']
    KSCpcEUCV: typeof import('./../../public/cmaps/KSCpc-EUC-V.bcmap')['default']
    KSCV: typeof import('./../../public/cmaps/KSC-V.bcmap')['default']
    LICENSE: typeof import('./../../public/cmaps/LICENSE')['default']
    MagicCubeEditor: typeof import('./../components/MagicCubeEditor/index.vue')['default']
    MarkdownView: typeof import('./../components/MarkdownView/index.vue')['default']
    'Mobile.test': typeof import('./../views/development/drawings/mobile.test.js')['default']
    NodeHandler: typeof import('./../components/SimpleProcessDesignerV2/src/NodeHandler.vue')['default']
    NoModalDrawer: typeof import('./../components/NoModalDrawer/index.vue')['default']
    NumberFilter: typeof import('./../components/FilterInput/NumberFilter.vue')['default']
    NWPH: typeof import('./../../public/cmaps/NWP-H.bcmap')['default']
    NWPV: typeof import('./../../public/cmaps/NWP-V.bcmap')['default']
    OperateLogV2: typeof import('./../components/OperateLogV2/src/OperateLogV2.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    ParallelNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/ParallelNode.vue')['default']
    Pc: typeof import('./../views/development/drawings/pc.vue')['default']
    ProcessDesigner: typeof import('./../components/bpmnProcessDesigner/package/designer/ProcessDesigner.vue')['default']
    ProcessExpressionDialog: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ProcessExpressionDialog.vue')['default']
    ProcessListenerDialog: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/ProcessListenerDialog.vue')['default']
    ProcessNodeTree: typeof import('./../components/SimpleProcessDesignerV2/src/ProcessNodeTree.vue')['default']
    ProcessPalette: typeof import('./../components/bpmnProcessDesigner/package/palette/ProcessPalette.vue')['default']
    ProcessViewer: typeof import('./../components/bpmnProcessDesigner/package/designer/ProcessViewer.vue')['default']
    PropertiesPanel: typeof import('./../components/bpmnProcessDesigner/package/penal/PropertiesPanel.vue')['default']
    Qrcode: typeof import('./../components/Qrcode/src/Qrcode.vue')['default']
    README: typeof import('./../views/development/drawings/README.md')['default']
    ReceiveTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ReceiveTask.vue')['default']
    RightMenu: typeof import('./../components/XTable/RightMenu.vue')['default']
    RKSJH: typeof import('./../../public/cmaps/RKSJ-H.bcmap')['default']
    RKSJV: typeof import('./../../public/cmaps/RKSJ-V.bcmap')['default']
    Roman: typeof import('./../../public/cmaps/Roman.bcmap')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterSearch: typeof import('./../components/RouterSearch/index.vue')['default']
    RouterView: typeof import('vue-router')['RouterView']
    ScriptTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/ScriptTask.vue')['default']
    Search: typeof import('./../components/Search/src/Search.vue')['default']
    ShortcutDateRangePicker: typeof import('./../components/ShortcutDateRangePicker/index.vue')['default']
    SignalAndMessage: typeof import('./../components/bpmnProcessDesigner/package/penal/signal-message/SignalAndMessage.vue')['default']
    SimpleProcessDesigner: typeof import('./../components/SimpleProcessDesignerV2/src/SimpleProcessDesigner.vue')['default']
    StartUserNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/StartUserNode.vue')['default']
    StartUserNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/StartUserNodeConfig.vue')['default']
    Sticky: typeof import('./../components/Sticky/src/Sticky.vue')['default']
    SummaryCard: typeof import('./../components/SummaryCard/index.vue')['default']
    Table: typeof import('./../components/Table/src/Table.vue')['default']
    TableSelectForm: typeof import('./../components/Table/src/TableSelectForm.vue')['default']
    TagInput: typeof import('./../components/TagInput/index.vue')['default']
    TextFilter: typeof import('./../components/FilterInput/TextFilter.vue')['default']
    Tooltip: typeof import('./../components/Tooltip/src/Tooltip.vue')['default']
    UniCNSUCS2H: typeof import('./../../public/cmaps/UniCNS-UCS2-H.bcmap')['default']
    UniCNSUCS2V: typeof import('./../../public/cmaps/UniCNS-UCS2-V.bcmap')['default']
    UniCNSUTF16H: typeof import('./../../public/cmaps/UniCNS-UTF16-H.bcmap')['default']
    UniCNSUTF16V: typeof import('./../../public/cmaps/UniCNS-UTF16-V.bcmap')['default']
    UniCNSUTF32H: typeof import('./../../public/cmaps/UniCNS-UTF32-H.bcmap')['default']
    UniCNSUTF32V: typeof import('./../../public/cmaps/UniCNS-UTF32-V.bcmap')['default']
    UniCNSUTF8H: typeof import('./../../public/cmaps/UniCNS-UTF8-H.bcmap')['default']
    UniCNSUTF8V: typeof import('./../../public/cmaps/UniCNS-UTF8-V.bcmap')['default']
    UniGBUCS2H: typeof import('./../../public/cmaps/UniGB-UCS2-H.bcmap')['default']
    UniGBUCS2V: typeof import('./../../public/cmaps/UniGB-UCS2-V.bcmap')['default']
    UniGBUTF16H: typeof import('./../../public/cmaps/UniGB-UTF16-H.bcmap')['default']
    UniGBUTF16V: typeof import('./../../public/cmaps/UniGB-UTF16-V.bcmap')['default']
    UniGBUTF32H: typeof import('./../../public/cmaps/UniGB-UTF32-H.bcmap')['default']
    UniGBUTF32V: typeof import('./../../public/cmaps/UniGB-UTF32-V.bcmap')['default']
    UniGBUTF8H: typeof import('./../../public/cmaps/UniGB-UTF8-H.bcmap')['default']
    UniGBUTF8V: typeof import('./../../public/cmaps/UniGB-UTF8-V.bcmap')['default']
    UniJIS2004UTF16H: typeof import('./../../public/cmaps/UniJIS2004-UTF16-H.bcmap')['default']
    UniJIS2004UTF16V: typeof import('./../../public/cmaps/UniJIS2004-UTF16-V.bcmap')['default']
    UniJIS2004UTF32H: typeof import('./../../public/cmaps/UniJIS2004-UTF32-H.bcmap')['default']
    UniJIS2004UTF32V: typeof import('./../../public/cmaps/UniJIS2004-UTF32-V.bcmap')['default']
    UniJIS2004UTF8H: typeof import('./../../public/cmaps/UniJIS2004-UTF8-H.bcmap')['default']
    UniJIS2004UTF8V: typeof import('./../../public/cmaps/UniJIS2004-UTF8-V.bcmap')['default']
    UniJISProUCS2HWV: typeof import('./../../public/cmaps/UniJISPro-UCS2-HW-V.bcmap')['default']
    UniJISProUCS2V: typeof import('./../../public/cmaps/UniJISPro-UCS2-V.bcmap')['default']
    UniJISProUTF8V: typeof import('./../../public/cmaps/UniJISPro-UTF8-V.bcmap')['default']
    UniJISUCS2H: typeof import('./../../public/cmaps/UniJIS-UCS2-H.bcmap')['default']
    UniJISUCS2HWH: typeof import('./../../public/cmaps/UniJIS-UCS2-HW-H.bcmap')['default']
    UniJISUCS2HWV: typeof import('./../../public/cmaps/UniJIS-UCS2-HW-V.bcmap')['default']
    UniJISUCS2V: typeof import('./../../public/cmaps/UniJIS-UCS2-V.bcmap')['default']
    UniJISUTF16H: typeof import('./../../public/cmaps/UniJIS-UTF16-H.bcmap')['default']
    UniJISUTF16V: typeof import('./../../public/cmaps/UniJIS-UTF16-V.bcmap')['default']
    UniJISUTF32H: typeof import('./../../public/cmaps/UniJIS-UTF32-H.bcmap')['default']
    UniJISUTF32V: typeof import('./../../public/cmaps/UniJIS-UTF32-V.bcmap')['default']
    UniJISUTF8H: typeof import('./../../public/cmaps/UniJIS-UTF8-H.bcmap')['default']
    UniJISUTF8V: typeof import('./../../public/cmaps/UniJIS-UTF8-V.bcmap')['default']
    UniJISX02132004UTF32H: typeof import('./../../public/cmaps/UniJISX02132004-UTF32-H.bcmap')['default']
    UniJISX02132004UTF32V: typeof import('./../../public/cmaps/UniJISX02132004-UTF32-V.bcmap')['default']
    UniJISX0213UTF32H: typeof import('./../../public/cmaps/UniJISX0213-UTF32-H.bcmap')['default']
    UniJISX0213UTF32V: typeof import('./../../public/cmaps/UniJISX0213-UTF32-V.bcmap')['default']
    UniKSUCS2H: typeof import('./../../public/cmaps/UniKS-UCS2-H.bcmap')['default']
    UniKSUCS2V: typeof import('./../../public/cmaps/UniKS-UCS2-V.bcmap')['default']
    UniKSUTF16H: typeof import('./../../public/cmaps/UniKS-UTF16-H.bcmap')['default']
    UniKSUTF16V: typeof import('./../../public/cmaps/UniKS-UTF16-V.bcmap')['default']
    UniKSUTF32H: typeof import('./../../public/cmaps/UniKS-UTF32-H.bcmap')['default']
    UniKSUTF32V: typeof import('./../../public/cmaps/UniKS-UTF32-V.bcmap')['default']
    UniKSUTF8H: typeof import('./../../public/cmaps/UniKS-UTF8-H.bcmap')['default']
    UniKSUTF8V: typeof import('./../../public/cmaps/UniKS-UTF8-V.bcmap')['default']
    UploadFile: typeof import('./../components/UploadFile/src/UploadFile.vue')['default']
    UploadImg: typeof import('./../components/UploadFile/src/UploadImg.vue')['default']
    UploadImgs: typeof import('./../components/UploadFile/src/UploadImgs.vue')['default']
    UserFilter: typeof import('./../components/FilterInput/UserFilter.vue')['default']
    UserOrDeptFilter: typeof import('./../components/FilterInput/UserOrDeptFilter.vue')['default']
    UserTask: typeof import('./../components/bpmnProcessDesigner/package/penal/task/task-components/UserTask.vue')['default']
    UserTaskListeners: typeof import('./../components/bpmnProcessDesigner/package/penal/listeners/UserTaskListeners.vue')['default']
    UserTaskNode: typeof import('./../components/SimpleProcessDesignerV2/src/nodes/UserTaskNode.vue')['default']
    UserTaskNodeConfig: typeof import('./../components/SimpleProcessDesignerV2/src/nodes-config/UserTaskNodeConfig.vue')['default']
    V: typeof import('./../../public/cmaps/V.bcmap')['default']
    VDatePicker: typeof import('./../components/Mobile/VDatePicker.vue')['default']
    Verify: typeof import('./../components/Verifition/src/Verify.vue')['default']
    VerifyPoints: typeof import('./../components/Verifition/src/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/Verifition/src/Verify/VerifySlide.vue')['default']
    VerticalButtonGroup: typeof import('./../components/VerticalButtonGroup/index.vue')['default']
    WPSymbol: typeof import('./../../public/cmaps/WP-Symbol.bcmap')['default']
    XButton: typeof import('./../components/XButton/src/XButton.vue')['default']
    XTable: typeof import('./../components/XTable/index.vue')['default']
    XTextButton: typeof import('./../components/XButton/src/XTextButton.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
