<template>
  <div>
    <div class="h-[calc(100vh-347px)] mt-20px">
      <vxe-table
        :row-config="{ height: 27, keyField: 'id' }"
        ref="tableRef"
        :data="list"
        :header-cell-style="{ padding: 0 }"
        border
        stripe
        align="center"
        height="100%"
        max-height="100%"
        show-overflow="title"
        :column-config="{ resizable: true }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        :loading="loading"
        :checkbox-config="{ reserve: true, highlight: true, range: true }"
        :filter-config="{}"
        :footer-data="footerData"
        show-footer
        keep-source
        :footer-cell-style="{
          padding: 0,
          background: '#dcefdc',
          border: '1px solid #ebeef5'
        }"
        :mouse-config="{ selected: true }"
        tabindex="0"
        size="mini"
        @filter-change="handleFilterChange"
      >
        <vxe-column
          field="customersCode"
          width="150"
          title="客户编码"
        />
        <vxe-column
          field="customersName"
          width="150"
          title="客户名称"
        />
        <vxe-column
          field="dateStr"
          width="120"
          title="订单日期"
          :filters="claimDate"
          :filter-render="FilterTemplate.dateRangeFilterRender"
        />
        <vxe-column
          field="orderNo"
          title="订单号"
          width="200"
          :filters="orderNoOptions"
          :filter-render="FilterValue.textFilterRender"
        />
        <vxe-column field="currency" title="币种" width="100" />
        <vxe-column field="salesPrice" title="订单金额" width="150" />
        <vxe-column field="shipPrice" title="已出货金额" width="150" />
        <vxe-column field="collectionAmount" title="收款金额" width="150" />
        <vxe-column field="collectionAmountLocal" title="收款金额（本币）" width="150" />
        <vxe-column field="receivableAmount" title="应收金额" min-width="120" />
        <vxe-column field="receivableAmountLocal" title="应收金额（本币）" min-width="120" />
        <vxe-column field="rate" title="汇率" width="150" />
      </vxe-table>
    </div>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script lang="ts" setup>
import { BusinessReconciliationApi } from '@/api/foreign-trade/businessReconciliation/index'
import type { VxeTablePropTypes } from 'vxe-table'
import * as FilterTemplate from '@/utils/Filter'
import * as FilterValue from '@/utils/Filter'

const orderNoOptions = ref([{ data: '' }])
const claimDate = ref([{ data: [] }])

//订单总金额
const totalSalesPrice = ref(0)

//已出货总金额
const totalShipPrice = ref(0)
//收款总金额
const totalCollectionAmount = ref(0)
//应收总金额
const totalReceivableAmount = ref(0)
//应收总金额（本币）
const totalReceivableAmountLocal = ref(0)

//合计行
const footerData = ref<VxeTablePropTypes.FooterData>([
  {
    customersCode: '合计总和',
    salesPrice: totalSalesPrice,
    shipPrice: totalShipPrice,
    collectionAmount: totalCollectionAmount,
    receivableAmount: totalReceivableAmount,
    receivableAmountLocal: totalReceivableAmountLocal,
  }
])

const loading = ref(false) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const tableRef = ref()

const props = defineProps<{ orderCodes?: string[] }>()

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  orderCodes: props.orderCodes
})

/** 查询列表 */
const getList = async () => {
  queryParams.orderCodes = props.orderCodes
  loading.value = true
  try {
    const res = await BusinessReconciliationApi.getOrderReceivablePage(queryParams)
    list.value = res.list
    total.value = res.total
    totalSalesPrice.value = list.value
      .reduce((total, item) => total + item.salesPrice, 0)
      .toFixed(3)
    totalShipPrice.value = list.value.reduce((total, item) => total + item.shipPrice, 0).toFixed(3)
    totalCollectionAmount.value = list.value
      .reduce((total, item) => total + item.collectionAmount, 0)
      .toFixed(3)
    totalReceivableAmount.value = list.value
      .reduce((total, item) => total + item.receivableAmount, 0)
      .toFixed(3)
    totalReceivableAmountLocal.value=list.value
      .reduce((total, item) => total + item.receivableAmountLocal, 0)
      .toFixed(3)
  } finally {
    loading.value = false
  }
}

const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['classification']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize', 'orderCodes'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

watch(
  () => props.orderCodes,
  () => {
    getList()
  },
  { immediate: true }
)
</script>
