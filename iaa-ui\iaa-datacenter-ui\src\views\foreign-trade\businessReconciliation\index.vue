<template>
   <div v-if="!mobile">
      <ContentWrap>
    <!-- 顶部客户选择（总应收模块上移至此处） -->
    <Total v-model="customerCodes" />

    <!-- 两个标签页：订单应收、费用应收 -->
    <el-tabs v-model="activeTab">
      <el-tab-pane label="订单应收" name="order" />
      <el-tab-pane label="费用应收" name="fee" />
    </el-tabs>

    <div v-if="activeTab === 'order'">
      <OrderReceivable :order-codes="customerCodes" />
    </div>
    <div v-else>
      <ExpenseReceivable :order-codes="customerCodes" />
    </div>
  </ContentWrap>
  </div>
  <div v-else>
    <MobileReconciliation />
  </div>

</template>
<script lang="ts" setup>
import Total from './components/Total.vue'
import OrderReceivable from './components/OrderReceivable.vue'
import ExpenseReceivable from './components/ExpenseReceivable.vue'
import MobileReconciliation from './components/MobileReconciliation.vue'
import { useAppStore } from '@/store/modules/app'


const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

const activeTab = ref('order')
const customerCodes = ref<string[]>([])
</script>
