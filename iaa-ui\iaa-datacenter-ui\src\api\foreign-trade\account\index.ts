import request from '@/config/axios'

export const getAccount = async () => {
  return await request.get({ url: '/collection/account/get' })
}

export const getErpBankAccount = async (data: any) => {
  return await request.post({ url: '/collection/account/getErpBankAccount', data })
}


export const addAccountPage = async (data: any) => {
  return await request.post({ url: '/collection/account/add', data })
}

export const deletesAccountPage = async (data: any) => {
  return await request.post({ url: '/collection/account/delete', data })
}