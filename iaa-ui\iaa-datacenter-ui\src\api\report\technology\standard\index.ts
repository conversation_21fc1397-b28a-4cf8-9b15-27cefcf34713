
import request from '@/config/axios'

// 标准工时录入 API
export const StandardApi = {
  // 查询标准工时录入分页
  getStandardPage: async (params: any) => {
    return await request.post({ url: `/report/standard/page`, data: params })
  },

  // 查询标准工时录入详情
  getStandard: async (id: number) => {
    return await request.get({ url: `/report/standard/get?id=` + id })
  },

  // 新增标准工时录入
  createStandard: async (data: any) => {
    return await request.post({ url: `/report/standard/create`, data })
  },

  // 新增标准工时录入
  batchCreate: async (data: any) => {
    return await request.post({ url: `/report/standard/batch-create`, data })
  },


  // 修改标准工时录入
  updateStandard: async (data: any) => {
    return await request.put({ url: `/report/standard/update`, data })
  },

  // 批量修改标准工时录入
  updateBatch: async (data: any) => {
    return await request.post({ url: `/report/standard/update-batch`, data })
  },

  // 删除标准工时录入
  deleteStandard: async (id: any) => {
    return await request.post({ url: `/report/standard/delete`, data:id })
  },

  // 导出标准工时录入 Excel
  exportStandard: async (params) => {
    return await request.download({ url: `/report/standard/export-excel`, params })
  },
  
  //获取所有型号
  getAllModels: async () => {
    return await request.get({ url: `/report/standard/getAllModels` })
  },
}