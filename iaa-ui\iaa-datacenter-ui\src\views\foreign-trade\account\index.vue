<template>
  <el-row :gutter="20">
    <el-col :span="24" :xs="24">
      <ContentWrap>
        <el-transfer
          v-model="transferValue"
          :data="erpBankAccountData"
          :props="{ key: 'accountId', label: 'accountName' }"
          :titles="['可添加账户', '已添加账户']"
          @change="handleTransferChange"
          class="mt-20px"
          filterable
          :filter-method="filterMethod"
        />
      </ContentWrap>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import * as AccountApi from '@/api/foreign-trade/account' // 引入账户相关接口

const message = useMessage()

// 穿梭框数据
const accountData = ref<any[]>([]) // 右侧已选数据
const erpBankAccountData = ref<any[]>([]) // 左侧可选数据
const transferValue = ref<string[]>([]) // 穿梭框绑定值（已选中的key）

// 初始化数据
const initTransferData = async () => {
  try {
    // 1. 获取右侧账户数据（已选中的账户）
    const accountRes = await AccountApi.getAccount()
    accountData.value = accountRes || []
    console.log('右侧已选数据:', accountData.value)
    
    // 2. 设置transferValue为右侧已选数据的accountId数组
    // 这一步很关键，它决定了哪些项显示在右侧
    transferValue.value = accountData.value
      .map(item => item.accountId)
      .filter(Boolean) // 过滤掉空值
    console.log('transferValue:', transferValue.value)
    
    // 3. 从右侧数据中提取id列表（用于获取左侧数据）
    const accountIds = accountData.value.map(item => item.id).filter(Boolean)
    
    // 4. 调用左侧接口，传入右侧数据的id列表
    const erpRes = await AccountApi.getErpBankAccount(accountIds)
    erpBankAccountData.value = erpRes || []
    console.log('左侧可选数据:', erpBankAccountData.value)
  } catch (error) {
    console.error('获取账户数据失败:', error)
    message.error('获取账户数据失败')
  }
}

// 穿梭框变更处理
const handleTransferChange = async (value: string[], direction: 'left' | 'right', movedKeys: string[]) => {
  try {
    if (direction === 'right') {
      // 从左侧往右移动 - 添加账户
      // 获取移动的完整数据对象
      const movedItems = erpBankAccountData.value.filter(item => 
        movedKeys.includes(item.accountId)
      )
      
      // 构造需要传递的数据格式
      const addData = movedItems.map(item => ({
        accountId: item.accountId,
        accountName: item.accountName,
        accountCode: item.accountCode
      }))
      
      // 调用添加接口
      await AccountApi.addAccountPage(addData)
      // initTransferData()
      message.success('操作成功')
    } else if (direction === 'left') {
      // 从右侧往左移动 - 删除账户
      // 构造需要传递的accountId数组
      const deleteData = movedKeys
      
      // 调用删除接口
      await AccountApi.deletesAccountPage(deleteData)
      // initTransferData()
      message.success('操作成功')
    }
    
    // 更新transferValue值
    transferValue.value = value
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败')
  }
}

const filterMethod = (query: string, item: any) => {
  return item.accountName?.toLowerCase().includes(query.toLowerCase())
}

onMounted(() => {
  initTransferData()
})
</script>

<style lang="scss" scoped>
:deep(.el-transfer) {
  height: calc(100vh - 190px);
}

:deep(.el-transfer-panel__body) {
  height: calc(100vh - 240px);
}

:deep(.el-transfer-panel) {
  width: calc(50% - 82px);
}

:deep(.el-transfer-panel__list) {
  height: calc(100% - 64px);
}
</style>
