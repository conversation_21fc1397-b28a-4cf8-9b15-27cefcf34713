import request from '@/config/axios'

export const DefineApi = {
  // 保存或修改工艺定义
  saveOrUpdate: async (data: any) => {
    return await request.post({ url: '/report/technology-define/save-or-update', data })
  },
  // 获取工艺列表
  listDefine: async () => {
    return await request.get({ url: '/report/technology-define/list' })
  },
  // 删除工艺定义
  deleteDefine: async (id: number) => {
    return await request.get({ url: '/report/technology-define/delete/' + id })
  }
}
