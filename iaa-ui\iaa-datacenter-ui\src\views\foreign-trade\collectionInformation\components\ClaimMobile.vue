<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    round
    :style="{ height: '90%', padding: '12px' }"
    :closeable="true"
    @click-overlay="onClickOverlay"
    @click-close-icon="onClickOverlay"
  >
    <div class="h-full flex flex-col">
      <div class="text-center text-5 font-600 mb-8px">
        {{ readOnly ? '认款详情' : '收款认领' }}</div
      >
      <div class="flex-1 overflow-auto pr-4px">
        <van-cell-group inset>
          <VDatePicker
            v-model="form.claimDate"
            label="日期"
            style="margin-left: -10px; margin-bottom: -18px"
            :readonly="readOnly || !isDateDisabled"
          />
          <van-field
            v-model="form.salesmanName"
            label="业务员"
            placeholder="默认当前登录人"
            :readonly="readOnly"
          />
          <van-field
            :model-value="customerDisplay"
            label="客户名称"
            is-link
            readonly
            placeholder="输入选择"
            @click="!readOnly && openCustomerPicker()"
          />
          <van-field v-model="form.customerCode" label="客户编码" readonly />
          <van-field v-model="form.currency" label="币种" readonly />

          <div>
            <van-cell title="已选择" is-link @click="!readOnly && onCheckShowPicker()">
              <span class="text-#333">{{ fullName(selectList.join(', ')) }}</span>
            </van-cell>
            <van-popup
              v-model:show="showPicker"
              position="bottom"
              :style="{ height: '60%' }"
              v-if="!readOnly"
            >
              <div
                style="
                  position: sticky;
                  top: 0;
                  z-index: 10;
                  display: flex;
                  justify-content: space-between;
                  height: 44px;
                  line-height: 44px;
                  background: #fff;
                  border-bottom: 1px solid #f5f5f5;
                "
              >
                <van-button
                  style="border: none; color: #969799"
                  @click="showPicker = false"
                  size="normal"
                >
                  取消
                </van-button>
                <van-button
                  style="border: none; color: #6398fb"
                  @click="checkedChange"
                  size="normal"
                >
                  确认
                </van-button>
              </div>
<van-checkbox-group v-model="checkboxValues">
  <van-cell v-for="(item, index) in showColumns" :key="index">
    <template #default>
      <div class="collection-item">
        <div class="account-line">账户：{{ item.collectionAccount }}</div>
        <div class="amount-line"
          >金额: {{ item.collectionAmount }} {{ item.currency }}</div
        >
        <div class="date-line">日期: {{ item.dateStr }}</div>
      </div>
    </template>
    <template #right-icon>
      <van-checkbox :name="`${item.id}-${item.collectionAccount}-${item.collectionAmount}-${item.payer || ''}`" />
    </template>
  </van-cell>
</van-checkbox-group>
            </van-popup>
          </div>
          <van-field v-model="form.totalAmount" label="总金额" readonly />
          <van-field
            v-model="form.remark"
            label="备注"
            :readonly="readOnly"
          />
        </van-cell-group>
        <van-divider>订单明细</van-divider>
        <div class="px-10px">
          <div
            v-for="(row, idx) in orders"
            :key="idx"
            class="mb-10px border rounded p-10px bg-#fff"
          >
            <van-field
              :model-value="row.orderNo"
              label="订单号"
              is-link
              readonly
              placeholder="选择订单"
              @click="!readOnly && openOrderPicker(idx)"
            />
            <van-field
              v-model.number="row.orderAmount"
              label="订单金额"
              type="number"
              :readonly="true"
            />
            <!-- <van-field
              v-model.number="row.shipAmount"
              label="已出货金额"
              type="number"
              :readonly="true"
            /> -->
            <van-field
              v-model.number="row.remainingAmount"
              label="剩余认款余额"
              type="number"
              :readonly="true"
            />
            <van-field
              v-model.number="row.claimRatio"
              label="收款比例(%)"
              type="number"
              :readonly="readOnly"
              @input="!readOnly && handleRatioInput(row, $event)"
            />
            <van-field
              v-model.number="row.amount"
              label="认款金额"
              type="number"
              placeholder="≤订单金额"
              @input="!readOnly && handleAmountInput(row, $event)"
            />
            <div class="text-right mt-6px" v-if="!readOnly">
              <van-button size="small" type="danger" @click="orders.splice(idx, 1)"
                >删除</van-button
              >
            </div>
          </div>
          <van-button block type="primary" size="small" plain @click="addOrder" v-if="!readOnly"
            >+ 添加订单</van-button
          >
        </div>
        <van-divider>费用类别</van-divider>
        <div class="px-10px">
          <div
            v-for="(row, idx) in expenses"
            :key="idx"
            class="mb-10px border rounded p-10px bg-#fff"
          >
            <!-- 修改费用类别字段为下拉选择 -->
            <van-field
              :model-value="getExpenseTypeLabel(row.expenseType)"
              label="费用类别"
              placeholder="请选择费用类别"
              readonly
              is-link
              @click="!readOnly && openExpenseTypePicker(idx)"
            />
            <van-field
              v-model.number="row.amount"
              label="金额"
              type="number"
              :readonly="readOnly"
            />
            <van-field
              v-model="row.expenseRemark"
              label="备注"
              :readonly="readOnly"
            />
            <div class="text-right mt-6px" v-if="!readOnly">
              <van-button size="small" type="danger" @click="expenses.splice(idx, 1)"
                >删除</van-button
              >
            </div>
          </div>
          <van-button block type="primary" size="small" plain @click="addExpense" v-if="!readOnly"
            >+ 添加费用</van-button
          >
        </div>
        <van-divider>订单未下</van-divider>
        <div class="px-10px">
          <div
            v-for="(row, idx) in ordersUnsettled"
            :key="idx"
            class="mb-10px border rounded p-10px bg-#fff"
          >
            <van-field
              v-model.number="row.amount"
              label="金额"
              type="number"
              :readonly="readOnly"
            />
          </div>
        </div>
      </div>

      <!-- 客户选择 Picker -->
      <van-popup
        v-model:show="customerPickerShow"
        position="bottom"
        round
        :style="{ height: '60%' }"
      >
        <van-picker
          :columns="customerColumns"
          :loading="customerLoading"
          @confirm="onCustomerConfirm"
          @cancel="customerPickerShow = false"
        >
          <template #columns-top>
            <div class="p-10px">
              <van-field
                v-model="customerKeyword"
                placeholder="输入客户名称检索"
                clearable
                @update:model-value="fetchCustomerColumns"
              />
            </div>
          </template>
          <template #columns-bottom v-if="!customerLoading && customerColumns.length === 0">
            <div class="text-center p-4 text-gray-400 mt--50">暂无相关客户信息</div>
          </template>
        </van-picker>
      </van-popup>

      <!-- 订单选择 Picker -->
      <van-popup v-model:show="orderPickerShow" position="bottom" round :style="{ height: '60%' }">
        <van-picker
          :columns="orderColumns"
          :loading="orderLoading"
          @confirm="onOrderConfirm"
          @cancel="orderPickerShow = false"
        >
          <template #columns-top>
            <div class="p-10px">
              <van-field
                v-model="orderKeyword"
                placeholder="输入订单号检索"
                clearable
                @update:model-value="fetchOrderColumns"
              />
            </div>
          </template>
          <template #columns-bottom v-if="!orderLoading && orderColumns.length === 0">
            <div class="text-center p-4 text-gray-400 mt--50">暂无订单信息</div>
          </template>
        </van-picker>
      </van-popup>
      <!-- 币种选择 Picker -->
      <van-popup v-model:show="currencyPicker" round position="bottom" :style="{ height: '60%' }">
        <van-picker
          :columns="currencyColumns"
          @cancel="currencyPicker = false"
          @confirm="onCurrency"
        />
      </van-popup>

      <!-- 费用类别选择器弹窗 -->
      <van-popup
        v-model:show="expenseTypePickerShow"
        position="bottom"
        round
        :style="{ height: '60%' }"
        v-if="!readOnly"
      >
        <van-picker
          :columns="expenseTypeColumns"
          @confirm="onExpenseTypeConfirm"
          @cancel="expenseTypePickerShow = false"
        />
      </van-popup>

      <div class="pt-10px" v-if="!readOnly  && !isEdit">
        <van-row :gutter="20">
          <van-col span="24" 
            ><span class="mr-15px text-xs ">已填写总金额：{{totalClaimed}}</span></van-col>
          <van-col span="12"
            ><van-button
              type="warning"
              block
              size="small"
              :loading="temporarily"
              @click="temporarilySubmit"
              >暂存</van-button
            ></van-col
          >
          <van-col span="12"
            ><van-button type="primary" block size="small" :loading="submitting" @click="submit"
              >确认提交</van-button
            ></van-col
          >
        </van-row>
      </div>
      <div class="pt-10px" v-else-if="!readOnly && isEdit">
        <van-row :gutter="20">
          <van-col span="24" 
            ><span class="mr-15px text-xs ">已填写总金额：{{totalClaimed}}</span></van-col>
          <van-col span="24"
            ><van-button type="primary" block size="small" :loading="submitting" @click="submit"
              >确认提交</van-button
            ></van-col
          >
        </van-row>
      </div>

      <!-- 只读模式下显示关闭按钮 -->
      <div class="pt-10px" v-else>
        <van-button type="primary" block size="small" @click="show = false">关闭</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import { InformationApi } from '@/api/foreign-trade/collectionInformation/index'
import { useUserStore } from '@/store/modules/user'
import VDatePicker from '@/components/Mobile/VDatePicker.vue'
import { showSuccessToast, showFailToast, showToast } from 'vant'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CollectionDetailApi } from '@/api/foreign-trade/collectionDetails'
import dayjs from 'dayjs'

const message = useMessage()
const userStore = useUserStore()

const props = defineProps<{
  show: boolean
  ids?: number[]
  prefill?: any
  readOnly?: boolean // 添加只读属性
  isEdit?: boolean
}>()

// 费用类别选择
const expenseTypePickerShow = ref(false)
const expenseTypeColumns = computed(() => {
  const dictOptions = getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)
  return dictOptions.map((dict) => ({
    text: dict.label,
    value: dict.value
  }))
})

let currentExpenseTypeIndex = -1


// 添加获取费用类型标签的方法
const getExpenseTypeLabel = (value: string) => {
  if (value === undefined || value === null || value === '') return ''
  const dictOptions = getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)
  const dict = dictOptions.find((option) => option.value === Number(value))
  return dict ? dict.label : value
}

const openExpenseTypePicker = (index: number) => {
  currentExpenseTypeIndex = index
  expenseTypePickerShow.value = true
}

const onExpenseTypeConfirm = ({ selectedValues }: { selectedValues: string[] }) => {
  const value = selectedValues?.[0]
  if (value === undefined || value === null || currentExpenseTypeIndex < 0) return

  const row = expenses.value[currentExpenseTypeIndex]
  row.expenseType = value
  expenseTypePickerShow.value = false
}

const emits = defineEmits(['update:show', 'success'])
const show = computed({ get: () => props.show, set: (v) => emits('update:show', v) })

// 暂存预填的 claimId（提交时需要带上）
const claimIdRef = ref<number | null>(null)

// 币种选择
const currencyPicker = ref(false)
const currencyColumns = ref([])
let currencyIndex = -1

//选择币种后
const onCurrency = ({ selectedValues }) => {
  const item = selectedValues?.[0]
  if (!item) return
  const row = orders.value[currencyIndex]
  console.log('row', row)
  row.currency = item
  currencyPicker.value = false
}

// 客户选择
const customerPickerShow = ref(false)
const customerKeyword = ref('')
const customerOptions = ref<any[]>([])
const customerColumns = ref<any[]>([])
const customerLoading = ref(false)
const customerDisplay = computed(() =>
  form.customerName && form.customerCode ? `${form.customerName}` : ''
)

const selectList = ref<string[]>([])
// 添加用于内部判断的唯一标识符列表
const selectUniqueIds = ref<string[]>([])
const showPicker = ref(false)
const showColumns = ref<any[]>([])

// 添加用于复选框组绑定的变量
const checkboxValues = ref<string[]>([])
// 初始化选中列表 - 只负责初始化显示列表
const initSelectList = () => {
  // 显示用的列表保持原有格式
  selectList.value = collectionList.value.map(
    (item) => `${item.collectionAccount}-${item.collectionAmount}`
  )

  selectIds.value = collectionList.value.map((item) => item.id)
}

const selectIds = ref<number[]>([])
// 打开选择器时加载数据
const onCheckShowPicker = async () => {
  showPicker.value = true
  const queryParams = {
    pageNo: -1,
    pageSize: 30,
    status: [0],
    temporaryIds: selectIds.value,
    isMe: false // 默认为false
  }
  // 判断是否为继续认领的情况
  if (props.prefill) {
    // 如果是继续认领，设置isMe为true
    queryParams.isMe = true
  }
  const res = await InformationApi.getInformationPage(queryParams)
  showColumns.value = res.list

  initSelectList()

  // 设置复选框的初始选中状态
  checkboxValues.value = collectionList.value.map(
    (item) => `${item.id}-${item.collectionAccount}-${item.collectionAmount}-${item.payer || ''}`
  )
}
const checkedChange = () => {
  showPicker.value = false
  // 使用唯一标识符来匹配选中的项目
  const selectedItems = showColumns.value.filter((item) =>
    checkboxValues.value.includes(`${item.id}-${item.collectionAccount}-${item.collectionAmount}-${item.payer || ''}`)
  )
  collectionList.value = selectedItems.map((item) => ({
    id: item.id,
    dateStr: item.dateStr,
    collectionAccount: item.collectionAccount,
    collectionId: item.collectionId,
    collectionAmount: item.collectionAmount,
    currency: item.currency,
    payer: item.payer || '' // 添加付款人信息
  }))
  // 更新总金额
  form.totalAmount = collectionList.value.reduce((acc, cur) => acc + cur.collectionAmount, 0)
  
  // 更新显示列表
  selectList.value = collectionList.value.map(
    (item) => `${item.collectionAccount}-${item.collectionAmount}`
  )
  
  // 更新selectIds
  selectIds.value = collectionList.value.map((item) => item.id)
}

const fullName = (v) => {
  if (v.length >= 20) {
    const firstPart = v.substring(0, 5)
    const lastPart = v.substring(v.length - 5)
    return `${firstPart}...${lastPart}`
  }
  return v
}

// 在合适的地方使用 readOnly 属性控制组件行为
const readOnly = computed(() => props.readOnly || (props.prefill && props.prefill.readOnly))

const onClickOverlay = () => {
  //关闭弹窗
  //把claimIdRef赋值为空
  claimIdRef.value = null
}
const openCustomerPicker = async () => {
  customerPickerShow.value = true
  if (readOnly.value) return
  customerPickerShow.value = true
  await fetchCustomerColumns()
}
const fetchCustomerColumns = async () => {
  customerLoading.value = true
  try {
    const res: any = await ClaimApi.getCustomer(customerKeyword.value || '')
    customerOptions.value = res
    customerColumns.value = customerOptions.value.map((it: any) => ({
      text: `${it.name}-${it.code}`,
      value: it
    }))
  } finally {
    customerLoading.value = false
  }
}
const onCustomerConfirm = ({ selectedValues }) => {
  const item = selectedValues?.[0]
  if (!item) return
  form.customerName = item.name
  form.customerCode = item.code
  customerPickerShow.value = false
  orders.value = []
}

// 订单选择
const orderPickerShow = ref(false)
const orderKeyword = ref('')
const orderOptions = ref<any[]>([])
const orderColumns = ref<any[]>([])
const orderLoading = ref(false)
let currentOrderIndex = -1
const openOrderPicker = (index: number) => {
  if (!form.customerCode) {
    showFailToast('请先选择客户')
    return
  }
  orderKeyword.value = ''
  currentOrderIndex = index
  orderPickerShow.value = true
  fetchOrderColumns()
}
const fetchOrderColumns = async () => {
  orderLoading.value = true
  try {
    const res: any = await ClaimApi.getOrders({
      code: form.customerCode,
      DocNo: orderKeyword.value
    })
    orderOptions.value = res
    orderColumns.value = orderOptions.value.map((it: any) => ({
      text: `${it.DocNo}(${it.currency}-${it.salesPrice})`,
      value: it
    }))
  } finally {
    orderLoading.value = false
  }
}
const onOrderConfirm = ({ selectedValues }) => {
  const item = selectedValues?.[0]
  if (!item || currentOrderIndex < 0) return

  // 检查订单币种是否与认领币种一致
  if (form.currency && item.currency) {
    if (form.currency !== item.currency) {
      showFailToast(
        `所选订单币种为 ${item.currency}，与当前认领币种 ${form.currency} 不一致，无法选择`
      )
      return
    }
  }
  const row = orders.value[currentOrderIndex]
  row.orderNo = item.DocNo
  row.orderAmount = item.salesPrice
  row.shipAmount = item.shipPrice || 0
  row.claimedAmount = item.claimedAmount
  row.remainingAmount = item.remainingAmount
  row.amount = item.remainingAmount
  row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
  orderPickerShow.value = false
}

// 处理收款比例输入
const handleRatioInput = (row: any, event: Event) => {
  const input = event.target as HTMLInputElement
  const ratio = parseFloat(input.value) || 0

  // 根据收款比例计算认款金额，使用四舍五入
  const calculatedAmount = Math.round((row.orderAmount * ratio) / 100)
  row.amount = calculatedAmount

  // 确保认款金额不超过剩余可认款金额
  if (row.amount > row.remainingAmount) {
    row.amount = row.remainingAmount
    // 重新计算收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
    showFailToast('认款金额不能超过剩余可认款金额，收款比例已自动调整')
  }
}

// 处理认款金额输入
const handleAmountInput = (row: any, event: Event) => {
  const input = event.target as HTMLInputElement
  const value = parseFloat(input.value) || 0

  if (value > row.remainingAmount) {
    row.amount = row.remainingAmount
    // 同步更新收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
    showFailToast('认款金额不能超过剩余可认款金额，收款比例已自动调整')
  } else {
    // 同步更新收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
  }
}

// 添加计算认款总金额的方法
const claimTotalAmount = computed(() => {
  // 计算订单明细的认款金额总和
  const orderAmountTotal = orders.value.reduce((sum, order) => {
    return sum + Number(order.amount || 0)
  }, 0)

  // 计算费用类别的金额总和
  const expenseAmountTotal = expenses.value.reduce((sum, expense) => {
    return sum + Number(expense.amount || 0)
  }, 0)

  const ordersUnsettledAmount = ordersUnsettled.value.reduce((sum, item) => {
    return sum + Number(item.amount || 0)
  }, 0)

  // 返回总和
  return orderAmountTotal + expenseAmountTotal + ordersUnsettledAmount
})
const today = dayjs().format('YYYY-MM-DD')
const form = reactive<any>({
  claimDate: today,
  type: 1,
  status: 0,
  salesmanName: userStore.user?.nickname || '',
  customerName: '',
  customerCode: '',
  totalAmount: 0.0,
  currency: '',
  currencyCode: ''
})

const orders = ref<any[]>([])
const expenses = ref<any[]>([])
const ordersUnsettled = ref<any[]>([
  { type: 3, amount: 0 }
])

//添加订单
const addOrder = () => {
  orders.value.push({ 
    type: 1, 
    orderNo: '', 
    orderAmount: 0, 
    currency: '美元', 
    amount: 0,
    claimRatio: 100 // 默认收款比例100%
  })
}
const addExpense = () =>
  expenses.value.push({ type: 2, expenseType: '', expenseRemark: '', currency: '美元', amount: 0 })

const collectionList = ref<any[]>([])

const isDateDisabled = ref(false)
//打开弹窗
const loadCollections = async (ids?: number[]) => {
  if (!ids || !ids.length) return

  if (!props.prefill) {
    resetForm()
    const details = await InformationApi.getInformation(ids)
    collectionList.value = details.map((it: any) => ({
      id: it.id,
      dateStr: it.dateStr,
      collectionAccount: it.collectionAccount,
      collectionId: it.collectionId,
      collectionAmount: it.collectionAmount,
      currency: it.currency,
      currencyCode: it.currencyCode,
      payer: it.payer || ''
    }))
    form.currency = collectionList.value[0].currency
    form.currencyCode = collectionList.value[0].currencyCode
    form.totalAmount = collectionList.value.reduce((acc, cur) => acc + cur.collectionAmount, 0)
    initSelectList()
    // 设置复选框的初始选中状态
    checkboxValues.value = collectionList.value.map(
      (item) => `${item.id}-${item.collectionAccount}-${item.collectionAmount}-${item.payer || ''}`
    )
    // 确保默认有一条订单未下记录
    if (ordersUnsettled.value.length === 0) {
      ordersUnsettled.value.push({ type: 3, amount: 0 })
    }
  }

  isDateDisabled.value=await CollectionDetailApi.getDate()
}

watch(
  () => props.ids,
  async (val) => {
    if (val && val.length) await loadCollections(val)
  },
  { immediate: true }
)

// 如果传入了 prefill（继续认领），按后端返回预填数据回显
watch(
  () => props.prefill,
  (data) => {
    if (!data) return
    claimIdRef.value = data.id || null
    form.claimDate = data.claimDate
    form.salesmanName = data.salesmanName || form.salesmanName
    form.customerName = data.customerName || ''
    form.customerCode = data.customerCode || ''
    form.totalAmount = Number(data.totalAmount || 0)
    form.currency = data.currency || ''
    form.currencyCode = data.currencyCode || ''
    form.remark = data.remark || ''
    // 收款列表
    collectionList.value = (data.collectionList || []).map((it: any) => ({
      id: it.id,
      dateStr: it.dateStr,
      collectionAccount: it.collectionAccount,
      collectionId: it.collectionId,
      collectionAmount: it.collectionAmount,
      currency: it.currency,
      currencyCode: it.currencyCode,
      payer: it.payer || ''
    }))
    initSelectList()
        // 设置复选框的初始选中状态
    checkboxValues.value = collectionList.value.map(
      (item) => `${item.id}-${item.collectionAccount}-${item.collectionAmount}-${item.payer || ''}`
    )
    // 明细
    orders.value = []
    expenses.value = []
    ordersUnsettled.value = []
    ;(data.detailList || []).forEach((d: any) => {
      if (d.type === 1) {
        // 计算收款比例，使用四舍五入
        let claimRatio = 100
        if (d.orderAmount > 0) {
          claimRatio = Math.round((Number(d.amount || 0) / d.orderAmount) * 100)
        }
        
        orders.value.push({
          type: 1,
          orderNo: d.orderNo,
          orderAmount: Number(d.orderAmount || 0),
          currency: d.currency || '美元',
          amount: Number(d.amount || 0),
          remainingAmount: Number(d.remainingAmount || 0),
          shipAmount: Number(d.shipAmount || 0),
          claimRatio: claimRatio // 添加收款比例字段
        })
      }
      else if (d.type === 2)
        expenses.value.push({
          type: 2,
          expenseType: d.expenseType,
          expenseRemark: d.expenseRemark,
          currency: d.currency || '美元',
          amount: Number(d.amount || 0)
        })
      else if (d.type === 3)
        ordersUnsettled.value.push({
          type: 3,
          amount: Number(d.amount || 0)
        })
    })
    // 确保至少有一条 ordersUnsettled 记录
    if (ordersUnsettled.value.length === 0) {
      ordersUnsettled.value.push({ type: 3, amount: 0 })
    }
  },
  { immediate: true }
)

const totalClaimed=ref(0)
watch(
  [orders, expenses, ordersUnsettled],
  () => {
    totalClaimed.value = claimTotalAmount.value
  },
  { deep: true }
)
const buildDetailList = () => [
  ...orders.value.map((o) => ({
    type: 1,
    orderNo: o.orderNo,
    orderAmount: o.orderAmount,
    amount: Number(o.amount || 0),
    remainingAmount: Number(o.remainingAmount || 0),
    shipAmount: Number(o.shipAmount || 0)
  })),
  ...expenses.value.map((e) => ({
    type: 2,
    expenseType: e.expenseType,
    expenseRemark: e.expenseRemark,
    amount: Number(e.amount || 0)
  })),
  // 订单未下金额直接提交，不管是否为0（但通常只有大于0才会提交）
  ...ordersUnsettled.value.map((o) => ({
    type: 3,
    amount: Number(o.amount || 0)
  })).filter(item => item.amount > 0) // 只提交大于0的记录
]

const submitting = ref(false)
const submit = async () => {
  if (submitting.value) return
  if (!collectionList.value.length) return showFailToast('未选择任何收款记录')

  //校验认款总金额是否等于收款总金额
  const totalClaimAmount = claimTotalAmount.value
  const collectionAmount = form.totalAmount
  if (Math.abs(totalClaimAmount - collectionAmount) > 0.01) {
    // 使用误差范围比较
    showFailToast(`认款总金额（${totalClaimAmount}）必须等于收款总金额（${collectionAmount}）`)
    return
  }
  if (!rulesClaim()) return
  const payload: any = {
    id: claimIdRef.value || undefined,
    claimDate: form.claimDate || today,
    type: form.type,
    status: form.status,
    salesmanName: form.salesmanName,
    customerName: form.customerName,
    customerCode: form.customerCode,
    currency: form.currency,
    currencyCode: form.currencyCode,
    totalAmount: form.totalAmount,
    detailList: buildDetailList(),
    collectionList: collectionList.value,
    remark: form.remark
  }
  try {
    submitting.value = true
    const res = await ClaimApi.createClaim(payload)
    console.log(res)

    if (res && res === '操作成功') {
      showSuccessToast('认领成功！')
      claimIdRef.value = null
      emits('success')
      emits('update:show', false)
    } else {
      showToast({
        message: res,
        wordBreak: 'break-all'
      })
    }
  } finally {
    submitting.value = false
  }
}

const temporarily = ref(false)
//暂存
const temporarilySubmit = async () => {
  if (temporarily.value) return
  if (!collectionList.value.length) return showFailToast('未选择任何收款记录')

  // if (!orders.value.length) return showFailToast('未选择订单')

  if (!rulesClaim()) return
  const payload: any = {
    id: claimIdRef.value || undefined,
    claimDate: form.claimDate || today,
    type: form.type,
    status: 2,
    salesmanName: form.salesmanName,
    customerName: form.customerName,
    customerCode: form.customerCode,
    currency: form.currency,
    currencyCode: form.currencyCode,
    totalAmount: form.totalAmount,
    detailList: buildDetailList(),
    collectionList: collectionList.value,
    remark: form.remark
  }
  try {
    submitting.value = true
    const res = await ClaimApi.createClaim(payload)
    console.log(res)
    if (res && res === '操作成功') {
      showSuccessToast('暂存成功，可前往 我的 继续填写')
      claimIdRef.value = null
      emits('success')
      emits('update:show', false)
    } else {
      showToast({
        message: res,
        wordBreak: 'break-all'
      })
    }
  } finally {
    submitting.value = false
  }
}

const rulesClaim = () => {
  // 验证
  if (!form.customerName || !form.customerCode) {
    showFailToast('客户名称和客户编码不能为空')
    return false
  }
  // 验证订单明细中的认款金额不能为0
  for (let i = 0; i < orders.value.length; i++) {
    const order = orders.value[i]
    if (!order.orderNo) {
      showFailToast(`第${i + 1}条订单明细的订单号不能为空`)
      return false
    }
    if (order.amount <= 0) {
      showFailToast(`第${i + 1}条订单明细的认款金额必须大于0`)
      return false
    }
  }

  // 验证费用明细中的金额不能为0
  for (let i = 0; i < expenses.value.length; i++) {
    const expense = expenses.value[i]
    if (
      expense.expenseType === undefined ||
      expense.expenseType === null ||
      expense.expenseType === ''
    ) {
      showFailToast(`第${i + 1}条费用明细的费用类别不能为空`)
      return false
    }
    if (expense.amount <= 0) {
      showFailToast(`第${i + 1}条费用明细的金额必须大于0`)
      return false
    }
  }

  return true
}
//重置表单和订单、费用
const resetForm = () => {
  Object.assign(form, {
    claimDate: today,
    type: 1,
    status: 0,
    salesmanName: userStore.user?.nickname || '',
    customerName: '',
    customerCode: '',
    totalAmount: 0.0,
    currency: ''
  })

  orders.value = []
  expenses.value = []
  // 重置时确保有一条订单未下记录
  ordersUnsettled.value = [{ type: 3, amount: 0 }]
}
</script>

<style scoped>
.border {
  border: 1px solid #f0f0f0;
}
.rounded {
  border-radius: 6px;
}

.collection-item {
  flex: 1;
  text-align: left;
}

.account-line {
  font-weight: 500;
  margin-bottom: 4px;
  text-align: left;
}

.amount-line,
.date-line {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  text-align: left;
}

.amount-line {
  margin-bottom: 2px;
}
</style>
