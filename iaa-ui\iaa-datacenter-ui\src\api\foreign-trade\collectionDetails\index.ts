import request from '@/config/axios'

export const CollectionDetailApi = {

  getCollectionDetailsPage: (params: any) => {
    return request.post({ url: `/collection/detail/page`, data:params })
  },
    getRateAndTotal: (params: any) => {
    return request.post({ url: `/collection/detail/getTotal`, data:params })
  },
  exportCollectionDetail: (params: any) => {
    return request.download({ url: `/collection/detail/export-excel`, params })
  },  
  getDate: () => {
    return request.get({ url: `/collection/detail/getDate`})
  },
  updateDate: (params: any) => {
    return request.get({ url: `/collection/detail/updateDate?isDate=${params}`})
  },
}