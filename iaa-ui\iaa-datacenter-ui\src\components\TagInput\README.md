# TagInput 组件

一个支持文本和标签混合输入的Vue 3组件，特别适用于AI Agent流程框架中的输入场景。

## 功能特性

- ✅ 支持文本和标签混合输入
- ✅ 使用 `{tag}` 格式自动解析为标签
- ✅ 退格键可以整体删除标签（类似@提及功能）
- ✅ 支持自定义标签样式
- ✅ 支持粘贴纯文本
- ✅ 实时解析为结构化数据
- ✅ 响应式设计
- ✅ TypeScript支持

## 基础用法

```vue
<template>
  <TagInput 
    v-model="inputValue"
    placeholder="输入内容，使用 {tag} 表示标签"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import TagInput from '@/components/TagInput.vue'

const inputValue = ref('请处理 {user:张三} 的数据请求')

const handleChange = (parsedContent) => {
  console.log('解析结果:', parsedContent)
}
</script>
```

## API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | string | '' | 输入框的值，支持v-model |
| placeholder | string | '输入内容，使用 {tag} 表示标签' | 占位符文本 |
| tagStyles | Record<string, string> | {} | 自定义标签样式（预留） |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value: string | 输入值变化时触发 |
| change | parsedContent: ParsedItem[] | 解析结果变化时触发 |

### ParsedItem 类型

```typescript
interface ParsedItem {
  type: 'text' | 'tag'
  value: string
}
```

## 标签类型

组件支持多种预设的标签类型，会自动应用对应的样式：

### 用户标签
```
{user:张三}
```
- 样式：绿色背景
- 用途：表示用户、人员

### 系统标签
```
{system:数据处理}
```
- 样式：橙色背景
- 用途：表示系统功能、模块

### 错误标签
```
{error:连接失败}
```
- 样式：红色背景
- 用途：表示错误信息、异常

### AI标签
```
{ai:GPT-4}
```
- 样式：紫色背景
- 用途：表示AI模型、智能功能

### 数据标签
```
{data:用户行为}
```
- 样式：青色背景
- 用途：表示数据类型、数据源

### 普通标签
```
{普通标签}
```
- 样式：蓝色背景
- 用途：通用标签

## 使用示例

### AI Agent 流程描述
```vue
<template>
  <TagInput 
    v-model="agentFlow"
    placeholder="描述AI Agent的处理流程"
    @change="handleFlowChange"
  />
</template>

<script setup>
const agentFlow = ref('AI Agent 接收到 {user:用户} 的请求，首先调用 {system:数据验证} 模块，然后使用 {ai:语言模型} 进行分析，最后将结果存储到 {data:数据库} 中')

const handleFlowChange = (parsed) => {
  // 解析结果可用于后续处理
  const steps = parsed.filter(item => item.type === 'tag')
  console.log('流程步骤:', steps)
}
</script>
```

### 数据处理流程
```vue
<template>
  <TagInput 
    v-model="dataFlow"
    placeholder="描述数据处理流程"
  />
</template>

<script setup>
const dataFlow = ref('从 {data:用户行为} 数据中提取特征，使用 {ai:机器学习} 算法进行分析，如果出现 {error:数据异常} 则通知 {user:管理员}')
</script>
```

## 键盘操作

- **退格键（Backspace）**：
  - 在标签内部或标签前面按退格键，会整体删除标签
  - 在普通文本中按退格键，正常删除字符
  
- **粘贴（Ctrl+V）**：
  - 支持粘贴纯文本
  - 粘贴的内容会保持原有的 `{tag}` 格式

## 样式自定义

可以通过CSS变量自定义标签样式：

```css
.tag.custom-style {
  background: var(--tag-bg, #00D4FF);
  color: var(--tag-color, #000);
  border-color: var(--tag-border, rgba(0, 212, 255, 0.3));
}
```

或者直接覆盖对应的CSS类：

```css
.tag.tag-user {
  background: #your-color;
  color: #your-text-color;
}
```

## 注意事项

1. 标签格式必须是 `{内容}` 的形式，大括号内不能包含其他大括号
2. 组件使用 `contenteditable` 实现，在某些浏览器中可能有兼容性差异
3. 建议在表单验证时检查解析后的结构化数据
4. 标签内容支持中文、英文、数字和常见符号

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新日志

### v1.0.0
- 初始版本
- 支持基础的文本和标签混合输入
- 支持退格键删除标签
- 支持多种预设标签类型
