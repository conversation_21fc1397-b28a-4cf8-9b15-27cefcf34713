<template>
  <NoModalDrawer v-model="visible" size="50%">
    <template #header>
      <div class="header-box w-full">
        <div class="pb-10px text-#000 text-16px font-bold"> 型号：{{ formData?.model }} </div>
        <el-form label-width="90" class="header-form" size="small" :disabled="isDisabled">
          <el-row>
            <el-col :span="8">
              <el-form-item label="操控方式：">
                <el-input v-model="formData.operationMode" placeholder="请输入操控方式" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="机箱颜色："
                ><el-input v-model="formData.chassisColor" placeholder="请输入机箱颜色"
              /></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主型号归集："
                ><el-input v-model="formData.mainModel" placeholder="请输入主型号归集"
              /></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产品分类：">
                <el-select v-model="formData.productsType" placeholder="请选择产品分类">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTS_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="场景分类：">
                <el-select v-model="formData.sceneType" placeholder="请选择场景分类">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.SCENE_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="技术分类：">
                <el-select v-model="formData.technologyType" placeholder="请选择技术分类">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.TECHNOLOGY_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  /> </el-select
              ></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缩略图" prop="imageUrl">
                <UploadImg v-model="formData.imageUrl" height="60px" width="60px" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-tabs v-model="activeTab" @tab-change="onTabChange">
          <!-- <el-tab-pane label="产品基本属性" name="base" /> -->
          <el-tab-pane label="生命周期属性" name="lifecycle" />
          <el-tab-pane label="规格属性" name="specification" />
          <el-tab-pane label="包装属性" name="packaging" />
          <el-tab-pane label="推广属性" name="promotion" />
          <el-tab-pane label="认证报告" name="certification" />
          <el-tab-pane label="更新记录" name="history" />
        </el-tabs>
      </div>
    </template>

    <div class="drawer-content">
      <!-- 产品基本属性标签页 -->
      <div v-show="activeTab === 'base'">
        <div class="details-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="edit-form"
            :disabled="!checkPermission(['report:prducts:update'])"
          />
        </div>
      </div>

      <!-- 生命周期属性标签页 -->
      <div v-show="activeTab === 'lifecycle'">
        <div class="details-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="edit-form"
            :disabled="isDisabled"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="生命周期" prop="lifeCycle">
                  <el-select
                    v-model="formData.lifeCycle"
                    placeholder="请选择生命周期"
                    @change="handleLifeCycleChange"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.LIFE_CYCLE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="定制/中性" prop="customizedNeutral">
                  <el-select v-model="formData.customizedNeutral" placeholder="请选择定制/中性">
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.CUSTOMIZED_NEUTRAL)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="产品分组" prop="productsGroup">
                  <el-input v-model="formData.productsGroup" placeholder="请输入产品分组" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="上市时间" prop="listedTime">
                  <el-date-picker
                    v-model="formData.listedTime"
                    type="date"
                    placeholder="选择上市时间"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="退市时间" prop="delistingTime">
                  <el-date-picker
                    v-model="formData.delistingTime"
                    type="date"
                    placeholder="选择退市时间"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="退市替代方案" prop="delistingProgramme">
                  <el-input
                    v-model="formData.delistingProgramme"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入退市替代方案"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="起订量" prop="moq">
                  <el-input v-model="formData.moq" type="number" placeholder="请输入起订量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开机费(￥)" prop="startupFee">
                  <el-input
                    v-model="formData.startupFee"
                    type="number"
                    placeholder="请输入开机费"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="独家情况" prop="remark">
                  <el-input
                    v-model="formData.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入独家情况信息"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 规格属性标签页 -->
      <div v-show="activeTab === 'specification'">
        <div class="details-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="edit-form"
            :disabled="isDisabled"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="产品尺寸" prop="productSize">
                  <el-input v-model="formData.productSize" placeholder="请输入产品尺寸" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="材料" prop="material">
                  <el-input v-model="formData.material" placeholder="请输入材料" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="覆盖范围" prop="coverageArea">
                  <el-input v-model="formData.coverageArea" placeholder="请输入覆盖范围" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="瓶子容量" prop="bottleCapacity">
                  <el-input v-model="formData.bottleCapacity" placeholder="请输入瓶子容量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="雾化量" prop="atomizationVolume">
                  <el-input v-model="formData.atomizationVolume" placeholder="请输入雾化量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="噪音（30cm）" prop="noiseThirty">
                  <el-input v-model="formData.noiseThirty" placeholder="请输入噪音（30cm）" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="噪音（1m）" prop="noiseMeter">
                  <el-input v-model="formData.noiseMeter" placeholder="请输入噪音（1m）" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供电方式" prop="supplyMode">
                  <el-input v-model="formData.supplyMode" placeholder="请输入供电方式" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="适用适配器" prop="applicableAdapter">
                  <el-input v-model="formData.applicableAdapter" placeholder="请输入适用适配器" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="额定功率" prop="ratedPower">
                  <el-input v-model="formData.ratedPower" placeholder="请输入额定功率" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电池类型" prop="batteryType">
                  <el-input v-model="formData.batteryType" placeholder="请输入电池类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电池容量" prop="batteryCapacity">
                  <el-input v-model="formData.batteryCapacity" placeholder="请输入电池容量" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="电池续航" prop="batteryLife">
                  <el-input v-model="formData.batteryLife" placeholder="请输入电池续航" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="气泵寿命" prop="airPumpLife">
                  <el-input v-model="formData.airPumpLife" placeholder="请输入气泵寿命" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="雾化芯/片选型" prop="atomizationCore">
                  <el-input v-model="formData.atomizationCore" placeholder="请输入雾化芯/片选型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="风扇转速" prop="fanSpeed">
                  <el-input v-model="formData.fanSpeed" placeholder="请输入风扇转速" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 包装属性标签页 -->
      <div v-show="activeTab === 'packaging'">
        <div class="details-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="edit-form"
            :disabled="isDisabled"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单个净重" prop="singleWeight">
                  <el-input v-model="formData.singleWeight" placeholder="请输入单个净重" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单个毛重" prop="singleMz">
                  <el-input v-model="formData.singleMz" placeholder="请输入单个毛重" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="整箱台数" prop="boxTotal">
                  <el-input v-model="formData.boxTotal" placeholder="请输入整箱台数" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="整箱尺寸" prop="boxSize">
                  <el-input v-model="formData.boxSize" placeholder="请输入整箱尺寸" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="整箱毛重" prop="boxMz">
                  <el-input v-model="formData.boxMz" placeholder="请输入整箱毛重" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 推广属性标签页 -->
      <div v-show="activeTab === 'promotion'">
        <div class="details-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="edit-form"
            :disabled="isDisabled"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="基本卖点" prop="sellingPoints">
                  <el-input
                    v-model="formData.sellingPoints"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入基本卖点"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="海报(链接)" prop="posterUrl">
                  <el-input v-model="formData.posterUrl" placeholder="请输入海报链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="详情页(链接)" prop="detailsUrl">
                  <el-input v-model="formData.detailsUrl" placeholder="请输入详情页链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="视频(链接)" prop="videoUrl">
                  <el-input v-model="formData.videoUrl" placeholder="请输入视频链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="包材(链接)" prop="packagingUrl">
                  <el-input v-model="formData.packagingUrl" placeholder="请输入包材链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="说明书(链接)" prop="instructionUrl">
                  <el-input v-model="formData.instructionUrl" placeholder="请输入说明书链接" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 认证报告标签页 -->
      <div v-show="activeTab === 'certification'">
        <div class="details-content">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            class="edit-form"
            :disabled="isDisabled"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="CE(链接)" prop="ceUrl">
                  <el-input v-model="formData.ceUrl" placeholder="请输入CE认证链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="RoHS(链接)" prop="rohsUrl">
                  <el-input v-model="formData.rohsUrl" placeholder="请输入RoHS认证链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="FCC(链接)" prop="fccUrl">
                  <el-input v-model="formData.fccUrl" placeholder="请输入FCC认证链接" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="ISED(链接)" prop="isedUrl">
                  <el-input v-model="formData.isedUrl" placeholder="请输入ISED认证链接" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 更新记录标签页 -->
      <div v-show="activeTab === 'history'">
        <div
          class="history-content"
          v-infinite-scroll="loadMore"
          :infinite-scroll-distance="20"
          :infinite-scroll-immediate="false"
          :infinite-scroll-disabled="loading || noMore"
        >
          <div class="history-header">
            <el-input
              v-model="searchKeyword"
              placeholder="按回车筛选变更记录"
              :prefix-icon="Search"
              clearable
              @keyup.enter="handleHistorySearch"
              @clear="handleHistorySearch"
              class="history-search"
            />
            <el-tag type="info" size="small"> 共 {{ total }} 条记录 </el-tag>
          </div>

          <div
            class="history-records"
            v-infinite-scroll="loadMore"
            :infinite-scroll-distance="20"
            :infinite-scroll-immediate="false"
            :infinite-scroll-disabled="loading || noMore"
          >
            <div v-for="(item, index) in displayList" :key="item.id || index" class="record-item">
              <div class="record-header">
                <div class="operator-info">
                  <el-avatar :size="32" class="operator-avatar">
                    <Icon icon="ep:user" />
                  </el-avatar>
                  <div class="operator-details">
                    <span class="operator-name">{{ item.userName || '系统' }}</span>
                  </div>
                </div>
                <div class="operation-time">
                  <el-tooltip :content="formatToDateTime(item.createTime)" placement="top">
                    <span class="time-text">{{ formatToDateTime(item.createTime) }}</span>
                  </el-tooltip>
                </div>
              </div>

              <div class="record-content">
                <div class="change-record">
                  <el-tag :type="getChangeType(item.editType)" size="small" class="change-tag">
                    {{ getChangeTypeText(item.editType) }}
                  </el-tag>
                  <div class="change-description">
                    <div
                      v-for="(line, index) in formatEditContent(item.editContent)"
                      :key="index"
                      class="change-line"
                    >
                      {{ line }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="3" animated />
            </div>

            <!-- 无更多数据提示 -->
            <div v-if="noMore && displayList.length > 0" class="no-more">
              <el-divider>
                <span class="no-more-text">没有更多记录了</span>
              </el-divider>
            </div>

            <!-- 空状态 -->
            <el-empty
              v-if="!loading && displayList.length === 0"
              description="暂无历史记录"
              :image-size="120"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <!-- <el-button @click="handleClose">取消</el-button> -->
        <el-button
          v-if="activeTab !== 'history' && isDisabled && checkPermission(['report:prducts:update'])"
          type="success"
          @click="handleEdit"
        >
          修改
        </el-button>
        <el-button
          v-if="activeTab !== 'history' && !isDisabled"
          type="primary"
          @click="handleSave"
          :loading="saveLoading"
          v-hasPermi="['report:prducts:update']"
        >
          保存
        </el-button>
      </div>
    </template>
  </NoModalDrawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { formatToDate, formatToDateTime } from '@/utils/dateUtil'
import { Search, Plus } from '@element-plus/icons-vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { PublicityApi } from '@/api/products/publicity'
import { checkPermission } from '@/store/modules/permission'
import NoModalDrawer from '@/components/NoModalDrawer/index.vue'

// 定义组件属性
defineOptions({ name: 'EditDrawer' })

// 定义事件
const emit = defineEmits(['success'])

// 响应式数据
const activeTab = ref('base')
const saveLoading = ref(false)
const currentId = ref<number | null>(null)

//是否有权限修改
const isDisabled = ref(true)
// 表单相关
const formRef = ref()
const formData = ref({
  model: '',
  productsType: 0,
  operationMode: '',
  chassisColor: '',
  mainModel: '',
  sceneType: 0,
  technologyType: 0,
  customizedNeutral: 0,
  productsGroup: '',
  lifeCycle: 0,
  listedTime: '',
  delistingTime: '',
  imageUrl: '',
  remark: '',
  delistingProgramme: '',
  moq: '',
  startupFee: '',
  productSize: '',
  material: '',
  coverageArea: '',
  bottleCapacity: '',
  atomizationVolume: '',
  noiseThirty: '',
  noiseMeter: '',
  supplyMode: '',
  applicableAdapter: '',
  ratedPower: '',
  batteryType: '',
  batteryCapacity: '',
  batteryLife: '',
  airPumpLife: '',
  atomizationCore: '',
  fanSpeed: '',
  singleWeight: '',
  singleMz: '',
  boxTotal: '',
  boxSize: '',
  boxMz: '',
  sellingPoints: '',
  posterUrl: '',
  detailsUrl: '',
  videoUrl: '',
  packagingUrl: '',
  instructionUrl: '',
  ceUrl: '',
  rohsUrl: '',
  fccUrl: '',
  isedUrl: ''
})

const formRules = {
  model: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
  productsType: [{ required: true, message: '请选择产品类型', trigger: 'change' }]
}

// 历史记录相关
const visible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const dateRange = ref<[string, string] | null>(null)
const historyList = ref<any[]>([])
const displayList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const noMore = ref(false)

// 标签页切换处理
const onTabChange = () => {
  if (activeTab.value === 'history' && currentId.value && displayList.value.length === 0) {
    loadRecordHistory()
  }
}

// 生命周期选择变化时的处理
const handleLifeCycleChange = (value: number) => {
  if (value === 9) {
    const currentDate = new Date()
    formData.value.delistingTime = formatToDate(currentDate) // 使用工具方法格式化日期
  } else {
    formData.value.delistingTime = ''
  }
}

onMounted(() => {
  // 初始化表单数据
  activeTab.value = 'lifecycle'
})
// 方法定义
const open = (type: 'create' | 'update', id?: number, data?: any) => {
  visible.value = true
  currentId.value = id || null

  if (type === 'update' && data) {
    // 手动将相关字段从字符串转换为整数
    const convertedData = {
      ...data,
      productsType: parseInt(data.productsType, 10),
      sceneType: parseInt(data.sceneType, 10),
      technologyType: parseInt(data.technologyType, 10),
      lifeCycle: parseInt(data.lifeCycle, 10),
      customizedNeutral: parseInt(data.customizedNeutral, 10)
    }

    Object.assign(formData.value, convertedData)
    // 如果是编辑模式，加载该记录的更新历史
    loadRecordHistory()
  } else {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
  resetRecordData()
}

const handleEdit = () => {
  isDisabled.value = false
}

const resetForm = () => {
  formData.value = {
    model: '',
    productsType: 0,
    operationMode: '',
    chassisColor: '',
    mainModel: '',
    sceneType: 0,
    technologyType: 0,
    customizedNeutral: 0,
    productsGroup: '',
    lifeCycle: 0,
    listedTime: '',
    delistingTime: '',
    imageUrl: '',
    remark: '',
    delistingProgramme: '',
    moq: '',
    startupFee: '',
    productSize: '',
    material: '',
    coverageArea: '',
    bottleCapacity: '',
    atomizationVolume: '',
    noiseThirty: '',
    noiseMeter: '',
    supplyMode: '',
    applicableAdapter: '',
    ratedPower: '',
    batteryType: '',
    batteryCapacity: '',
    batteryLife: '',
    airPumpLife: '',
    atomizationCore: '',
    fanSpeed: '',
    singleWeight: '',
    singleMz: '',
    boxTotal: '',
    boxSize: '',
    boxMz: '',
    sellingPoints: '',
    posterUrl: '',
    detailsUrl: '',
    videoUrl: '',
    packagingUrl: '',
    instructionUrl: '',
    ceUrl: '',
    rohsUrl: '',
    fccUrl: '',
    isedUrl: ''
  }
  formRef.value?.clearValidate()
}

const resetRecordData = () => {
  historyList.value = []
  displayList.value = []
  currentPage.value = 1
  noMore.value = false
  searchKeyword.value = ''
  dateRange.value = null
}

// 保存数据
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saveLoading.value = true

    console.log('formData', formData.value)
    const res = await PublicityApi.update(formData.value)
    if (res) {
      ElMessage.success('保存成功')
      isDisabled.value = true
      emit('success')
      // handleClose()
    } else {
      ElMessage.error(
        '型号: ' +
          formData.value.model +
          ',操控方式: ' +
          formData.value.operationMode +
          ',颜色: ' +
          formData.value.chassisColor +
          ' 已存在,请检查后重新提交！'
      )
    }
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saveLoading.value = false
  }
}

// 加载记录历史
const loadRecordHistory = async (isLoadMore = false) => {
  if (loading.value) return

  loading.value = true
  try {
    const params = {
      editId: currentId.value,
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      editContent: searchKeyword.value
    }

    // 调用实际API
    const response = await PublicityApi.getHistory(params)
    if (isLoadMore) {
      historyList.value.push(...response.list)
    } else {
      historyList.value = response.list
    }

    total.value = response.total
    displayList.value = [...historyList.value]
    if (historyList.value.length >= total.value) {
      noMore.value = true
    }
  } catch (error) {
    console.error('加载记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (loading.value || noMore.value) return
  currentPage.value++
  loadRecordHistory(true)
}

// 搜索记录
const handleHistorySearch = () => {
  currentPage.value = 1
  noMore.value = false
  loadRecordHistory()
}

// 获取变更类型样式
const getChangeType = (type: number) => {
  const typeMap = {
    0: 'success', // 新增
    1: 'warning', // 修改
    2: 'danger' // 删除
  }
  return typeMap[type] || 'info'
}

// 获取变更类型文本
const getChangeTypeText = (type: number) => {
  const textMap = {
    0: '新增',
    1: '修改',
    2: '删除'
  }
  return textMap[type] || '操作'
}

// 格式化editContent，按分号分行显示
const formatEditContent = (editContent: string) => {
  if (!editContent) return []

  return editContent
    .split(';')
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: calc(100% - 120px);
  overflow-y: auto;

  .details-content {
    .edit-form {
      .image-uploader {
        :deep(.el-upload) {
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: all 0.3s;
          width: 120px;
          height: 120px;

          &:hover {
            border-color: #409eff;
          }
        }

        .image-uploader-icon {
          font-size: 28px;
          color: #8c939d;
          width: 120px;
          height: 120px;
          text-align: center;
          line-height: 120px;
        }

        .uploaded-image {
          width: 120px;
          height: 120px;
          object-fit: cover;
        }
      }
    }
  }

  .history-content {
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .history-search {
        width: 300px;
      }
    }

    .history-records {
      .record-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;

        .record-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .operator-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .operator-details {
              display: flex;
              flex-direction: column;

              .operator-name {
                font-weight: 500;
                color: #303133;
              }

              .operator-role {
                font-size: 12px;
                color: #909399;
              }
            }
          }

          .operation-time {
            .time-text {
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .record-content {
          .change-record {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            margin-bottom: 8px;

            .change-tag {
              flex-shrink: 0;
              margin-top: 2px;
            }

            .change-description {
              flex: 1;
              color: #606266;
              line-height: 1.5;

              .change-line {
                margin-bottom: 4px;

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }

          .change-details {
            .details-json {
              background: #fff;
              border-radius: 4px;
              padding: 12px;

              pre {
                margin: 0;
                font-size: 12px;
                color: #606266;
                white-space: pre-wrap;
              }
            }
          }
        }
      }

      .loading-container {
        padding: 20px;
      }

      .no-more-records {
        text-align: center;
        margin: 20px 0;

        .no-more-text {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.header-form .el-form-item) {
  margin-bottom: 5px !important;
}

:deep(.header-form .el-form-item__content) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-form-item__content) {
  background-color: #f5f7f9 !important;
  border-radius: 5px;
  padding: 0 5px;
  cursor: pointer;
}

:deep(.el-progress-bar__inner),
:deep(.el-progress-bar__outer) {
  border-radius: 0 !important;
}
</style>
