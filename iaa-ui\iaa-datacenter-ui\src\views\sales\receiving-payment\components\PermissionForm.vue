<template>
  <Dialog title="收款状况表权限管理" v-model="visible" width="80%">
    <vxe-table
      :data="permissionUserList"
      align="center"
      :header-cell-config="{ height: 30 }"
      :cell-config="{ height: 30 }"
      height="400"
      :loading="loading"
      border
      stripe
    >
      <vxe-column title="用户" field="nickname" width="100" />
      <vxe-column title="查看所有" field="viewAll" width="120">
        <template #default="{ row }">
          <el-switch
            v-model="row.viewAll"
            @change="onRowChangeSave(row)"
            :active-value="true"
            :inactive-value="false"
          />
        </template>
      </vxe-column>
      <vxe-column title="可查看范围" field="viewUser">
        <template #default="{ row }">
          <el-select
            v-model="row.viewUser"
            multiple
            filterable
            size="small"
            @change="onRowChangeSave(row)"
          >
            <el-option
              v-for="(item, index) in sellerList"
              :key="index"
              :value="item"
              :label="item"
            />
          </el-select>
        </template>
      </vxe-column>
    </vxe-table>
  </Dialog>
</template>

<script lang="ts" setup>
import { RecPaymentApi } from '@/api/sales/receiving-payment'

const visible = ref(false)
const loading = ref(false)
const sellerList = ref<string[]>([])
const permissionUserList = ref<any[]>([])
const message = useMessage()

const openForm = () => {
  visible.value = true
  onList()
}

const onRowChangeSave = async (row: any) => {
  if (row?.viewUser?.length > 0) {
    row.viewAll = false
  }
  await RecPaymentApi.savePermission(row)
  message.success('保存成功')
}

const onList = async () => {
  loading.value = true
  try {
    sellerList.value = await RecPaymentApi.getAllSellerList()
    permissionUserList.value = await RecPaymentApi.getPermissionList()
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
