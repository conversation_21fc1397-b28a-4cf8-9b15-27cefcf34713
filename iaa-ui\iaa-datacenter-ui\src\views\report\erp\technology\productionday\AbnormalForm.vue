<template>
  <div class="dialogContainer" v-show="dialogVisible" v-resizer>
    <Dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      :width="mobile ? '100%' : '80%'"
      fullscreen
      v-dialogDrag
    >
      <el-form ref="formRef" :model="abnormalData" :rules="formRules" label-width="120px">
        <el-card class="mb-4" shadow="never">
          <!-- 全选按钮 -->
          <div v-if="!mobile" style="margin-bottom: 10px">
            <el-button @click="addNewRow" type="primary" size="small"> 新增 </el-button>
            <span style="margin-left: 10px; color: #666">
              已选择 {{ selectedRows.length }} 项
            </span>
          </div>
          <!-- 批量赋值按钮（移动端） -->
          <div v-if="mobile" class="mobile-batch-container">
            <div class="batch-assignment-sticky">
              <el-button @click="toggleSelectAll" size="small">
                {{ isAllSelected ? '取消' : '全选' }}
              </el-button>
              <el-button @click="batchAssignment" type="primary" size="small" style="width: 65px"
                >批量赋值</el-button
              >
              <el-button @click="batchCopyValues" type="info" size="small" style="width: 65px"
                >插入选中</el-button
              >
              <el-button @click="batchDeleteValues" type="danger" size="small" style="width: 65px"
                >删除选中</el-button
              >
            </div>
          </div>
          <div v-if="mobile" class="card-list-container">
            <div v-for="(row, index) in abnormalData" :key="index" class="card-item">
              <div class="card-title">
                <el-checkbox
                  v-model="row._checked"
                  @click.stop
                  style="top: 2px; padding: 0; height: 0"
                  @change="(value) => handleCheckboxChange(row, Boolean(value))"
                />
                第 {{ index + 1 }} 条 / 共 {{ abnormalData.length }} 条
                <span>
                  <el-button style="padding: 0; color: #fff" link @click="addNewRow">
                    <icon icon="ep:plus" />
                  </el-button>
                  <el-button
                    v-if="!row.isEditing"
                    style="padding: 0; color: #fff"
                    link
                    @click="enterEditMode(index)"
                  >
                    <icon icon="ep:edit" />
                  </el-button>
                  <el-button style="padding: 0; color: #fff" link @click="removeBodyRow(index)">
                    <icon icon="ep:delete" />
                  </el-button>
                </span>
              </div>
              <!-- 只读模式 -->
              <div v-if="!row.isEditing" class="mobile-body-info">
                <div class="info-row">
                  <span class="info-label">生产时间段:</span>
                  <span class="info-value"
                    >{{ row.productionTimeStart }}-{{ row.productionTimeEnd }}</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">异常时间段:</span>
                  <span class="info-value"
                    >{{ row.abnormalTimeStart }}-{{ row.abnormalTimeEnd }}</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">工单号:</span>
                  <span class="info-value">{{ row.productionOrderCode }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">销售订单号:</span>
                  <span class="info-value">{{ row.salesOrderCode }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">品号:</span>
                  <span class="info-value">{{ row.productNo }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">品名:</span>
                  <span class="info-value">{{ row.modelsOrColor }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">异常数量:</span>
                  <span class="info-value">{{ row.abnormalReportNum }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">单位:</span>
                  <span class="info-value">{{ row.units }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">问题点:</span>
                  <span class="info-value">{{ row.abnormalRemark }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">临时对策:</span>
                  <span class="info-value">{{ row.abnormalCountermeasures }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">人数:</span>
                  <span class="info-value">{{ row.abnormalNum }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">总工时:</span>
                  <span class="info-value">{{ row.abnormalWork }}</span>
                </div>
              </div>
              <el-form v-else class="mobile-body-form">
                <el-row>
                  <el-col :span="24" v-if="!row.isNew">
                    <el-form-item label="生产时间段">
                      <div class="time-range-row">
                        <div v-if="row.isNew">
                          <el-time-select
                            v-model="row.productionTimeStart"
                            placeholder="起始时间"
                            :start="'08:30'"
                            :end="'23:30'"
                            :step="'00:15'"
                            placement="top-start"
                          />
                          <span class="time-range-separator">-</span>
                          <el-time-select
                            v-model="row.productionTimeEnd"
                            placeholder="结束时间"
                            :start="'08:30'"
                            :end="'23:30'"
                            :step="'00:15'"
                            :min-time="row.productionTimeStart"
                            placement="top-start"
                          />
                        </div>
                        <div v-else>
                          {{ row.productionTimeStart }}-{{ row.productionTimeEnd }}
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="异常时间段">
                      <div class="time-range-row">
                        <el-time-select
                          v-model="row.abnormalTimeStart"
                          placeholder="起始时间"
                          :start="'08:30'"
                          :end="'23:30'"
                          :step="'00:15'"
                          placement="top-start"
                        />
                        <span class="time-range-separator">-</span>
                        <el-time-select
                          v-model="row.abnormalTimeEnd"
                          placeholder="结束时间"
                          :start="'08:30'"
                          :end="'23:30'"
                          :step="'00:15'"
                          :min-time="row.abnormalTimeStart"
                          placement="top-start"
                        />
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="工单号">
                      <div v-if="row.isNew">
                        <el-autocomplete
                          v-model="row.productionOrderCode"
                          :fetch-suggestions="
                            (queryString, cb) => queryWorkSalesOptions(queryString, cb, 0)
                          "
                          placeholder="输入工单号"
                          style="width: 100%"
                          @select="(item) => handleProductionOrderSelect(item, index)"
                          :trigger-on-focus="false"
                          :debounce="300"
                          popper-class="production-order-autocomplete"
                          placement="top-start"
                        >
                          <template #default="{ item }">
                            <div class="autocomplete-item">
                              <div class="main-text"
                                >{{ item.productionOrderCode }}-{{ item.productNo }}</div
                              >
                            </div>
                          </template>
                        </el-autocomplete>
                      </div>
                      <div v-else>
                        {{ row.productionOrderCode }}
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="销售订单号">
                      <div v-if="row.isNew">
                        <el-autocomplete
                          v-model="row.salesOrderCode"
                          :fetch-suggestions="
                            (queryString, cb) => queryWorkSalesOptions(queryString, cb, 1)
                          "
                          placeholder="输入销售订单号"
                          style="width: 100%"
                          @select="(item) => handleSalesOrderSelect(item, index)"
                          :trigger-on-focus="false"
                          :debounce="300"
                          popper-class="production-order-autocomplete"
                          placement="top-start"
                        >
                          <template #default="{ item }">
                            <div class="autocomplete-item">
                              <div class="main-text"
                                >{{ item.salesOrderCode }}-{{ item.productNo }}</div
                              >
                            </div>
                          </template>
                        </el-autocomplete>
                      </div>
                      <div v-else>
                        {{ row.salesOrderCode }}
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品号">
                      <div v-if="row.isNew">
                        <el-autocomplete
                          v-model="row.productNo"
                          :fetch-suggestions="
                            (queryString, cb) =>
                              queryProductNo(
                                queryString,
                                cb,
                                row.productionOrderCode,
                                row.salesOrderCode
                              )
                          "
                          placeholder="输入品号查询"
                          style="width: 100%"
                          @select="(item) => handleSalesOrderSelect(item, index)"
                          :trigger-on-focus="false"
                          :debounce="300"
                          popper-class="production-order-autocomplete"
                          placement="top-start"
                        >
                          <template #default="{ item }">
                            <div class="autocomplete-item">
                              <div class="main-text">{{ item.productNo }}</div>
                            </div>
                          </template>
                        </el-autocomplete>
                      </div>
                      <div v-else>
                        {{ row.productNo }}
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品名">
                      {{ row.modelsOrColor }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="异常数量">
                      <el-input
                        v-model="row.abnormalReportNum"
                        min="0"
                        @input="(val) => (row.abnormalReportNum = Number(val))"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="单位">
                      {{ row.units }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="问题点">
                      <el-input v-model="row.abnormalRemark" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="临时对策">
                      <el-input v-model="row.abnormalCountermeasures" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="人数">
                      <el-input
                        v-model="row.abnormalNum"
                        min="0"
                        @input="(val) => (row.abnormalNum = Number(val))"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="总工时">
                      <el-input
                        v-model="row.abnormalWork"
                        type="number"
                        :min="0"
                        @input="(val) => (batchForm.abnormalWork = Number(val))"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <el-table
            v-else
            :data="abnormalData"
            border
            stripe
            scrollbar-always-on
            ref="tableRef"
            @selection-change="handleSelectionChange"
            @row-contextmenu="rightClick"
            @row-click="clickTableRow"
            style="min-height: 250px; height: calc(100vh - 455px)"
          >
            ">
            <el-table-column type="selection" width="30" align="center" fixed="left" />
            <el-table-column label="序号" type="index" width="40" align="center" fixed="left" />
            <el-table-column label="生产时间段" min-width="100">
              <template #default="{ row }">
                <!-- <div v-if="row.isNew" class="time-range-row">
                  <el-time-select
                    v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'" :end="'23:30'"
                    :step="'00:15'" placement="top-start" />
                  <span class="time-range-separator">-</span>
                  <el-time-select
                    v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'" :end="'23:30'"
                    :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                </div> -->
                <div :style="{ color: row.isOldNew && row.id === undefined ? 'red' : '' }">
                  {{ row.productionTimeStart }}-{{ row.productionTimeEnd }}
                  <!-- <el-tag v-if="row.isOldNew && row.id==undefined" type="primary">
                    本次新增
                  </el-tag> -->
                </div>
              </template>
            </el-table-column>
            <el-table-column label="工单号" min-width="150" prop="productionOrderCode">
              <template #default="{ row, $index }">
                <div v-if="row.workType === 1 || row.isNew">
                  <el-autocomplete
                    v-model="row.productionOrderCode"
                    :fetch-suggestions="
                      (queryString, cb) => queryWorkSalesOptions(queryString, cb, 0)
                    "
                    placeholder="输入工单号"
                    style="width: 100%"
                    @select="(item) => handleProductionOrderSelect(item, $index)"
                    :trigger-on-focus="false"
                    :debounce="300"
                    popper-class="production-order-autocomplete"
                    placement="top-start"
                  >
                    <template #default="{ item }">
                      <div class="autocomplete-item">
                        <div class="main-text"
                          >{{ item.productionOrderCode }}-{{ item.productNo }}</div
                        >
                      </div>
                    </template>
                  </el-autocomplete>
                </div>
                <div v-else>
                  {{ row.productionOrderCode }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="销售订单号" min-width="150" prop="salesOrderCode">
              <template #default="{ row, $index }">
                <div v-if="row.workType === 1 || row.isNew">
                  <el-autocomplete
                    v-model="row.salesOrderCode"
                    :fetch-suggestions="
                      (queryString, cb) => queryWorkSalesOptions(queryString, cb, 1)
                    "
                    placeholder="输入销售订单号"
                    style="width: 100%"
                    @select="(item) => handleSalesOrderSelect(item, $index)"
                    :trigger-on-focus="false"
                    :debounce="300"
                    popper-class="production-order-autocomplete"
                    placement="top-start"
                  >
                    <template #default="{ item }">
                      <div class="autocomplete-item">
                        <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                </div>
                <div v-else>
                  {{ row.salesOrderCode }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="品号" min-width="100" prop="productNo">
              <template #default="{ row, $index }">
                <div v-if="row.workType === 1 || row.isNew">
                  <el-autocomplete
                    v-model="row.productNo"
                    :fetch-suggestions="
                      (queryString, cb) =>
                        queryProductNo(queryString, cb, row.productionOrderCode, row.salesOrderCode)
                    "
                    placeholder="输入品号查询"
                    style="width: 100%"
                    @select="(item) => handleSalesOrderSelect(item, $index)"
                    :trigger-on-focus="false"
                    :debounce="300"
                    popper-class="production-order-autocomplete"
                    placement="top-start"
                  >
                    <template #default="{ item }">
                      <div class="autocomplete-item">
                        <div class="main-text">{{ item.productNo }}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                </div>
                <div v-else>
                  {{ row.productNo }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="机型/颜色（品名）" min-width="120" prop="modelsOrColor">
              <template #default="{ row }">
                {{ row.modelsOrColor }}
              </template>
            </el-table-column>
            <el-table-column
              label="异常数量"
              min-width="60"
              prop="abnormalReportNum"
              align="center"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalReportNum"
                  type="number"
                  min="0"
                  class="no-spin-input"
                  @input="(val) => (row.abnormalReportNum = Number(val))"
                />
              </template>
            </el-table-column>
            <!-- <el-table-column label="单位" min-width="30" prop="units">
              <template #default="{ row }">
                {{ row.units }}
              </template>
            </el-table-column> -->
            <el-table-column label="异常时间段" min-width="180">
              <template #default="{ row }">
                <div class="time-range-row">
                  <el-time-select
                    v-model="row.abnormalTimeStart"
                    placeholder="起始时间"
                    :start="'08:30'"
                    :end="'23:30'"
                    :step="'00:15'"
                    placement="top-start"
                  />
                  <span class="time-range-separator">-</span>
                  <el-time-select
                    v-model="row.abnormalTimeEnd"
                    placeholder="结束时间"
                    :start="'08:30'"
                    :end="'23:30'"
                    :step="'00:15'"
                    :min-time="row.abnormalTimeStart"
                    placement="top-start"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="人数" min-width="50" prop="abnormalNum">
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalNum"
                  type="number"
                  min="0"
                  class="no-spin-input"
                  @input="(val) => (row.abnormalNum = Number(val))"
                />
              </template>
            </el-table-column>
            <el-table-column label="总工时" min-width="60" prop="abnormalWork">
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalWork"
                  type="number"
                  class="no-spin-input"
                  :readonly="true"
                  @input="(val) => (row.abnormalWork = Number(val))"
                />
              </template>
            </el-table-column>
            <el-table-column label="问题点" min-width="180" prop="abnormalRemark">
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalRemark"
                  type="textarea"
                  placeholder="请输入问题点"
                  @keydown.enter="handleAutoNumber($event, row, 'abnormalRemark')"
                  @focus="initializeTextContent(row, 'abnormalRemark')"
                  style="width: 100%; max-height: 50px"
                />
              </template>
            </el-table-column>

            <el-table-column label="临时对策" min-width="180" prop="abnormalCountermeasures">
              <template #default="{ row }">
                <el-input
                  v-model="row.abnormalCountermeasures"
                  type="textarea"
                  placeholder="请输入临时对策"
                  @keydown.enter="handleAutoNumber($event, row, 'abnormalCountermeasures')"
                  @focus="initializeTextContent(row, 'abnormalCountermeasures')"
                  style="width: 100%; max-height: 50px"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="50" fixed="right">
              <template #default="{ $index }">
                <el-button type="danger" link @click="removeBodyRow($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 右键菜单 -->
          <div
            id="contextMenu"
            class="context-menu"
            style="position: fixed; display: none; z-index: 1000"
          >
            <ul class="menu-list">
              <li @click="batchAssignValues" class="menu-item"> 列批量赋值 </li>
              <li @click="batchCopyValues" class="menu-item"> 插入选中行 </li>
              <li @click="batchDeleteValues" class="menu-item"> 批量删除 </li>
            </ul>
          </div>
        </el-card>
      </el-form>
      <template #footer>
        <div
          style="display: flex; align-items: center; justify-content: space-between; width: 100%"
        >
          <span style="font-size: 14px; color: #333">异常分组总工时：{{ totalHours }}</span>
          <div>
            <el-button
              @click="submitForm"
              type="primary"
              :disabled="formLoading"
              :loading="formLoading"
              >{{ formLoading ? '提交中...' : '确 定' }}</el-button
            >
            <el-button @click="dialogVisible = false" :disabled="formLoading">取 消</el-button>
          </div>
        </div>
      </template>
    </Dialog>

    <el-dialog v-model="batchDialogVisible" title="批量赋值" width="400px">
      <el-form :model="batchForm" label-width="90px">
        <el-form-item label="异常时间段">
          <div class="time-range-row">
            <el-time-select
              v-model="batchForm.abnormalTimeStart"
              placeholder="起始时间"
              :start="'08:30'"
              :end="'23:30'"
              :step="'00:15'"
              placement="top-start"
            />
            <span class="time-range-separator">-</span>
            <el-time-select
              v-model="batchForm.abnormalTimeEnd"
              placeholder="结束时间"
              :start="'08:30'"
              :end="'23:30'"
              :step="'00:15'"
              :min-time="batchForm.abnormalTimeStart"
              placement="top-start"
            />
          </div>
        </el-form-item>
        <el-form-item label="问题点">
          <el-input v-model="batchForm.abnormalRemark" />
        </el-form-item>
        <el-form-item label="临时对策">
          <el-input v-model="batchForm.abnormalCountermeasures" />
        </el-form-item>
        <el-form-item label="人数">
          <el-input
            v-model="batchForm.abnormalNum"
            type="number"
            :min="0"
            @input="(val) => (batchForm.abnormalNum = Number(val))"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmBatchAssignment">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { DayApi, AbnormalDayVO } from '@/api/report/technology/production'
import { useCache } from '@/hooks/web/useCache'
import { cloneDeep } from 'lodash-es'
import { is } from '@/utils/is'

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

const formRules = reactive({
  number: [{ required: true, message: '请输入人数', trigger: 'blur' }],
  abnormalFormTimeStart: [{ required: true, message: '请选择起始时间', trigger: 'change' }],
  abnormalFormTimeEnd: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const { wsCache } = useCache()
// 计算有效分钟数并更新工时
const parseTime = (timeStr: string): Date => {
  const now = new Date()
  const [hours, minutes] = timeStr.split(':').map(Number)
  return new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes)
}

const isRestPeriod = (time: Date): boolean => {
  const restPeriods = [
    { start: '12:00', end: '13:00' },
    { start: '17:30', end: '18:00' }
  ]
  for (const period of restPeriods) {
    const restStart = parseTime(period.start)
    const restEnd = parseTime(period.end)
    if (time >= restStart && time < restEnd) return true
  }
  return false
}

const totalHours = ref(0)

/**
 * 计算异常分组总工时
 */
const calculateAbnormalGroupTotalWork = () => {
  const groupMap = new Map<string, number>()

  for (const row of abnormalData.value) {
    // 判断是否具备异常信息
    if (!row.abnormalRemark || !row.abnormalTimeStart || !row.abnormalTimeEnd) continue

    const timeRange = `${row.abnormalTimeStart}-${row.abnormalTimeEnd}`
    const remark = row.abnormalRemark.trim()

    // 构造唯一key
    const key = `${timeRange}_${remark}`

    // 只保留第一个出现的值
    if (!groupMap.has(key)) {
      groupMap.set(key, row.abnormalWork || 0)
    }
  }

  // 求和
  const total = Array.from(groupMap.values()).reduce((sum, val) => sum + val, 0)
  return total
}

const calculateEffectiveMinutes = (start: string, end: string): number => {
  const startDate = parseTime(start)
  const endDate = parseTime(end)
  if (endDate <= startDate) return 0
  let totalMinutes = 0
  let current = new Date(startDate)
  while (current < endDate) {
    const nextMinute = new Date(current.getTime() + 60000)
    const minuteEndTime = nextMinute > endDate ? endDate : nextMinute
    if (!isRestPeriod(current)) {
      totalMinutes += (minuteEndTime.getTime() - current.getTime()) / 60000
    }
    current = minuteEndTime
  }
  return totalMinutes
}

// 表身数据（其他字段）
const abnormalData = ref<any[]>([])

// 工时监听
watch(
  abnormalData,
  (rows) => {
    const groupMap = new Map<string, number>()
    rows.forEach((row) => {
      // 只有当异常人数大于0且时间段有效时才计算工时
      if (
        row.abnormalTimeStart !== undefined &&
        row.abnormalTimeEnd !== undefined &&
        row.abnormalNum > 0
      ) {
        const minutes = calculateEffectiveMinutes(row.abnormalTimeStart, row.abnormalTimeEnd)
        row.abnormalWork = parseFloat(((minutes / 60) * (row.abnormalNum || 0)).toFixed(3))
      }

      if (
        row.abnormalRemark &&
        row.abnormalTimeStart &&
        row.abnormalTimeEnd &&
        row.abnormalNum > 0
      ) {
        const timeRange = `${row.abnormalTimeStart}-${row.abnormalTimeEnd}`
        const remark = row.abnormalRemark.trim()

        // 构造唯一key
        const key = `${timeRange}_${remark}`

        // 只保留第一个出现的值
        if (!groupMap.has(key)) {
          groupMap.set(key, row.abnormalWork || 0)
        }
      }
    })
    totalHours.value = Array.from(groupMap.values()).reduce((sum, val) => sum + val, 0)
  },
  { deep: true, immediate: true }
)

// 选中的行数据
const selectedRows = ref<any[]>([])

// 表格引用
const tableRef = ref()

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

//移动端处理选择变化
const handleCheckboxChange = (row: any, checked: boolean) => {
  if (checked) {
    // 如果选中，加入 selectedRows
    if (!selectedRows.value.includes(row)) {
      selectedRows.value.push(row)
    }
  } else {
    // 如果取消选中，从 selectedRows 中移除
    selectedRows.value = selectedRows.value.filter((item) => item !== row)
  }
}

// 进入编辑模式
const enterEditMode = (index: number) => {
  // 为该行设置编辑状态
  abnormalData.value[index].isEditing = true
}
// 获取工单销售订单数据
const queryWorkSalesOptions = (queryString: string, cb: Function, type: number) => {
  if (!queryString) {
    cb([])
    return
  }

  const placeholderText = type === 0 ? '生产工单号' : '销售订单号'
  const loadingItem = {
    [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '加载中...',
    loading: true
  }
  const noDataItem = {
    [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '暂无数据',
    noData: true
  }
  const errorItem = {
    [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '查询失败',
    error: true
  }

  // 先同步返回加载状态
  cb([loadingItem])

  const data = {
    docNo: queryString,
    type: type
  }
  DayApi.getWorkSalesOptions(data)
    .then((response) => {
      const suggestions = response || []
      cb(suggestions.length > 0 ? suggestions : [noDataItem])
    })
    .catch((error) => {
      console.error(`查询${placeholderText}失败:`, error)
      cb([errorItem])
    })
}
//选择后 工单号
const handleProductionOrderSelect = async (item: any, rowIndex: number) => {
  const row = abnormalData.value[rowIndex]
  if (row && item) {
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.workOrderNum = item.workOrderNum
    row.units = item.units
    row.salesman = item.salesman
  }
}

// 查询品号
const queryProductNo = (
  queryString: string,
  cb: Function,
  productionOrderCode: string,
  salesOrderCode: string
) => {
  if (!queryString) {
    cb([])
    return
  }
  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productNo: '加载中...', loading: true }])
  //把工单号和销售订单号和输入的品号都传给后端
  const data = {
    productionOrderCode: productionOrderCode, // 生产工单号
    salesOrderCode: salesOrderCode, // 销售订单号
    productNo: queryString // 输入的品号
  }
  DayApi.getProductNo(data)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productNo: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ productNo: '查询失败', error: true }])
    })
}

// 选择后 销售订单号的处理
const handleSalesOrderSelect = async (item: any, rowIndex: number) => {
  const row = abnormalData.value[rowIndex]
  // 设置生产工单号
  row.productionOrderCode = item.productionOrderCode
  row.salesOrderCode = item.salesOrderCode
  row.productNo = item.productNo
  row.modelsOrColor = item.modelsOrColor
  row.workOrderNum = item.workOrderNum
  row.units = item.units
  row.salesman = item.salesman
}
// 新增可编辑行
const addNewRow = () => {
  const newRow = createEditableRow()
  abnormalData.value.push(newRow)
}
const isAllSelected = ref(false)
const toggleSelectAll = () => {
  const checked = !isAllSelected.value
  isAllSelected.value = checked

  abnormalData.value.forEach((row) => {
    row._checked = checked
  })

  if (checked) {
    selectedRows.value = [...abnormalData.value]
  } else {
    selectedRows.value = []
  }
}
const batchAssignment = () => {
  if (selectedRows.value.length === 0) {
    message.error('请先选择行')
    return
  }
  // 初始化表单数据
  batchForm.value = {
    abnormalTimeStart: '',
    abnormalTimeEnd: '',
    abnormalRemark: '',
    abnormalCountermeasures: '',
    abnormalNum: 0,
    abnormalWork: 0
  }
  batchDialogVisible.value = true
}

// 批量赋值表单数据
const batchForm = ref({
  abnormalTimeStart: '',
  abnormalTimeEnd: '',
  abnormalRemark: '',
  abnormalCountermeasures: '',
  abnormalNum: 0,
  abnormalWork: 0
})

const confirmBatchAssignment = () => {
  const {
    abnormalTimeStart,
    abnormalTimeEnd,
    abnormalRemark,
    abnormalCountermeasures,
    abnormalNum,
    abnormalWork
  } = batchForm.value

  // 验证时间有效性
  if (abnormalTimeStart && abnormalTimeEnd) {
    const startTime = parseTime(abnormalTimeStart)
    const endTime = parseTime(abnormalTimeEnd)
    if (endTime <= startTime) {
      message.error('结束时间必须晚于开始时间')
      return
    }
  }

  selectedRows.value.forEach((row) => {
    if (abnormalTimeStart !== '') row.abnormalTimeStart = abnormalTimeStart
    if (abnormalTimeEnd !== '') row.abnormalTimeEnd = abnormalTimeEnd
    if (abnormalRemark !== '') row.abnormalRemark = abnormalRemark
    if (abnormalCountermeasures !== '') row.abnormalCountermeasures = abnormalCountermeasures
    if (abnormalNum !== null) row.abnormalNum = abnormalNum
    if (abnormalWork !== null) row.abnormalWork = abnormalWork
  })

  message.success(`已对 ${selectedRows.value.length} 行进行批量赋值`)
  batchDialogVisible.value = false
}

// 控制弹窗显示
const batchDialogVisible = ref(false)
//新增行
const createEditableRow = () => ({
  dateStr: dateStrAbnormal.value,
  productionLine: productionLineAbnormal.value,
  productionTime: '',
  productionTimeStart: '',
  productionTimeEnd: '',
  abnormalTime: '',
  abnormalTimeStart: '',
  abnormalTimeEnd: '',
  productionOrderCode: '',
  salesOrderCode: '',
  productNo: '',
  modelsOrColor: '',
  workOrderNum: '',
  units: '',
  abnormalRemark: '',
  abnormalCountermeasures: '',
  abnormalNum: 0,
  abnormalWork: 0,
  displayAbnormal: 0,
  isNew: true, // 标记为新增行
  isEditing: true // 添加编辑状态标识
})

// 删除行时清理监听
const removeBodyRow = (index: number) => {
  message.confirm('确定要第' + (index + 1) + '行删除吗？').then(() => {
    if (abnormalData.value[index].isRework === 1 || abnormalData.value[index].isTrial === 1) {
      // 返工，只需要在主页取消选中即可
      message.alert('该异常为返工/试产，请回到上一页面取消选中即可！')
      return false
    }
    DayApi.updateDisplayAbnormal([abnormalData.value[index].id])
    abnormalData.value.splice(index, 1)
  })
}

const batchesIdAbnormal = ref()

const dateStrAbnormal = ref()
const productionLineAbnormal = ref()
/** 打开弹窗 */
const openForm = async (
  row: any,
  title?: any,
  batchesId?: any,
  dateStr?: any,
  productionLine?: any
) => {
  console.log(batchesId)
  batchesIdAbnormal.value = batchesId
  dialogVisible.value = true
  dialogTitle.value = title
  dateStrAbnormal.value = dateStr
  productionLineAbnormal.value = productionLine
  let originalData: any[] = []
  // 处理父组件传来的原始数据
  if (row?.value?.length > 0) {
    originalData = row.value.map((item) => {
      // 如果 abnormalReportNum 为空或者 0，则赋值 hoursReportNum
      const abnormalReportNum =
        item.abnormalReportNum === null ||
        item.abnormalReportNum === undefined ||
        item.abnormalReportNum === 0
          ? item.hoursReportNum
          : item.abnormalReportNum
      return {
        ...item,
        isNew: false,
        isOldNew: false,
        isEditing: false,
        workType: 0,
        abnormalReportNum // 替换异常数量字段
      }
    })
  }

  // 请求服务端获取已有异常数据
  const res = await DayApi.getAbnormalByBatchesId(batchesId)
  let backendData: any[] = []

  if (res.length > 0) {
    backendData = res.map((item: any) => {
      const [productionTimeStart = '', productionTimeEnd = ''] = (item.productionTime || '').split(
        '-'
      )
      const [abnormalTimeStart = '', abnormalTimeEnd = ''] = (item.abnormalTime || '').split('-')
      return {
        ...item,
        productionTimeStart,
        productionTimeEnd,
        abnormalTimeStart,
        abnormalTimeEnd,
        isNew: true,
        isOldNew: false
      }
    })
  }

  // 合并原始数据和后端数据，保持顺序：原始数据 + 其下复制新增行
  let combinedData: any[] = [...originalData]
  console.log(backendData)
  for (const item of backendData) {
    // 查找是否有相同的工单、销售订单、品号等字段
    const matchIndex = combinedData.findIndex(
      (row) =>
        row.productionOrderCode === item.productionOrderCode &&
        row.salesOrderCode === item.salesOrderCode &&
        row.productNo === item.productNo
    )

    if (matchIndex > -1) {
      // 插入到匹配项下方，并设置 isNew = false 表示是复制添加
      item.isNew = false
      // 设置 isOldNew = true 表示是旧数据复制的，那么同样不可修改工单号等等
      item.isOldNew = true
      combinedData.splice(matchIndex + 1, 0, item)
    } else {
      // 没有找到匹配项，则直接 push
      combinedData.push(item)
    }
  }

  // 最终赋值给表格数据源
  abnormalData.value = combinedData.filter((item) => item.displayAbnormal === 0)
  // 清空已选中的行
  selectedRows.value = []
}
defineExpose({ open, openForm }) // 提供 open 方法，用于打开弹窗

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 提交表单 */
const submitForm = async () => {
  // 防止重复提交
  if (formLoading.value) {
    message.warning('数据提交中，请勿重复点击')
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    let isValid = true
    for (
      let i = 0;
      i < abnormalData.value.filter((item) => item.displayAbnormal !== 1).length;
      i++
    ) {
      const item = abnormalData.value.filter((item) => item.displayAbnormal !== 1)[i]
      if (item.isOldNew && item.id === undefined) {
        //本次复制新增的行，异常时间段必填
        if (!item.abnormalTimeStart || !item.abnormalTimeEnd) {
          message.alert(`第 ${i + 1} 行异常时间段未填写，请检查！`)
          isValid = false
          break
        }
      }

      // 新增校验逻辑：如果填写了问题点和临时对策，人数必须填写
      if (
        (item.abnormalRemark && item.abnormalRemark.trim() !== '') ||
        (item.abnormalCountermeasures && item.abnormalCountermeasures.trim() !== '')
      ) {
        if (item.abnormalNum === undefined || item.abnormalNum === null || item.abnormalNum <= 0) {
          message.alert(`第 ${i + 1} 行，人数必须大于0，请检查！`)
          isValid = false
          break
        }

        if (
          item.abnormalReportNum === undefined ||
          item.abnormalReportNum === null ||
          item.abnormalReportNum <= 0
        ) {
          message.alert(`第 ${i + 1} 行，异常数量必须大于0，请检查！`)
          isValid = false
          break
        }
        // 如果人数大于0，异常时间段必填
        if (!item.abnormalTimeStart || !item.abnormalTimeEnd) {
          message.alert(`第 ${i + 1} 行人数大于0，异常时间段未填写，请检查！`)
          isValid = false
          break
        }
      }
      if (item.abnormalNum != undefined && item.abnormalNum != null && item.abnormalNum > 0) {
        //异常人数大于0，异常时间段必填
        if (!item.abnormalTimeStart || !item.abnormalTimeEnd) {
          message.alert(`第 ${i + 1} 行异常时间段未填写，请检查！`)
          isValid = false
          break
        }
        //异常人数大于0，异常问题点和临时对策必填
        if (!item.abnormalRemark || !item.abnormalCountermeasures) {
          message.alert(`第 ${i + 1} 行问题点和临时对策未填写，请检查！`)
          isValid = false
          break
        }
      }
    }
    if (isValid) {
      //判断是否有在本页面新增的异常工时，如果有则存入数据库中
      const newData = abnormalData.value.filter(
        (item) => (item.isNew || item.isOldNew) && item.displayAbnormal === 0
      )
      // 过滤掉异常工时为空的数据
      const validNewData = newData.filter((item) => {
        // 检查异常工时相关字段是否为空
        return (
          (item.abnormalRemark && item.abnormalRemark.trim() !== '') ||
          (item.abnormalCountermeasures && item.abnormalCountermeasures.trim() !== '') ||
          (item.abnormalNum !== undefined && item.abnormalNum !== null && item.abnormalNum > 0) ||
          (item.abnormalTimeStart && item.abnormalTimeEnd) ||
          (item.abnormalReportNum !== undefined &&
            item.abnormalReportNum !== null &&
            item.abnormalReportNum > 0)
        )
      })
      if (newData.length > 0) {
        if (validNewData.length === 0) {
          message.alert('新增异常工时数据不合法，请重新提交！')
          return
        }
        validNewData.forEach((item) => {
          item.dateStr = dateStrAbnormal.value
          item.productionLine = productionLineAbnormal.value
          item.batchesId = batchesIdAbnormal.value
          item.workType = 1
          item.isAbnormal = 0
          item.productionTime = item.productionTimeStart + '-' + item.productionTimeEnd
          item.abnormalTime = item.abnormalTimeStart + '-' + item.abnormalTimeEnd
          item.abnormalReportNum = item.abnormalReportNum
        })
        const res = await DayApi.createAbnormalDay(
          validNewData.filter((item) => item.displayAbnormal === 0)
        )
        abnormalData.value.forEach((item) => {
          item.batchesId = res
        })
      } else {
        //搞一条空数据，把批次ID塞进去，让后端删除掉数据
        const emptyData = [
          {
            batchesId: batchesIdAbnormal.value,
            isAbnormal: 1
          }
        ]
        await DayApi.createAbnormalDay(emptyData)
        abnormalData.value.forEach((item) => {
          item.batchesId = batchesIdAbnormal.value
        })
      }
      //关闭弹窗
      dialogVisible.value = false
      emit('success', abnormalData.value)
      message.success('操作成功')
    }
  } finally {
    formLoading.value = false
  }
}

// 处理自动编号功能
const handleAutoNumber = (event: KeyboardEvent, row: any, property: string) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()

    const textarea = event.target as HTMLTextAreaElement
    const cursorPosition = textarea.selectionStart
    const currentValue = row[property] || ''

    // 分析当前内容，找到最后一个序号
    const lines = currentValue.split('\n')
    let lastNumber = 0

    // 查找最后一个有效的序号
    for (let i = lines.length - 1; i >= 0; i--) {
      const match = lines[i].match(/^(\d+)\.\s*/)
      if (match) {
        lastNumber = parseInt(match[1])
        break
      }
    }

    const nextNumber = lastNumber + 1
    const newLine = `\n${nextNumber}. `

    // 在光标位置插入新的编号行
    const beforeCursor = currentValue.substring(0, cursorPosition)
    const afterCursor = currentValue.substring(cursorPosition)

    const newValue = beforeCursor + newLine + afterCursor
    row[property] = newValue

    // 设置新的光标位置
    nextTick(() => {
      const newCursorPosition = cursorPosition + newLine.length
      textarea.setSelectionRange(newCursorPosition, newCursorPosition)
      textarea.focus()
    })
  }
}

// 初始化文本框内容（如果为空则添加第一个序号）
const initializeTextContent = (row: any, property: string) => {
  if (!row[property] || row[property].trim() === '') {
    row[property] = '1. '
  }
}

// 右键菜单相关
const currentRow = ref<any>(null)
const currentColumn = ref<any>(null)

// 右键点击事件
const rightClick = (row: any, column: any, event: MouseEvent) => {
  currentRow.value = row
  currentColumn.value = column

  const menu = document.getElementById('contextMenu')
  if (!menu) return

  event.preventDefault()

  menu.style.left = `${event.clientX + 10}px`
  menu.style.top = `${event.clientY - 10}px`
  menu.style.display = 'block'
}

// 表格左键点击事件（隐藏菜单）
const clickTableRow = () => {
  const menu = document.getElementById('contextMenu') as HTMLElement
  if (menu) {
    menu.style.display = 'none'
  }
}

// 批量赋值功能
const batchAssignValues = () => {
  // 检查是否有选中的行
  if (selectedRows.value.length === 0) {
    message.error('请先选择行')
    return
  }

  if (!currentRow.value) {
    message.error('请先右键选择一个单元格')
    return
  }
  message.confirm('确定要批量赋值选中的行吗？').then(() => {
    // 获取当前行的四个字段值
    const sourceValues = {
      abnormalRemark: currentRow.value.abnormalRemark,
      abnormalCountermeasures: currentRow.value.abnormalCountermeasures,
      abnormalNum: currentRow.value.abnormalNum,
      abnormalWork: currentRow.value.abnormalWork,
      abnormalTimeStart: currentRow.value.abnormalTimeStart,
      abnormalTimeEnd: currentRow.value.abnormalTimeEnd
    }

    // 批量赋值给选中的行
    selectedRows.value.forEach((row) => {
      row.abnormalRemark = sourceValues.abnormalRemark
      row.abnormalCountermeasures = sourceValues.abnormalCountermeasures
      row.abnormalNum = sourceValues.abnormalNum
      row.abnormalWork = sourceValues.abnormalWork
      row.abnormalTimeStart = sourceValues.abnormalTimeStart
      row.abnormalTimeEnd = sourceValues.abnormalTimeEnd
    })

    message.success(`已将选中的 ${selectedRows.value.length} 行批量赋值`)

    // 隐藏菜单
    const menu = document.getElementById('contextMenu') as HTMLElement
    if (menu) {
      menu.style.display = 'none'
    }
  })
}
//复制选中行
const batchCopyValues = () => {
  // 检查是否有选中的行
  if (selectedRows.value.length === 0) {
    message.error('请先选择行')
    return
  }

  message.confirm('确定要复制选中的行吗？').then(() => {
    // 创建一个临时数组来保存所有需要插入的新行及其位置
    const insertions: { index: number; row: any }[] = []

    selectedRows.value.forEach((row) => {
      const index = abnormalData.value.findIndex((item) => item === row)
      if (index > -1) {
        // 深拷贝当前行数据
        const newRow = JSON.parse(JSON.stringify(row))
        newRow.type = 0 //异常工时类型
        newRow.isOldNew = true
        newRow.id = undefined // 清除唯一标识，避免冲突（如果存在 id）

        // 记录插入位置
        insertions.push({ index: index + 1, row: newRow })
      }
    })

    // 按照原顺序插入新行（从后往前插入，避免索引错乱）
    insertions
      .sort((a, b) => b.index - a.index)
      .forEach(({ index, row }) => {
        abnormalData.value.splice(index, 0, row)
      })

    message.success(`已复制 ${selectedRows.value.length} 行，并插入到对应行下方`)
    // 隐藏菜单
    const menu = document.getElementById('contextMenu') as HTMLElement
    if (menu) {
      menu.style.display = 'none'
    }
  })
}

const batchDeleteValues = () => {
  // 检查是否有选中的行
  if (selectedRows.value.length === 0) {
    message.error('请先选择行')
    return
  }

  // 批量删除选中的行
  message.confirm('确定要删除选中的行吗？').then(() => {
    // 检查是否有返工/试产的异常数据
    const reworkOrTrialRows = selectedRows.value.filter(
      (row) => row.isRework === 1 || row.isTrial === 1
    )

    if (reworkOrTrialRows.length > 0) {
      message.alert('选中的行中包含返工/试产的异常数据，无法删除，请重新选择！')
      return
    }
    const deleteIds = selectedRows.value
      .filter((row) => row.id != null && row.id != undefined)
      .map((row) => row.id)
    if (deleteIds) {
      DayApi.updateDisplayAbnormal(deleteIds)
    }

    // 过滤掉选中的行（推荐做法）
    abnormalData.value = abnormalData.value.filter((row) => !selectedRows.value.includes(row))

    // 清空选中行
    selectedRows.value = []
    const menu = document.getElementById('contextMenu') as HTMLElement
    if (menu) {
      menu.style.display = 'none'
    }
  })
}

onUnmounted(() => {
  formLoading.value = false
})
</script>

<style lang="scss" scoped>
.mobile-batch-container {
  position: relative;
  z-index: 999;
}

.batch-assignment-sticky {
  position: sticky;
  top: 0;
  background: white;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.card-list-container {
  max-height: calc(100vh - 220px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.time-range-row {
  display: flex;
  align-items: center;

  .time-select {
    margin: 0 4px;
  }

  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}

.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .mobile-body-form :deep(.el-form-item__label) {
    text-align: right !important;
    width: 80px !important;
    display: inline-block;
  }

  .mobile-body-info {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-top: 10px;

    .info-row {
      display: flex;
      margin-bottom: 8px;
      font-size: 13px;
      margin-left: 30px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-label {
      width: 80px;
      font-weight: 500;
      color: #606266;
      text-align: right;
      font-size: 13px;
      margin-left: -25px;
    }

    .info-value {
      flex: 1;
      color: #000000;
      word-break: break-all;
      margin-left: 12px;
      font-size: 14px;
    }
  }
}

:deep(.no-spin-input) {
  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
    /* Firefox */
  }
}

/* 合并单元格的文本框样式 */
:deep(.el-table__cell) {
  .el-textarea {
    width: 100%;
    height: 100%;

    .el-textarea__inner {
      width: 100%;
      height: 100%;
      min-height: 120px;
      resize: vertical;
      border: none;
      box-shadow: none;
      padding: 8px;
      line-height: 1.5;
      font-size: 14px;
    }
  }
}

/* 确保合并的单元格高度自适应 */
:deep(.el-table__row) {
  .el-table__cell {
    vertical-align: top;

    &:has(.el-textarea) {
      padding: 0;
      height: auto;
    }
  }
}

/* 表格行高度自适应 */
:deep(.el-table__body-wrapper) {
  .el-table__row {
    height: auto;

    .el-table__cell {
      height: auto;
      min-height: 120px;
    }
  }
}

/* 右键菜单样式 */
.context-menu {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0;
  min-width: 120px;

  .menu-list {
    list-style: none;
    margin: 0;
    padding: 0;

    .menu-item {
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      color: #606266;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      &:first-child {
        border-radius: 4px 4px 0 0;
      }

      &:last-child {
        border-radius: 0 0 4px 4px;
      }

      &:only-child {
        border-radius: 4px;
      }
    }
  }
}
</style>
