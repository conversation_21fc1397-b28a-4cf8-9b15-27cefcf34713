import request from '@/config/axios'

export const RouteApi = {
  // 根据id 获取工艺路线
  getRouteById: async (id: number) => {
    return await request.get({ url: '/report/technology-route/get/' + id })
  },
  // 保存工艺路线
  saveRoute: async (data: any) => {
    return await request.post({ url: '/report/technology-route/save', data })
  },
  // 获取工艺路线平铺分页
  getRoutePage: async (data: any) => {
    return await request.post({ url: '/report/technology-route/page', data })
  },
  // 获取工艺路线与物料关联分页
  getRouteLinkPage: async (data: any) => {
    return await request.post({ url: '/report/technology-route/page-link', data })
  },
  // 获取所有工艺路线的最新版本
  getRouteAllVersionList: async () => {
    return await request.get({ url: '/report/technology-route/get-route-all-version' })
  },
  // 保存或更新工艺路线与物料关联
  saveLink: async (data: any[]) => {
    return await request.post({ url: '/report/technology-route/save-link', data })
  },
  //** 获取条件设定下，匹配到的结果总数 */
  selectLinkCount: async (data: any) => {
    return await request.post({ url: '/report/technology-route/select-link-count', data })
  },
  /** 导出符合条件物料列表 */
  exportLink: async (data: any) => {
    return await request.downloadPost({ url: '/report/technology-route/export-link', data })
  },
  /** 保存或更新工艺路线与物料关联 */
  saveLinkConditions: async (data: any) => {
    return await request.post({ url: '/report/technology-route/save-link-conditions', data })
  },
  /** 导出工艺路线模板 */
  exportTemplate: async () => {
    return await request.downloadPost({ url: '/report/technology-route/export-template' })
  },
  exportLinkAll: async (data: any) => {
    return await request.downloadPost({ url: '/report/technology-route/export-link-all', data })
  }
}
