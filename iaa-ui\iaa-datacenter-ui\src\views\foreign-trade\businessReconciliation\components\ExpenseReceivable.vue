<template>
  <div>
      <div class="mt-20px">
      <vxe-table
        :row-config="{ height: 25, keyField: 'id' }"
        ref="tableRef"
        :data="list"
        :header-cell-style="{ padding: 0 }"
        border
        stripe
        align="center"
        show-overflow="title"
        :column-config="{ resizable: true }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        :loading="loading"
        :checkbox-config="{ reserve: true, highlight: true, range: true }"
        :filter-config="{}"
        show-footer
        keep-source
        :footer-data="footerData"
        :footer-cell-style="{
          padding: 0,
          background: '#dcefdc',
          border: '1px solid #ebeef5'
        }"
        :mouse-config="{ selected: true }"
        tabindex="0"
        size="mini"
      >
        <!-- <vxe-column field="dateStr" width="120" title="费用日期" /> -->
        <vxe-column field="project" title="项目" width="120" />
        <vxe-column field="cost" title="费用" width="120" />
        <vxe-column field="currency" title="费用币种" width="120" />
        <vxe-column field="collectionAmount" title="收款金额" width="120" />
                <vxe-column field="expenseDetails" title="收款明细" min-width="200">
          <template #default="{ row }">
            <span v-for="(detail, index) in row.expenseDetails" :key="index">
              {{ detail.currency }}：{{ detail.amount }}，汇率：{{ detail.rate || '-' }}；
            </span>
          </template>
        </vxe-column>
        <vxe-column field="receivableAmount" title="应收金额（本币）" min-width="120" />
      </vxe-table>
    </div>
    <!-- 费用应收返回数组，无分页；显示总条数 -->
    <div class="mt-8px text-right text-6 text-gray-500">共 {{ total }} 条</div>
  </div>
</template>

<script lang="ts" setup>
import { BusinessReconciliationApi } from '@/api/foreign-trade/businessReconciliation/index'
import type { VxeTablePropTypes } from 'vxe-table'

const props = defineProps<{ orderCodes?: string[] }>()

const loading = ref(false)
const list = ref<any[]>([])
const total = ref(0)
const tableRef = ref()
const totalCost=ref(0)
const totalCollectionAmount=ref(0)
const totalReceivableAmount=ref(0)
//合计行
const footerData = ref<VxeTablePropTypes.FooterData>([
  { dateStr: '合计总和', cost: totalCost, collectionAmount: totalCollectionAmount, receivableAmount: totalReceivableAmount },
])
/** 查询列表（费用应收返回数组，无分页） */
const getList = async () => {
  loading.value = true
  try {
    const res = await BusinessReconciliationApi.getExpenseReceivablePage({ orderCodes: props.orderCodes || [] })
    const arr = res || []
    list.value = (arr || []).map((it:any) => ({
      dateStr: it.dateStr,
      project: it.label,
      cost: it.cost,
      collectionAmount: it.collectionAmount,
      receivableAmount: it.receivableAmount,
      currency: it.currency,
      currencyAmount: it.currencyAmount,
      rate: it.rate,
      expenseDetails: it.expenseDetails

    }))
    total.value = list.value.length

    totalCost.value = list.value
      .reduce((total, item) => total + item.cost, 0)
      .toFixed(3)
    totalCollectionAmount.value = list.value
      .reduce((total, item) => total + item.collectionAmount, 0)
      .toFixed(3)
    totalReceivableAmount.value = list.value
      .reduce((total, item) => total + item.receivableAmount, 0)
      .toFixed(3)
  } finally {
    loading.value = false
  }
}

watch(
  () => props.orderCodes,
  () => {
    getList()
  },
  { immediate: true }
)
</script>
