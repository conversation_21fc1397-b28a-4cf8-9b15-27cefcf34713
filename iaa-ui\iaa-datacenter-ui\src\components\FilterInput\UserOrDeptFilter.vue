<template>
  <div v-if="currOption" class="my-filter-input">
    <div>
      <el-radio-group
        v-model="currOption.data.type"
        @change="
          () => {
            currOption!.data.userList = []
            currOption!.data.deptList = []
            changeOptionEvent()
          }
        "
      >
        <el-radio label="人员" value="user" />
        <el-radio label="部门" value="dept" />
      </el-radio-group>
    </div>
    <div v-if="currOption.data.type === 'user'">
      <div class="search">
        <el-input size="small" v-model="nickname" @input="filterUserEvent" />
      </div>
      <div class="p-5px">
        <el-checkbox v-model="isCheckedAll" @change="checkedAllEvent">全选</el-checkbox>
      </div>
      <div class="h-150px overflow-auto p-l-5px p-r-5px">
        <el-checkbox-group v-model="currOption.data.userList" @change="changeOptionEvent">
          <el-checkbox
            v-for="item in cloneUserList"
            :key="item.id"
            :value="item.id"
            :label="item.nickname"
          />
        </el-checkbox-group>
      </div>
    </div>
    <div v-else>
      <div class="head-container">
        <el-input v-model="deptName" class="mb-20px" clearable placeholder="请输入部门名称">
          <template #prefix>
            <Icon icon="ep:search" />
          </template>
        </el-input>
      </div>
      <div class="h-200px overflow-auto">
        <el-tree
          ref="treeRef"
          :data="deptList"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          :props="defaultProps"
          :default-expanded-keys="[100]"
          :default-checked-keys="currOption.data?.deptList || []"
          highlight-current
          node-key="id"
          show-checkbox
          check-on-click-node
          @node-click="changeDept"
          @check-change="changeDept"
        />
      </div>
    </div>
    <div class="my-fc-footer">
      <el-button type="primary" plain @click="confirmEvent" size="small">筛选</el-button>
      <el-button @click="resetEvent" size="small">重置</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import type { VxeTableDefines } from 'vxe-table'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { getSimpleDeptList, DeptVO } from '@/api/system/dept'
import { cloneDeep } from 'lodash-es'
import { defaultProps, handleTree } from '@/utils/tree'

const nickname = ref('')
const deptName = ref('')
const userList = ref<UserVO[]>([])
const cloneUserList = ref<UserVO[]>([])
const deptList = ref<DeptVO[]>([])
const treeRef = ref()
const props = defineProps({
  renderParams: propTypes.any.def({})
})

const isCheckedAll = ref(false)

const currOption = ref<VxeTableDefines.FilterOption>()

const currField = computed(() => {
  const { column } = props.renderParams || {}
  return column ? column.field : ''
})

const onListUser = async () => {
  const res = await getSimpleUserList()
  userList.value = res?.filter((item: any) => item.id != 1)
  cloneUserList.value = cloneDeep(userList.value)
}

const onListDept = async () => {
  const res = await getSimpleDeptList()
  deptList.value = []
  deptList.value.push(...handleTree(res))
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

const load = () => {
  const { renderParams } = props
  if (renderParams) {
    const { column } = renderParams
    const option = column.filters[0]
    currOption.value = option
    currOption.value!.data.type = 'dept'
    onListUser()
    onListDept()
  }
}

const changeDept = () => {
  currOption.value!.data.deptList = treeRef.value?.getCheckedKeys()
  changeOptionEvent()
}

const changeOptionEvent = () => {
  const { renderParams } = props
  const option = currOption.value
  if (renderParams && option) {
    const { $table } = renderParams
    const checked =
      currOption.value?.data.type == 'user' ? !!option.data.userList : !!option.data.deptList
    $table.updateFilterOptionStatus(option, checked)
  }
}

const checkedAllEvent = () => {
  const option = currOption.value
  if (option) {
    if (isCheckedAll.value) {
      option.data.userList = userList.value.map((item) => item.id)
    } else {
      option.data.userList = []
    }
    changeOptionEvent()
  }
}

const filterUserEvent = () => {
  cloneUserList.value = userList.value.filter((item) => {
    return item.nickname.includes(nickname.value)
  })
}

const confirmEvent = ($event) => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.confirmFilterEvent($event)
  }
}

const resetEvent = ($event) => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    currOption.value!.data.type == 'user'
    currOption.value!.data.userList = []
    currOption.value!.data.deptList = []
    $table.confirmFilterEvent($event)
  }
}

watch(currField, () => {
  load()
})

load()
</script>

<style scoped>
.my-filter-input {
  padding: 10px;
}

.my-fc-footer {
  text-align: center;
  margin-top: 8px;
}

:deep(.el-checkbox) {
  display: block;
}
</style>
