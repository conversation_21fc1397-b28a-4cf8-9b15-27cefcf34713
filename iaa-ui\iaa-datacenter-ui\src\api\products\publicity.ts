import request from '@/config/axios'
export const PublicityApi = {
  //获取列表
  getList: async (params) => {
    return await request.post({ url: `/report/products/page`,data:params })
  },
  //获取下载模版
    exportTemplate: () => {
    return request.download({ url: `/report/products/export-template` })
  },

  //获取最新的变更记录日志
  getLastChangeLog: async () => {
    return await request.get({ url: `/report/products/last-change-log` })
  },

  //批量删除
  batchDelete: async (ids) => {
    return await request.post({ url: `/report/products/batch-delete`,data:ids })
  },
  // 导出产品数据 Excel
  exportDay: async (params) => {
    return await request.download({ url: `/report/products/export-excel`, params })
  },

  //获取变更记录日志
  getHistory: async (params) => {
    return await request.post({ url: `/report/products/history`,data:params })
  },
  update: async (data) => {
    return await request.post({ url: `/report/products/update`, data })
  }

}