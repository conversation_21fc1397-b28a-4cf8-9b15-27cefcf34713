import request from '@/config/axios'

/** 获取后台可见小满报表分页数据 */
export const pageUser = async (params: any) => {
  return await request.get({ url: '/butt-joint/xiaoman/permissions/page-user', params })
}
/** 获取小满所有用户 */
export const listXiaomanUser = async () => {
  return await request.get({ url: '/butt-joint/xiaoman/permissions/list-xiaoman-user' })
}
/** 获取小满用户可见报表 */
export const getXiaomanViewAuthorityDO = async (backendUserId: number) => {
  return await request.get({ url: '/butt-joint/xiaoman/permissions/get-view/' + backendUserId })
}
/** 设定小满用户可见报表 */
export const setXiaomanViewAuthority = async (data: any) => {
  return await request.post({ url: '/butt-joint/xiaoman/permissions/set-view', data })
}
/** 获取当前用户可见小满用户 */
export const getXiaomanViewUserDO = async()=>{
  return await request.get({url:'/butt-joint/xiaoman/permissions/get-view-user'})
}
