import request from '@/config/axios'

//认款、录款接口
export const ClaimApi = {
  // 查询认款、录款信息分页
  getClaimPage: async (data: any) => {
    return await request.post({ url: `/collection/claim/page`, data })
  },

  // 新增认款、录款信息
  createClaim: async (data: any) => {
    return await request.post({ url: `/collection/claim/create`, data })
  },

    // 修改认款、录款信息
  updateClaim: async (data: any) => {
    return await request.post({ url: `/collection/claim/update`, data })
  },


  // 删除认款、录款信息
  deleteClaim: async (ids: any) => {
    return await request.post({ url: `/collection/claim/deletes`,data:ids })
  },

    // 删除收款信息
  deleteClaimInformation: async (ids: any) => {
    return await request.post({ url: `/collection/information/deletes`,data:ids })
  },

  //获取ERP币种列表
  getCurrency: async () => {
    return await request.get({ url: `/collection/information/getCurrency`})
  },
    // 获取当前业务员所属ERP客户信息
  getCustomer: async (params: any) => {
    return await request.get({ url: `/collection/reconciliation/getCustomers?customersName=${params}`})
  },
      // 根据客户编码获取ERP客户订单信息
  getOrders: async (params: any) => {
    return await request.get({ url: `/collection/claim/getOrders`,params: {
      code: params.code,
      claimId: params.claimId,
      DocNo: params.DocNo
    } })
  },
  getClaimDetail: async (id: number) => {
    return await request.get({ url: `/collection/claim/get?id=${id}`})
  },
  // 预填：根据收款信息ID + 状态筛选获取上次填写数据（后端已实现，按需调整URL）
  getClaimPrefill: async (data: { ids: number[]; status: number[]; isMe: boolean }) => {
    return await request.post({ url: `/collection/claim/prefill`, data })
  }
}
