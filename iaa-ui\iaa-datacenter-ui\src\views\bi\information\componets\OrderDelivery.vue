<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <el-form inline class="custom-form" size="small">
      <el-form-item label="日期">
        <el-radio-group v-model="queryParams.dateType" @change="onList">
          <el-radio-button label="年" value="year" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
        <el-date-picker
          class="!w-100px"
          :type="queryParams.dateType"
          v-model="queryParams.sTime"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="onList"
        />
      </el-form-item>
    </el-form>
    <div
      id="order-delivery-chart"
      class="h-300px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
    ></div>
    <div class="h-[calc(100%-300px-60px-54px)]">
      <vxe-table
        height="100%"
        align="center"
        border
        :loading="loading"
        :data="dataList"
        show-overflow
        stripe
      >
        <vxe-column title="销售订单号" field="salesOrderNo" />
        <vxe-column title="料号" field="itemCode" />
        <vxe-column title="所属月份" field="deliveryMonth" />
        <vxe-column title="下单日期" field="orderDate" />
        <vxe-column title="预计交期" field="deliveryDate" />
        <vxe-column title="销售数量" field="orderQty" />
        <vxe-column title="累计完成数量" field="totalCompletionQty" />
        <vxe-column title="最后报工日期" field="lastCompletionDate" />
        <vxe-column title="订单状态" field="orderStatus" />
        <vxe-column title="交付率" field="deliveryRate" />
      </vxe-table>
    </div>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="onList"
      size="small"
    />
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import { ErpApi } from '@/api/butt-joint/erp'
import * as echarts from 'echarts'
import { markRaw } from 'vue'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 50,
  dateType: 'year' as any,
  sTime: moment().format('YYYY-MM-DD')
})
const dataList = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const dataMap = ref<any>({})

// 图表实例
const chartInstance = ref<any>(null)

const onList = async () => {
  loading.value = true
  try {
    const res = await ErpApi.getOrderDeliveryList(queryParams)
    dataList.value = res.list
    total.value = res.total
    // 同时更新图表数据
    await getDataMap()
  } finally {
    loading.value = false
  }
}
const getDataMap = async () => {
  dataMap.value = await ErpApi.getOrderDeliveryRate(queryParams)
  // 数据获取完成后渲染图表
  await nextTick()
  renderChart()
}

// 渲染图表
const renderChart = () => {
  const container = document.getElementById('order-delivery-chart')
  if (!container) {
    console.warn('Chart container not found')
    return
  }

  // 销毁已存在的图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  // 处理数据
  const months = Object.keys(dataMap.value).sort()
  const values = months.map(month => dataMap.value[month] || 0)

  // 如果没有数据，显示空状态
  if (months.length === 0) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">订单交付率</div>
          <div class="text-sm mt-2">暂无数据</div>
        </div>
      </div>
    `
    return
  }

  // 创建ECharts实例
  chartInstance.value = markRaw(echarts.init(container))

  const option = {
    title: {
      text: '订单交付率统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const param = params[0]
        return `${param.axisValue}<br/>${param.marker}交付率: ${param.value}%`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months.map(month => {
        // 格式化月份显示
        if (queryParams.dateType === 'year') {
          return moment(month).format('MM月')
        } else {
          return moment(month).format('DD日')
        }
      }),
      axisLabel: {
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '交付率(%)',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLabel: {
        fontSize: 12,
        color: '#666',
        formatter: '{value}%'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '交付率',
        type: 'bar',
        data: values,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4facfe' },
            { offset: 1, color: '#00f2fe' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ])
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          fontSize: 11,
          color: '#666'
        }
      }
    ]
  }

  chartInstance.value.setOption(option)
}

// 窗口大小变化处理
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 清理图表实例
const disposeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
}

onMounted(() => {
  onList()
  getDataMap()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeChart()
})
</script>

<style scoped>
/* 图表容器样式 */
#order-delivery-chart {
  position: relative;
  overflow: hidden;
}

/* 空状态样式 */
#order-delivery-chart .flex {
  height: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  #order-delivery-chart {
    height: 250px !important;
  }
}

@media (max-width: 576px) {
  #order-delivery-chart {
    height: 200px !important;
  }
}
</style>
