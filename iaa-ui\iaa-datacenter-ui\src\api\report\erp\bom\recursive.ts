import request from '@/config/axios'

// 获得当前报表列
export const getForwardTableColumn = async () => {
  return await request.get({ url: '/report/bom/recursive/get-forward-table-column' })
}

export const getSoTableColumn = async () => {
  return await request.get({ url: '/report/bom/recursive/get-so-table-column' })
}

export const getReverseTableColumn = async () => {
  return await request.get({ url: '/report/bom/recursive/get-reverse-table-column' })
}

export const getItemTableColumn = async () => {
  return await request.get({url:'/report/bom/recursive/get-item-table-column'})
}

export const getUnits = async () => {
  return await request.get({url:'/report/bom/recursive/list-units'})
}

export const getOperators = async (type:number)=>{
  return await request.get({url:'/report/bom/recursive/list-operators/'+type})
}

export const getCustomer = async () => {
  return await request.get({url:'/report/bom/recursive/list-customer'})
}


export const getForwardRecursivePage = async (params: any) => {
  return await request.get({ url: '/report/bom/recursive/page-forward', params })
}

export const getReverseRecursivePage = async (params: any) => {
  return await request.get({ url: '/report/bom/recursive/page-reverse', params })
}

export const getSoRecursivePage = async (params: any) => {
  return await request.get({ url: '/report/bom/recursive/page-so', params })
}


export const getItemRecursivePage = async (params: any) => {
  return await request.get({ url: '/report/bom/recursive/page-material', params })
}