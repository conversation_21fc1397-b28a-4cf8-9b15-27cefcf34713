<template>
  <ContentWrap>
    <el-tabs v-model="currentTab">
      <el-tab-pane label="未同步到合思项目列表" name="project" />
    </el-tabs>

    <XTable
      :data="dataList"
      :columns="columns"
      :page="queryParams"
      :total="total"
      @refresh="getTableData"
      @search="onSerch"
      @pagination="(page) => onSerch(page, true)"
      v-loading="loading"
    >
      <template #operation="{ row }">
        <el-button
          type="primary"
          link
          size="small"
          v-if="currentTab === 'project'"
          v-hasPermi="['ekuaibao:custom:list']"
          @click="onSendData('ID01AdKeqenyyz:项目', row.code, row.name)"
        >
          同步到合思
        </el-button>
      </template>
    </XTable>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as ErpApi from '@/api/butt-joint/ekuaibao/erp'
import * as CustomApi from '@/api/butt-joint/ekuaibao/custom'

const currentTab = ref('project')
const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})

const message = useMessage()

const columns = ref<any[]>([])
const dataList = ref<any[]>([])
const total = ref(0)
const loading = ref(false)

/** 查询表格数据 */
const onSerch = (row: any, hasPage?: boolean) => {
  for (let key in row) {
    if (row[key]) {
      queryParams.value[key] = row[key]
    } else if (queryParams.value.hasOwnProperty(key)) {
      delete queryParams.value[key]
    }
  }
  if (!hasPage) {
    queryParams.value.pageNo = 1
  }
  getTableData()
}

const getTableColumn = async () => {
  switch (currentTab.value) {
    case 'project':
      columns.value = await ErpApi.getProrjectTableColumn()
      break
  }
}

const getTableData = async () => {
  switch (currentTab.value) {
    case 'project':
      const res = await ErpApi.getErpProjectPage(queryParams.value)
      dataList.value = res.list
      total.value = res.total
      break
  }
}

const onSendData = async (dimensionId: string, code: string, name: string) => {
  await message.confirm('确定将' + name + '同步到合思' + dimensionId + '档案嘛？')
  await CustomApi.sendData(dimensionId, code, name)
  message.success('同步成功')
  getTableData()
}

onMounted(() => {
  getTableColumn(), getTableData()
})
</script>
