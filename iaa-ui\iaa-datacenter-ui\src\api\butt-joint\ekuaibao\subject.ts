import request from '@/config/axios'

/** 获取合思科目列 */
export const getTableColumn = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/subject/get-columns' })
}

/** 获取U9所有的科目信息 */
export const getErpAllSubject = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/subject/get-erp-all-subject' })
}

/** 获取合思科目分页数据 */
export const page = async (params: any) => {
  return await request.get({ url: '/butt-joint/ekuaibao/subject/page', params })
}

/** 更新科目 */
export const save = async (data:any)=>{
  return await request.post({url:'/butt-joint/ekuaibao/subject/save',data})
}