<template>
  <div class="dialogContainer" v-show="dialogVisible">
    <Dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      :width="'100%'"
      :height="'100%'"
      :draggable="true"
      :overflow="true"
      :fullscreen="true"
      :before-close="handleClose"
    >
      <el-form
        ref="formRef"
        :model="headerData"
        :rules="formRules"
        label-width="80px"
        v-loading="formLoading"
      >
        <!-- 表头信息 -->
        <el-card class="mb-4" shadow="never">
          <el-row>
            <el-col :span="4" :xs="12">
              <el-form-item label="产线" :label-width="mobile ? '' : '70px'" prop="productionLine">
                <el-select v-model="headerData.productionLine" placeholder="请选择产线">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_LINE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="班组长" :label-width="mobile ? '' : '58px'" prop="teamLeader">
                <el-input v-model="headerData.teamLeader" placeholder="请输入班组长" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="日期" :label-width="mobile ? '' : '72px'" prop="dateStr">
                <el-date-picker
                  v-model="headerData.dateStr"
                  type="date"
                  value-format="x"
                  placeholder="选择日期"
                  style="width: 100%"
                  :prefix-icon="customPrefix"
                  class="custom-date-picker"
                  :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item
                label="应出勤"
                :label-width="mobile ? '' : '84px'"
                prop="requiredAttendanceNum"
              >
                <el-input
                  v-model="headerData.requiredAttendanceNum"
                  style="width: 100%"
                  type="number"
                  min="0"
                  class="no-spin-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item
                label="实际出勤"
                :label-width="mobile ? '' : '84px'"
                prop="actualAttendanceNum"
              >
                <el-input
                  v-model="headerData.actualAttendanceNum"
                  style="width: 100%"
                  type="number"
                  min="0"
                  class="no-spin-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item
                label="组装人数"
                :label-width="mobile ? '' : '72px'"
                prop="assembledNum"
              >
                <el-input
                  v-model="headerData.assembledNum"
                  style="width: 100%"
                  type="number"
                  min="0"
                  @change="syncBodyNumber"
                  class="no-spin-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4" :xs="12">
              <el-form-item label="工序" :label-width="mobile ? '' : '70px'" prop="type">
                <el-select v-model="headerData.type" placeholder="工序" style="width: 100%">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item
                label="标准工时"
                :label-width="mobile ? '' : '60px'"
                prop="standard_work"
              >
                <el-input
                  v-model="headerData.standardWork"
                  type="number"
                  :min="0"
                  style="width: 100%"
                  @change="syncBodyNumber"
                  class="no-spin-input"
                >
                  <template #append>分</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="组装工时" :label-width="mobile ? '' : '72px'">
                <el-input
                  v-model="headerData.assembledTotal"
                  type="number"
                  placeholder="系统自动计算"
                  :min="0"
                  style="width: 100%"
                  :readonly="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" :xs="12">
              <el-form-item label="出勤工时" :label-width="mobile ? '' : '84px'">
                <el-input
                  v-model="headerData.numberWork"
                  type="number"
                  placeholder="系统自动计算"
                  :min="0"
                  style="width: 100%"
                  :readonly="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2" :xs="12">
              <el-button
                @click="
                  openFormAttendance(
                    headerData.productionLine,
                    headerData.batchesId,
                    '出勤工时录入'
                  )
                "
                class="w-full"
                plain
                :style="mobile ? { 'margin-top': '5px' } : null"
              >
                出勤工时录入
              </el-button>
            </el-col>
            <el-col :span="2" :xs="12">
              <el-button
                @click="openFormPC('异常工时录入')"
                class="w-full"
                plain
                :style="mobile ? { 'margin-top': '5px' } : null"
              >
                异常工时录入
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 表身信息 -->
        <el-card shadow="never" class="custom-card">
          <template #header v-if="!mobile">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="display: flex; gap: 8px">
                <el-button type="primary" @click="addBodyRow" size="small" plain>
                  <Icon icon="ep:plus" style="margin-right: 4px" /> 新增行
                </el-button>
                <el-button type="success" @click="openQuickEntry" size="small" plain>
                  <Icon icon="ep:lightning" style="margin-right: 4px" /> 快速录入
                </el-button>
                <el-input
                  v-model="filters.productionOrderCode"
                  size="small"
                  placeholder="工单号筛选"
                />
                <el-input
                  v-model="filters.salesOrderCode"
                  size="small"
                  placeholder="销售订单号筛选"
                />
                <el-input v-model="filters.productNo" size="small" placeholder="品号筛选" />
                <el-button type="primary" @click="refreshQuery" size="small"> 重置 </el-button>
              </div>
            </div>
          </template>
          <div v-if="mobile">
            <el-row>
              <el-col :span="12">
                <el-input
                  v-model="filters.productionOrderCode"
                  size="small"
                  placeholder="工单号筛选"
                />
              </el-col>
              <el-col :span="12">
                <el-input
                  v-model="filters.salesOrderCode"
                  size="small"
                  placeholder="销售订单号筛选"
                />
              </el-col>
            </el-row>
            <div
              v-for="(row, index) in filteredBodyData"
              :key="row.id || `row-${index}`"
              class="card-item"
            >
              <div class="card-title" :v-loading="isLoading"
                >第 {{ index + 1 }} 条 / 共 {{ filteredBodyData.length }} 条
                <div class="card-title-actions">
                  <el-button style="padding: 0; color: #fff" link @click="addBodyRow">
                    <icon icon="ep:plus" />
                  </el-button>
                  <div v-if="formType === 'update'" style="display: flex; gap: 4px">
                    <el-button
                      v-if="!row.isEditing"
                      style="padding: 0; color: #fff"
                      link
                      @click="enterEditMode(index)"
                    >
                      <icon icon="ep:edit" />
                    </el-button>
                    <!-- <el-button v-else style="padding: 0; color: #fff" @click="saveEditMode(index)" link>
                      <icon icon="ep:check" />
                    </el-button> -->
                  </div>
                  <el-button style="padding: 0; color: #fff" link @click="removeBodyRow(index)">
                    <icon icon="ep:delete" />
                  </el-button>
                </div>
              </div>
              <el-form class="mobile-body-form" v-if="row.isEditing || formType === 'create'">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="时间段">
                      <div class="time-range-row">
                        <el-time-select
                          v-model="row.productionTimeStart"
                          placeholder="起始时间"
                          :start="'08:30'"
                          :end="'23:30'"
                          :step="'00:15'"
                          placement="top-start"
                        />
                        <span class="time-range-separator">-</span>
                        <el-time-select
                          v-model="row.productionTimeEnd"
                          placeholder="结束时间"
                          :start="'08:30'"
                          :end="'23:30'"
                          :step="'00:15'"
                          :min-time="row.productionTimeStart"
                          placement="top-start"
                        />
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="工单号">
                      <el-autocomplete
                        v-model="row.productionOrderCode"
                        :fetch-suggestions="
                          (queryString, cb) => queryWorkSalesOptions(queryString, cb, 0, index)
                        "
                        placeholder="输入工单号"
                        style="width: 100%"
                        @select="(item) => handleProductionOrderSelect(item, index)"
                        :trigger-on-focus="false"
                        :debounce="300"
                        popper-class="production-order-autocomplete"
                        placement="top-start"
                      >
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text"
                              >{{ item.productionOrderCode }}-{{ item.productNo }}</div
                            >
                          </div>
                        </template>
                      </el-autocomplete>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="订单号">
                      <el-autocomplete
                        v-model="row.salesOrderCode"
                        :fetch-suggestions="
                          (queryString, cb) => queryWorkSalesOptions(queryString, cb, 1, index)
                        "
                        placeholder="输入销售订单号"
                        style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)"
                        :trigger-on-focus="false"
                        :debounce="300"
                        popper-class="production-order-autocomplete"
                        placement="top-start"
                      >
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text"
                              >{{ item.salesOrderCode }}-{{ item.productNo }}</div
                            >
                          </div>
                        </template>
                      </el-autocomplete>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品号">
                      <el-autocomplete
                        v-model="row.productNo"
                        :fetch-suggestions="
                          (queryString, cb) =>
                            queryProductNo(
                              queryString,
                              cb,
                              row.productionOrderCode,
                              row.salesOrderCode
                            )
                        "
                        placeholder="输入品号查询"
                        style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)"
                        :trigger-on-focus="false"
                        :debounce="300"
                        popper-class="production-order-autocomplete"
                        placement="top-start"
                      >
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text"
                              >{{ item.productNo }}-{{ item.workOrderNum }}-{{ item.lineNo }}</div
                            >
                          </div>
                        </template>
                      </el-autocomplete>
                      <el-input v-model="row.productNo" placeholder="品号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品名">
                      <el-input v-model="row.modelsOrColor" placeholder="机型/颜色" readonly />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="规格" v-if="false">
                      <el-input v-model="row.specs" placeholder="规格" readonly />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="数量">
                      <el-input v-model="row.workOrderNum" placeholder="数量" readonly />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="人数">
                      <el-input
                        v-model="row.number"
                        placeholder="人数"
                        type="number"
                        @input="(val) => (row.number = Number(val))"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生产数">
                      <el-input
                        v-model="row.hoursReportNum"
                        placeholder="生产数量"
                        type="number"
                        :max="row.workOrderNum"
                        @input="(val) => validateHoursReportNum(val, row)"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="累计数">
                      <el-input
                        v-model="row.totalReportNum"
                        placeholder="累计数"
                        :readonly="true"
                        type="number"
                        :min="0"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="工序">
                      <template #default>
                        <el-select
                          v-model="row.type"
                          placeholder="请选择工序"
                          style="width: 100%"
                          @change="(value) => handleTypeChange(value, row)"
                        >
                          <el-option
                            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </template>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="标准工时">
                      <el-input
                        v-model="row.standardWork"
                        type="number"
                        min="0"
                        @input="(val) => (row.standardWork = Number(val))"
                      >
                        <template #append>分</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注">
                      <el-input v-model="row.remark" placeholder="请输入备注" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="row.pmcRemark">
                    <el-tooltip effect="dark" :content="row.pmcRemark" placement="top">
                      <el-form-item label="PMC备注">{{ row.pmcRemark }}</el-form-item>
                    </el-tooltip>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="返工">
                      <el-checkbox
                        :model-value="row.isRework === 1"
                        @update:model-value="(val) => handleReworkChange(val, row)"
                        align="center"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="试产">
                      <el-checkbox
                        :model-value="row.isTrial === 1"
                        @update:model-value="(val) => handleTryChange(val, row)"
                        align="center"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :span="12" v-if="row.abnormalWork">
                    <el-form-item label="异常工时">
                      <el-input v-model="row.abnormalWork" :readonly="true" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="24">
                    <el-button
                      type="success"
                      plain
                      @click="openQuickEntry"
                      size="small"
                      class="w-full"
                    >
                      快速录入
                    </el-button>
                  </el-col>
                </el-row>
              </el-form>
              <div v-else class="mobile-body-info">
                <!-- 只读模式下显示文本信息 -->
                <div class="info-row">
                  <span class="info-label">时间段:</span>
                  <span class="info-value"
                    >{{ row.productionTimeStart }} - {{ row.productionTimeEnd }}</span
                  >
                </div>
                <div class="info-row">
                  <span class="info-label">工单号:</span>
                  <span class="info-value">{{ row.productionOrderCode }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">订单号:</span>
                  <span class="info-value">{{ row.salesOrderCode }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">品号:</span>
                  <span class="info-value">{{ row.productNo }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">品名:</span>
                  <span class="info-value">{{ row.modelsOrColor }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">数量:</span>
                  <span class="info-value">{{ row.workOrderNum }}</span>
                  <span class="info-label">人数:</span>
                  <span class="info-value">{{ row.number }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">生产数:</span>
                  <span class="info-value">{{ row.hoursReportNum }}</span>
                  <span class="info-label">累计数:</span>
                  <span class="info-value">{{ row.totalReportNum }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">工序:</span>
                  <span class="info-value">{{
                    getDictLabel(DICT_TYPE.PRODUCTION_REPORT_TYPE, row.type)
                  }}</span>
                  <span class="info-label">标准工时:</span>
                  <span class="info-value">{{ row.standardWork||0 }} 分</span>
                </div>
                <div class="info-row" v-if="row.remark">
                  <span class="info-label">备注:</span>
                  <span class="info-value">{{ row.remark }}</span>
                </div>
                <div class="info-row" v-if="row.pmcRemark">
                  <span class="info-label">PMC备注:</span>
                  <span class="info-value">{{ row.pmcRemark }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">返工:</span>
                  <span class="info-value">{{ row.isRework === 1 ? '是' : '否' }}</span>
                  <span class="info-label">试产:</span>
                  <span class="info-value">{{ row.isTrial === 1 ? '是' : '否' }}</span>
                </div>
                <div class="info-row" v-if="row.abnormalWork">
                  <span class="info-label">异常工时:</span>
                  <span class="info-value">{{ row.abnormalWork }}</span>
                </div>
              </div>
            </div>
          </div>
          <el-table
            v-else
            :data="filteredBodyData"
            border
            stripe
            scrollbar-always-on
            @row-contextmenu="rightClick"
            @row-click="clickTableRow"
            ref="tableRef"
            style="min-height: 250px; height: calc(100vh - 455px)"
            :virtual-scrolling="true"
            :height="'calc(100vh - 455px)'"
            row-key="id"
            lazy
          >
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="时间段" min-width="180" align="center">
              <template #default="{ row }">
                <div class="time-range-row">
                  <el-time-select
                    v-model="row.productionTimeStart"
                    placeholder="起始时间"
                    :start="'08:30'"
                    :end="'23:30'"
                    :step="'00:15'"
                    placement="top-start"
                  />
                  <span class="time-range-separator">-</span>
                  <el-time-select
                    v-model="row.productionTimeEnd"
                    placeholder="结束时间"
                    :start="'08:30'"
                    :end="'23:30'"
                    :step="'00:15'"
                    :min-time="row.productionTimeStart"
                    placement="top-start"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="工序" min-width="80" prop="type" align="center">
              <template #default="{ row }">
                <el-select
                  v-model="row.type"
                  placeholder="请选择工序"
                  style="width: 100%"
                  @change="(value) => handleTypeChange(value, row)"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="返工" min-width="30" prop="isRework" align="center">
              <template #default="{ row }">
                <el-checkbox
                  :model-value="row.isRework === 1"
                  @update:model-value="(val) => handleReworkChange(val, row)"
                  align="center"
                />
              </template>
            </el-table-column>
            <el-table-column label="试产" min-width="30" prop="isTrial" align="center">
              <template #default="{ row }">
                <el-checkbox
                  :model-value="row.isTrial === 1"
                  @update:model-value="(val) => handleTryChange(val, row)"
                  align="center"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="工单号"
              min-width="160"
              prop="productionOrderCode"
              align="center"
            >
              <template #default="{ row, $index }">
                <el-autocomplete
                  v-model="row.productionOrderCode"
                  @focus="row.isEditing = true"
                  @blur="row.isEditing = false"
                  :fetch-suggestions="
                    (queryString, cb) => queryWorkSalesOptions(queryString, cb, 0, $index)
                  "
                  placeholder="输入工单号"
                  style="width: 100%"
                  @select="(item) => handleProductionOrderSelect(item, $index)"
                  :trigger-on-focus="false"
                  :debounce="300"
                  popper-class="production-order-autocomplete"
                  placement="top-start"
                >
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text"
                        >{{ item.productionOrderCode }}-{{ item.productNo }}</div
                      >
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column
              label="销售订单号"
              min-width="160"
              prop="salesOrderCode"
              align="center"
            >
              <template #default="{ row, $index }">
                <el-autocomplete
                  v-model="row.salesOrderCode"
                  @focus="row.isEditing = true"
                  @blur="row.isEditing = false"
                  :fetch-suggestions="
                    (queryString, cb) => queryWorkSalesOptions(queryString, cb, 1, $index)
                  "
                  placeholder="输入销售订单号"
                  style="width: 100%"
                  @select="(item) => handleSalesOrderSelect(item, $index)"
                  :trigger-on-focus="false"
                  :debounce="300"
                  popper-class="production-order-autocomplete"
                  placement="top-start"
                >
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column label="品号" min-width="110" prop="productNo" align="center">
              <template #default="{ row, $index }">
                <el-autocomplete
                  @focus="row.isEditing = true"
                  @blur="row.isEditing = false"
                  v-model="row.productNo"
                  :fetch-suggestions="
                    (queryString, cb) =>
                      queryProductNo(queryString, cb, row.productionOrderCode, row.salesOrderCode)
                  "
                  placeholder="输入品号查询"
                  style="width: 100%"
                  @select="(item) => handleSalesOrderSelect(item, $index)"
                  :trigger-on-focus="false"
                  :debounce="300"
                  popper-class="production-order-autocomplete"
                  placement="top-start"
                >
                  <template #default="{ item }">
                    <div class="autocomplete-item">
                      <div class="main-text"
                        >{{ item.productNo }}-{{ item.workOrderNum }}-{{ item.lineNo }}</div
                      >
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column
              label="机型/颜色（品名）"
              min-width="120"
              prop="modelsOrColor"
              align="center"
            >
              <template #default="{ row }">
                <el-input v-model="row.modelsOrColor" placeholder="机型/颜色" readonly />
              </template>
            </el-table-column>
            <el-table-column label="规格" min-width="120" prop="specs" v-if="false">
              <template #default="{ row }">
                <el-input v-model="row.specs" placeholder="规格" readonly />
              </template>
            </el-table-column>
            <el-table-column label="工单数量" min-width="70" prop="workOrderNum" align="center">
              <template #default="{ row }">
                <el-input v-model="row.workOrderNum" placeholder="工单数量" readonly />
              </template>
            </el-table-column>
            <el-table-column label="单位" min-width="40" prop="units" align="center">
              <template #default="{ row }">
                <el-input v-model="row.units" placeholder="单位" readonly />
              </template>
            </el-table-column>
            <el-table-column label="标准工时" min-width="70" prop="standardWork" align="center">
              <template #default="{ row }">
                <el-input
                  v-model="row.standardWork"
                  placeholder="标准工时"
                  type="number"
                  min="0"
                  @input="(val) => (row.standardWork = Number(val))"
                  class="no-spin-input"
                />
              </template>
            </el-table-column>
            <el-table-column label="人数" width="50" prop="number" align="center">
              <template #default="{ row }">
                <el-input
                  oninput="value=value.replace(/^\.+|[^\d.]/g,'')"
                  v-model="row.number"
                  title="人数"
                  placeholder="人数"
                  type="number"
                  min="0"
                  @input="(val) => (row.number = Number(val))"
                  class="no-spin-input"
                />
              </template>
            </el-table-column>
            <el-table-column label="生产数" min-width="70" prop="hoursReportNum" align="center">
              <template #default="{ row }">
                <el-input
                  v-model="row.hoursReportNum"
                  placeholder="生产数"
                  type="number"
                  :min="0"
                  :max="row.workOrderNum"
                  class="no-spin-input"
                  @input="(val) => validateHoursReportNum(val, row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="累计数" min-width="70" prop="totalReportNum" align="center">
              <template #default="{ row }">
                <el-input
                  v-model="row.totalReportNum"
                  placeholder="累计数"
                  :readonly="true"
                  type="number"
                  :min="0"
                />
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="50" prop="remark" align="center">
              <template #default="{ row }">
                <el-input v-model="row.remark" placeholder="备注" />
              </template>
            </el-table-column>
            <el-table-column label="PMC备注" min-width="70" prop="pmcRemark" align="center">
              <template #default="{ row }">
                <span
                  style="
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-height: 200px;
                  "
                  :title="row.pmcRemark"
                  >{{ row.pmcRemark }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <el-button type="danger" link @click="removeBodyRow($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 右键菜单 -->
          <div id="menu" class="menuDiv" style="position: fixed">
            <ul class="menuUl">
              <template v-for="(item, index) in displayedMenus" :key="index">
                <li v-if="!item.hidden" @click.stop="infoClick(index)">
                  {{ item.name }}
                </li>
              </template>
            </ul>
          </div>
        </el-card>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </Dialog>
  </div>
  <AbnormalForm ref="formRefAbnormal" @success="handleAbnormalSuccess" />
  <AttendanceForm ref="formRefAttendance" @success="handleAttendanceSuccess" />

  <!-- 快速录入对话框 -->
  <Dialog
    title="快速录入"
    v-model="quickEntryVisible"
    :width="mobile ? '100%' : '800px'"
    :draggable="true"
  >
    <div class="quick-entry-container">
      <!-- 输入销售订单号 -->
      <div class="mb-4">
        <el-form :inline="true">
          <el-form-item label="销售订单号:">
            <el-autocomplete
              v-model="salesOrderCode"
              :fetch-suggestions="queryQuickEntrySalesOptions"
              placeholder="请输入销售订单号"
              style="min-width: 300px"
              @select="handleQuickEntrySalesOrderSelect"
              :trigger-on-focus="false"
              :debounce="300"
              popper-class="sales-order-autocomplete"
              placement="bottom-start"
              clearable
            >
              <template #default="{ item }">
                <div class="autocomplete-item">
                  <div class="main-text">{{ item.salesOrderCode }}</div>
                </div>
              </template>
            </el-autocomplete>
          </el-form-item>
        </el-form>
      </div>

      <!-- 查询结果表格 -->
      <div v-if="salesOrderData.length > 0">
        <div class="mb-2">
          <span class="ml-2 text-gray-500">共 {{ salesOrderData.length }} 条数据</span>
        </div>

        <el-table
          ref="quickEntryTableRef"
          :data="salesOrderData"
          border
          size="small"
          min-height="400"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" min-width="20" align="center" />
          <el-table-column prop="salesOrderCode" label="销售订单号" min-width="120" />
          <el-table-column prop="productNo" label="品号" min-width="80" />
          <el-table-column prop="modelsOrColor" label="型号/颜色" min-width="120" />
          <el-table-column prop="workOrderNum" label="数量" min-width="40" align="center" />
          <el-table-column prop="lineNo" label="行号" min-width="40" align="center" />
          <el-table-column prop="units" label="单位" min-width="50" align="center" />
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!quickEntryLoading && salesOrderCode" class="text-center py-8 text-gray-500">
        <Icon icon="ep:document" size="48" class="mb-2" />
        <div>暂无数据</div>
      </div>
    </div>

    <template #footer>
      <el-button @click="quickEntryVisible = false">取消</el-button>
      <el-button type="primary" @click="addSelectedRows" :disabled="selectedRows.length === 0">
        添加选中项 ({{ selectedRows.length }})
      </el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DayApi, DayVO } from '@/api/report/technology/production'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { dateUtil } from '@/utils/dateUtil'
import {
  ref,
  nextTick,
  computed,
  shallowRef,
  defineAsyncComponent,
  onMounted,
  onUnmounted
} from 'vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
// 使用懒加载优化组件加载性能
const AbnormalForm = defineAsyncComponent(() => import('./AbnormalForm.vue'))
const AttendanceForm = defineAsyncComponent(() => import('./AttendanceForm.vue'))
import _, { last } from 'lodash'
import { cloneDeep, debounce } from 'lodash-es'
import { number } from 'vue-types';
/** 生产日报 表单 */
defineOptions({ name: 'DayForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

// 处理移动端屏幕旋转问题
const handleOrientationChange = () => {
  if (mobile.value) {
    // 延迟处理，确保旋转完成
    setTimeout(() => {
      // 重新设置viewport以修复缩放问题
      const viewport = document.querySelector('meta[name="viewport"]')
      if (viewport) {
        const content = viewport.getAttribute('content')
        viewport.setAttribute(
          'content',
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        )
        // 立即恢复原始设置
        setTimeout(() => {
          viewport.setAttribute('content', content || 'width=device-width, initial-scale=1.0')
        }, 100)
      }

      // 触发resize事件重新计算布局
      window.dispatchEvent(new Event('resize'))
    }, 300)
  }
}

//方向键上下左右切换单元格
const tableRef = ref() // 添加表格引用

const menus = [
  {
    name: '列批量赋值',
    operType: 1,
    visibleColumns: ['hoursReportNum', 'number', 'standardWork', 'type']
  },
  { name: '生成剩余生产数', operType: 2, visibleColumns: ['hoursReportNum'] }, // 添加可见列限制
  { name: '复制', operType: 3 }, // 添加可见列限制
  { name: '粘贴', operType: 4 } // 粘贴不依赖列名，始终显示
]

const copiedCellData = ref<any>(null) // 存储复制的单元格数据
const currentRow = ref<any>(null)
const currentColumn = ref<any>(null)
const rightClick = (row, column, event) => {
  currentRow.value = row // 保存当前行的信息
  currentColumn.value = column // 保存当前列的信息
  const menu = document.getElementById('menu')
  if (!menu) return

  event.preventDefault()

  // 判断哪些菜单项应该显示
  const filteredMenus = menus.map((item) => {
    // 如果是 operType=2（生成剩余生产数），则检查当前列是否在 visibleColumns 中
    if (
      item.operType !== 3 &&
      item.operType !== 4 &&
      !item.visibleColumns?.includes(column.property)
    ) {
      return { ...item, hidden: true }
    }
    return { ...item, hidden: false }
  })
  // 更新菜单项显示状态（可通过 ref 或其他方式传入组件）
  updateMenuDisplay(filteredMenus)
  menu.style.left = `${event.clientX + 10}px`
  menu.style.top = `${event.clientY - 310}px`
  menu.style.display = 'block'
  menu.style.zIndex = '1000'
}
const displayedMenus = ref<any[]>([])

// 更新菜单显示逻辑
const updateMenuDisplay = (filteredMenus) => {
  console.log('更新后的菜单项:', filteredMenus)
  displayedMenus.value = filteredMenus
}
//table的左键点击当前行事件
const clickTableRow = (row, column, event) => {
  const menu = document.getElementById('menu') as HTMLElement
  menu.style.display = 'none'
}

//自定义菜单的点击事件
const infoClick = async (index) => {
  const menu = document.getElementById('menu') as HTMLElement
  if (index === 0) {
    if (!currentRow.value || !currentColumn.value) {
      message.error('请先右键选择一个单元格')
      return
    }

    const fieldName = currentColumn.value.property // 获取列对应的字段名，如 'productionOrderCode'
    const fieldTitle = currentColumn.value.label
    if (
      !fieldName ||
      fieldName === 'productionOrderCode' ||
      fieldName === 'salesOrderCode' ||
      fieldName === 'modelsOrColor' ||
      fieldName === 'productNo' ||
      fieldName === 'workOrderNum' ||
      fieldName === 'units'
    ) {
      message.error('该列无法批量填写')
      const menu = document.getElementById('menu') as HTMLElement
      menu.style.display = 'none'
      return
    }
    //提示用户是否确认要把当前列的值复制到所有行
    const fieldValue = currentRow.value[fieldName] // 获取当前行该字段的值
    message
      .confirm(`是否要将 '${fieldTitle}' 列全部赋值 '${fieldValue}' 吗？`)
      .then(() => {
        // 用户点击“确定”时的操作
        bodyData.value.forEach((row) => {
          row[fieldName] = Number(fieldValue)
        })
        message.success('操作成功')
      })
      .catch(() => {
        // 用户点击“取消”时的操作
        message.info('操作已取消')
      })
  } else if (index === 1) {
    if (formType.value === 'update') {
      message.error(`该操作仅限新增数据时使用`)
      menu.style.display = 'none'
    } else {
      for (let i = 0; i < bodyData.value.length; i++) {
        const row = bodyData.value[i]
        if (!row.productionOrderCode && !row.salesOrderCode) {
          message.error(`第${i + 1}行：工单号或销售订单号不能为空，请补充！`)
          const menu = document.getElementById('menu') as HTMLElement
          menu.style.display = 'none'
          return false
        }
        if (!row.productNo) {
          message.error(`第${i + 1}行：品号不能为空，请补充！`)
          const menu = document.getElementById('menu') as HTMLElement
          menu.style.display = 'none'
          return false
        }
      }

      //生成剩余生产数
      const res = await DayApi.generated(bodyData.value)
      bodyData.value.map((row, i) => {
        row.hoursReportNum = res[i]
      })
    }
  }

  const selectedMenu = displayedMenus.value[index]

  if (!selectedMenu || selectedMenu.hidden) return

  switch (selectedMenu.operType) {
    case 3: // 复制
      if (!currentRow.value || !currentColumn.value) {
        message.error('请先右键选择一个单元格')
        menu.style.display = 'none'
        return
      }

      const fieldName = currentColumn.value.property
      if (!fieldName || ['modelsOrColor', 'workOrderNum', 'units'].includes(fieldName)) {
        message.error('该列无法复制')
        menu.style.display = 'none'
        return
      }

      const fieldValue = currentRow.value[fieldName]
      copiedCellData.value = { field: fieldName, value: fieldValue }
      message.success('已复制到剪贴板')
      break

    case 4: // 粘贴
      if (!copiedCellData.value) {
        message.warning('请先复制一个单元格内容')
        menu.style.display = 'none'
        return
      }

      if (!currentRow.value || !currentColumn.value) {
        message.error('请先右键选择一个目标单元格')
        menu.style.display = 'none'
        return
      }

      const targetField = currentColumn.value.property
      const targetRow = currentRow.value

      // 检查是否允许粘贴到当前字段（如不能粘贴到只读或不允许编辑的列）
      if (['modelsOrColor', 'workOrderNum', 'units'].includes(targetField)) {
        message.error('此列不允许粘贴内容')
        menu.style.display = 'none'
        return
      }

      // 执行粘贴
      targetRow[targetField] = copiedCellData.value.value
      message.success('粘贴成功')
      break
  }
  menu.style.display = 'none'
}

//异常工时录入、出勤录入
const formRefAbnormal = ref()
const formRefAttendance = ref()

const openFormPC = (title?: any) => {
  // 使用 lodash 的 cloneDeep 实现深拷贝
  bodyAbnormalData.value = cloneDeep(bodyData.value.filter((row) => row.displayAbnormal === 0))
  formRefAbnormal.value.openForm(
    bodyAbnormalData,
    title,
    headerData.value.batchesId,
    headerData.value.dateStr,
    headerData.value.productionLine
  )
}

const openFormAttendance = (type: number, batchesId?: any, title?: any) => {
  formRefAttendance.value.open(type, batchesId, title, headerData.value.dateStr)
}

// 获取工单销售订单数据 - 优化版本，添加缓存和防抖
const queryWorkSalesOptions = debounce(
  (queryString: string, cb: Function, type: number, rowIndex?: number) => {
    if (!queryString) {
      cb([])
      return
    }

    const placeholderText = type === 0 ? '生产工单号' : '销售订单号'
    const loadingItem = {
      [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '加载中...',
      loading: true
    }
    const noDataItem = {
      [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '暂无数据',
      noData: true
    }
    const errorItem = {
      [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '查询失败',
      error: true
    }

    // 构建缓存键
    const cacheKey = `${type}-${queryString}-${rowIndex}`
    const now = Date.now()

    // 检查缓存
    if (queryCache.has(cacheKey) && cacheTimestamps.has(cacheKey)) {
      const cacheTime = cacheTimestamps.get(cacheKey)!
      if (now - cacheTime < CACHE_EXPIRE_TIME) {
        const cachedData = queryCache.get(cacheKey)!
        cb(cachedData.length > 0 ? cachedData : [noDataItem])
        return
      }
    }

    // 先同步返回加载状态
    cb([loadingItem])

    //把已经选中的工单号和销售订单号和品号 传给后端
    // 构造数据时排除当前行
    const salesOrderCodes = bodyData.value
      .filter(
        (item, index) =>
          item.salesOrderCode !== null &&
          item.salesOrderCode !== '' &&
          item.salesOrderCode !== undefined &&
          item.productNo !== null &&
          item.productNo !== '' &&
          item.productNo !== undefined &&
          index !== rowIndex
      )
      .map((item) => ({ salesOrderCode: item.salesOrderCode, productNo: item.productNo }))

    // 把已选中的工单号、销售订单号、品号传给后端（排除当前行）
    const data = {
      codeList: salesOrderCodes,
      docNo: queryString,
      workSelectCode: bodyData.value[rowIndex || 0]?.salesOrderCode,
      workSelectNo: bodyData.value[rowIndex || 0]?.productNo,
      type: type
    }

    DayApi.getWorkSalesOptions(data)
      .then((response) => {
        const suggestions = response || []

        // 缓存结果
        queryCache.set(cacheKey, suggestions)
        cacheTimestamps.set(cacheKey, now)

        cb(suggestions.length > 0 ? suggestions : [noDataItem])
      })
      .catch((error) => {
        console.error(`查询${placeholderText}失败:`, error)
        cb([errorItem])
      })
  },
  300
) // 300ms防抖
// 查询品号
const queryProductNo = (
  queryString: string,
  cb: Function,
  productionOrderCode: string,
  salesOrderCode: string
) => {
  if (!queryString) {
    cb([])
    return
  }
  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productNo: '加载中...', loading: true }])
  //把工单号和销售订单号和输入的品号都传给后端
  const data = {
    productionOrderCode: productionOrderCode, // 生产工单号
    salesOrderCode: salesOrderCode, // 销售订单号
    productNo: queryString // 输入的品号
  }
  DayApi.getProductNo(data)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productNo: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ productNo: '查询失败', error: true }])
    })
}
// 选择后 生产工单号后的处理
const handleProductionOrderSelect = async (item: any, rowIndex: number) => {
  if (item.loading || item.noData || item.error) return

  const row = bodyData.value[rowIndex]
  if (row && item) {
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.lineNo = item.lineNo
    row.demand = item.demand
    row.specs = item.specs
    row.workOrderNum = item.workOrderNum
    row.units = item.units
    row.pmcRemark = item.pmcRemark
    row.salesman =item.salesman

    const dayVO: any = {
      productionOrderCode: item.productionOrderCode,
      salesOrderCode: item.salesOrderCode,
      type: row.type,
      dateStr: headerData.value.dateStr
    }

    const data = await DayApi.getOrderCodeNum(dayVO)
    const key = `${row.productionOrderCode}-${row.type}`

    // 设置初始累计值
    rowTotalReportNums.value[key] = data || 0
    row.totalReportNum = data || 0

    // 如果该订单已有生产数，则用已有的生产数作为原始值
    originalReportNums.value[key] = row.hoursReportNum || 0
  }
}
const customPrefix = shallowRef({
  render() {
    return null
  }
})
// 选择后 销售订单号后的处理
const handleSalesOrderSelect = async (item: any, rowIndex: number) => {
  // 忽略加载状态、无数据状态、错误状态的选择
  if (item.loading || item.noData || item.error) {
    return
  }
  const row = bodyData.value[rowIndex]
  if (row && item) {
    // 设置生产工单号
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.lineNo = item.lineNo
    row.demand = item.demand
    row.specs = item.specs
    row.workOrderNum = item.workOrderNum
    row.units = item.units
    row.pmcRemark = item.pmcRemark
    row.salesman =item.salesman
    handleTypeChange(row.type, row)
  }
}

// 切换工序时查询累计数
const handleTypeChange = async (value: number, row: any) => {
  // 新增：主动触发校验
  if (!row.productionOrderCode) return

  const dayVO: any = {
    productionOrderCode: row.productionOrderCode,
    salesOrderCode: row.salesOrderCode,
    type: value,
    dateStr: headerData.value.dateStr
  }

  const data = await DayApi.getOrderCodeNum(dayVO)
  const key = `${row.productionOrderCode}-${value}`

  row.type = value
  rowTotalReportNums.value[key] = data || 0
  row.totalReportNum = data || 0

  // 如果该订单已有生产数，则用已有的生产数作为原始值
  originalReportNums.value[key] = row.hoursReportNum || 0
}

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
// const dialogSizeMap = new Map<string, { width: string; height: string }>()

const totalReportNum = ref(0)

const officiallyList = ref([] as any)
const temporaryList = ref([] as any)
const borrowList = ref([] as any)
const headerData = ref({
  productionLine: 1,
  teamLeader: '',
  dateStr: '',
  requiredAttendanceNum: undefined,
  actualAttendanceNum: undefined,
  assembledNum: undefined,
  assembledTotal: undefined,
  standardWork: undefined,
  remarkHead: undefined,
  batchesId: undefined,
  numberWork: undefined,
  type: 1
})

// 表身数据（其他字段）
// 恢复使用ref，确保响应式更新正常工作
const bodyData = ref<any[]>([])

// 使用普通ref，因为filters对象较小
const filters = ref({
  productionOrderCode: '',
  salesOrderCode: '',
  productNo: ''
})

const refreshQuery = () => {
  filters.value = {
    productionOrderCode: '',
    salesOrderCode: '',
    productNo: ''
  }
}

// 使用computed优化过滤逻辑，确保响应式更新
const filteredBodyData = computed(() => {
  // 确保bodyData的变化能被监听到
  const data = bodyData.value
  const { productionOrderCode, salesOrderCode, productNo } = filters.value

  // 如果没有任何过滤条件，直接返回原数组
  if (!productionOrderCode && !salesOrderCode && !productNo) {
    return data
  }

  return data.filter((row) => {
    if (!row) return false

    const matchProduction =
      !productionOrderCode ||
      (row.productionOrderCode && row.productionOrderCode.toString().includes(productionOrderCode))
    const matchSales =
      !salesOrderCode ||
      (row.salesOrderCode && row.salesOrderCode.toString().includes(salesOrderCode))
    const matchProduct =
      !productNo || (row.productNo && row.productNo.toString().includes(productNo))

    return row.isEditing || (matchProduction && matchSales && matchProduct)
  })
})

//异常工时表身数据
const bodyAbnormalData = ref<any[]>([])

// 优化：创建空行模板，减少重复对象创建
const emptyRowTemplate = {
  productionTimeStart: '',
  productionTimeEnd: '',
  type: 1,
  productionOrderCode: undefined,
  salesOrderCode: undefined,
  productNo: undefined,
  modelsOrColor: undefined,
  specs: undefined,
  workOrderNum: undefined,
  pmcRemark: undefined,
  units: undefined,
  number: null,
  hoursReportNum: null,
  totalReportNum: undefined,
  standardWork: null,
  abnormalWork: undefined,
  abnormalNum: undefined,
  abnormalRemark: undefined,
  abnormalCountermeasures: undefined,
  abnormalTimeStart: undefined,
  abnormalTimeEnd: undefined,
  actualWork: undefined,
  isRework: 0,
  isTrial: 0,
  remark: undefined,
  isEditing: false,
  id: undefined // 添加唯一标识
}

// 创建空的表身行 - 优化版本
const createEmptyBodyRow = (
  lastEndTime = '',
  lastStrTime = '',
  number = 0,
  standardWork = 0,
  type = 1
) => {
  return {
    ...emptyRowTemplate,
    productionTimeStart: lastStrTime,
    productionTimeEnd: lastEndTime,
    type: type,
    number: number,
    standardWork: standardWork,
    displayAbnormal: 0
  }
}

const formRules = reactive({
  productionLine: [{ required: true, message: '请选择生产产线', trigger: 'blur' }],
  teamLeader: [{ required: true, message: '请输入班组长', trigger: 'blur' }],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 快速录入相关数据
const quickEntryVisible = ref(false)
const quickEntryLoading = ref(false)
const quickEntryTableRef = ref()
const salesOrderCode = ref('')
const salesOrderData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const selectAll = ref(false)
const isIndeterminate = ref(false)

// 新增监听器存储对象 - 使用 WeakMap 来避免索引问题
const rowWatchers = ref(new Map<any, () => void>())

// 添加查询缓存来优化自动完成组件性能
const queryCache = new Map<string, any[]>()
const CACHE_EXPIRE_TIME = 5 * 60 * 1000 // 5分钟缓存过期时间
const cacheTimestamps = new Map<string, number>()

// 性能监控函数
const performanceMonitor = {
  startTime: 0,
  start(label: string) {
    this.startTime = performance.now()
    console.log(`🚀 [Performance] ${label} started`)
  },
  end(label: string) {
    const endTime = performance.now()
    const duration = endTime - this.startTime
    console.log(`✅ [Performance] ${label} completed in ${duration.toFixed(2)}ms`)
    return duration
  }
}

const renderedBodyData = ref<any[]>([]) // 当前渲染的数据
const batchSize = 5 // 每次加载5条
const currentIndex = ref(0) // 当前已加载到的位置

// 初始化加载前5条数据
const loadMoreData = async () => {
  if (isLoading.value) return

  // 如果强制加载所有数据
  if (forceLoadAll.value) {
    await loadAllData()
    return
  }

  try {
    // 检查是否需要强制加载所有数据（当剩余数据不多时）
    const remainingCount = filteredBodyData.value.length - currentIndex.value
    if (remainingCount <= batchSize * 2) {
      forceLoadAll.value = true
      await loadAllData()
      return
    }

    const loadCount = isLowPerformanceDevice() ? batchSize * 2 : batchSize
    const remainingData = filteredBodyData.value.slice(
      currentIndex.value,
      currentIndex.value + loadCount
    )

    if (remainingData.length === 0) return

    renderedBodyData.value = [...renderedBodyData.value, ...remainingData]
    currentIndex.value += remainingData.length
  } catch (error) {
    console.error('加载数据时出错:', error)
  } finally {
    isLoading.value = false
  }
}

// 添加设备性能检测函数
const isLowPerformanceDevice = () => {
  // 简单的性能检测逻辑
  const start = performance.now()
  let iterations = 0
  while (performance.now() - start < 5) {
    iterations++
  }
  return iterations < 5000 // 根据实际情况调整阈值
}
// 实现加载所有数据的方法
const loadAllData = async () => {
  try {
    renderedBodyData.value = [...filteredBodyData.value]
    currentIndex.value = filteredBodyData.value.length
  } catch (error) {
    console.error('加载所有数据时出错:', error)
  }
}

// 重置加载逻辑
const resetRenderedData = () => {
  const newData = filteredBodyData.value.slice(0, currentIndex.value)
  renderedBodyData.value = [...newData]
}

watchEffect(() => {
  resetRenderedData()
})

// 添加新的响应式变量
const forceLoadAll = ref(false)

// 判断是否已经加载完所有数据
const hasMoreData = computed(() => {
  if (forceLoadAll.value) return false
  return currentIndex.value < filteredBodyData.value.length
})

const isLoading = ref(false)

// 新增表身行
const addBodyRow = async () => {
  let lastEndTime = ''
  let lastStrTime = ''
  if (bodyData.value.length > 0) {
    lastEndTime = bodyData.value[bodyData.value.length - 1].productionTimeEnd || ''
    lastStrTime = bodyData.value[bodyData.value.length - 1].productionTimeStart || ''
  }
  const number = headerData.value.assembledNum || 0
  const standardWork = headerData.value.standardWork || 0
  const type = headerData.value.type || 0

  // 创建新行并添加到数组
  const newRow = createEmptyBodyRow(lastEndTime, lastStrTime, number, standardWork, type)
  if (formType.value === 'update') {
    newRow.isEditing = true
  }
  bodyData.value.push(newRow)

  // 手动触发响应式更新
  bodyData.value = [...bodyData.value]
}

//监听返工字段
const handleReworkChange = (val: boolean, row: any) => {
  // 更新返工标识
  row.isRework = val ? 1 : 0
  updateAbnormalWorkByRow(val, row)
}

//监听试产字段
const handleTryChange = (val: boolean, row: any) => {
  row.isTrial = val ? 1 : 0
  updateAbnormalWorkByRow(val, row)
}
const updateAbnormalWorkByRow = (val: boolean, row: any) => {
  // 如果勾选了返工/试产，自动填充异常字段
  if (val) {
    //任意一个为空，则无法勾选试产或者返工
    if(!row.number||!row.productionTimeStart||!row.productionTimeEnd||!row.hoursReportNum){
      message.error('请先填写时间段、人数、生产数！')
      row.isTrial=0
      row.isRework=0
      return
    }
    // 生产时间段赋值给异常时间段
    row.abnormalTimeStart = row.productionTimeStart
    row.abnormalTimeEnd = row.productionTimeEnd

    // 生产数量赋值给异常数量
    row.abnormalReportNum = row.hoursReportNum

    // 人数赋值给异常人数
    row.abnormalNum = row.number
    
    row.displayAbnormal = 0

    // 计算并更新异常总工时
    updateAbnormalWork(row)
  } else if (row.isRework === 0 && row.isTrial === 0) {
    row.abnormalTimeStart = ''
    row.abnormalTimeEnd = ''
    row.abnormalNum = 0
    row.abnormalReportNum = 0
    row.abnormalWork = 0
  }
}
// 计算异常总工时的函数（参考AbnormalForm.vue中的逻辑）
const updateAbnormalWork = (row: any) => {
  // 只有当异常时间段和人数都存在时才计算工时
  if (row.abnormalTimeStart && row.abnormalTimeEnd && row.abnormalNum !== undefined) {
    const minutes = calculateEffectiveMinutes(row.abnormalTimeStart, row.abnormalTimeEnd)
    row.abnormalWork = parseFloat(((minutes / 60) * (row.abnormalNum || 0)).toFixed(3))
  } else {
    row.abnormalWork = 0
  }
}

// 计算有效分钟数的函数（从AbnormalForm.vue中提取的逻辑）
const parseTime = (timeStr: string): Date => {
  const now = new Date()
  const [hours, minutes] = timeStr.split(':').map(Number)
  return new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes)
}

const isRestPeriod = (time: Date): boolean => {
  const restPeriods = [
    { start: '12:00', end: '13:00' },
    { start: '17:30', end: '18:00' }
  ]
  for (const period of restPeriods) {
    const restStart = parseTime(period.start)
    const restEnd = parseTime(period.end)
    if (time >= restStart && time < restEnd) return true
  }
  return false
}

const calculateEffectiveMinutes = (start: string, end: string): number => {
  const startDate = parseTime(start)
  const endDate = parseTime(end)
  if (endDate <= startDate) return 0
  let totalMinutes = 0
  let current = new Date(startDate)
  while (current < endDate) {
    const nextMinute = new Date(current.getTime() + 60000)
    const minuteEndTime = nextMinute > endDate ? endDate : nextMinute
    if (!isRestPeriod(current)) {
      totalMinutes += (minuteEndTime.getTime() - current.getTime()) / 60000
    }
    current = minuteEndTime
  }
  return totalMinutes
}

// 进入编辑模式
const enterEditMode = (index: number) => {
  // 为该行设置编辑状态
  bodyData.value[index].isEditing = true
}

// 退出编辑模式
const saveEditMode = (index: number) => {
  // 为该行设置编辑状态
  bodyData.value[index].isEditing = false
}
// 获取字典标签
const getDictLabel = (dictType: string, value: any) => {
  const dictOptions = getIntDictOptions(dictType)
  const dict = dictOptions.find((option) => option.value === value)
  return dict ? dict.label : ''
}

// 删除行时清理监听
const removeBodyRow = (index: number) => {
  message.confirm('确定要第' + (index + 1) + '行删除吗？').then(() => {
    //最后一行不允许删除
    if (bodyData.value.length === 1) {
      message.error('最后一行不允许删除！')
      return
    }

    // 获取要删除的行对象
    const rowToDelete = bodyData.value[index]

    // 清理该行的监听器
    if (rowWatchers.value.has(rowToDelete)) {
      rowWatchers.value.get(rowToDelete)!()
      rowWatchers.value.delete(rowToDelete)
    }

    // 删除行
    bodyData.value.splice(index, 1)

    // 手动触发响应式更新
    bodyData.value = [...bodyData.value]
  })
}

/** 打开弹窗 */
const open = async (type: string, batchesId?: any) => {
  performanceMonitor.start('Form Opening')
  refreshQuery()
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (batchesId) {
    formLoading.value = true
    try {
      const data = await DayApi.getDay(batchesId)
      // 分离表头和表身数据
      headerData.value = {
        productionLine: data[0].productionLine,
        teamLeader: data[0].teamLeader,
        dateStr: data[0].dateStr,
        requiredAttendanceNum: data[0].requiredAttendanceNum,
        actualAttendanceNum: data[0].actualAttendanceNum,
        assembledNum: data[0].assembledNum,
        assembledTotal: data[0].assembledTotal,
        standardWork: data[0].standardWork,
        remarkHead: data[0].remarkHead,
        batchesId: data[0].batchesId,
        numberWork: data[0].numberWork,
        type: data[0].type
      }
      // 如果是修改
      const mappedData = data.map((item: any) => ({
        id: item.id,
        productionTimeStart: item.productionTime.split('-')[0],
        productionTimeEnd: item.productionTime.split('-')[1],
        productionTime: item.productionTime,
        productionOrderCode: item.productionOrderCode,
        salesOrderCode: item.salesOrderCode,
        productNo: item.productNo,
        modelsOrColor: item.modelsOrColor,
        workOrderNum: item.workOrderNum,
        units: item.units,
        number: item.number,
        hoursReportNum: item.hoursReportNum,
        totalReportNum: item.totalReportNum,
        lineNo: item.lineNo,
        demand: item.demand,
        standardWork: item.standardWork,
        actualWork: item.actualWork,
        abnormalWork: item.abnormalWork,
        abnormalNum: item.abnormalNum,
        abnormalReportNum: item.abnormalReportNum,
        abnormalRemark: item.abnormalRemark,
        abnormalCountermeasures: item.abnormalCountermeasures,
        abnormalTimeStart: (item.abnormalTime || '').split('-')[0],
        abnormalTimeEnd: (item.abnormalTime || '').split('-')[1] || '',
        type: item.type,
        audit: item.audit,
        isRework: item.isRework,
        isTrial: item.isTrial,
        remark: item.remark,
        pmcRemark: item.pmcRemark,
        displayAbnormal: item.displayAbnormal
      }))

      // 赋值给bodyData并触发响应式更新
      bodyData.value = mappedData

      // 初始化 originalReportNums 和 rowTotalReportNums
      bodyData.value.forEach((row) => {
        const key = `${row.productionOrderCode}-${row.type}`
        originalReportNums.value[key] = row.hoursReportNum || 0
        rowTotalReportNums.value[key] = row.totalReportNum || 0
      })

      const attendanceData = await DayApi.getAttendanceDay(batchesId)
      officiallyList.value = attendanceData.tabData.filter((data) => data.type == 0)
      temporaryList.value = attendanceData.tabData2
      borrowList.value = attendanceData.tabData5
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时，默认添加一行表身数据
    bodyData.value = [createEmptyBodyRow()]
    // setupRowWatcher(bodyData.value[0])

    //赋值当前登录用户为班组长
    //查询当前用户上一条表头信息
    const data = await DayApi.getPrevious()
    if (data) {
      headerData.value.productionLine = data.productionLine
      headerData.value.dateStr = dateUtil(new Date()).format('YYYY-MM-DD')
      ;(headerData.value.requiredAttendanceNum = data.requiredAttendanceNum),
        (headerData.value.actualAttendanceNum = data.actualAttendanceNum),
        (headerData.value.assembledNum = data.assembledNum),
        (headerData.value.remarkHead = data.remarkHead),
        (headerData.value.type = data.type)
      syncBodyNumber()
    } else {
      headerData.value.teamLeader = useUserStore().user.nickname
      headerData.value.dateStr = dateUtil(new Date()).format('YYYY-MM-DD')
      headerData.value.type = 1
      headerData.value.productionLine = 1
    }

    headerData.value.teamLeader = useUserStore().user.nickname
    totalReportNum.value = 0
    //重置批次号
    headerData.value.batchesId = undefined
  }

  // 等待DOM更新后重新收集输入框引用
  nextTick(() => {
    performanceMonitor.end('Form Opening')
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
// 处理异常工时表单提交成功事件
const handleAbnormalSuccess = (data: any) => {
  const processedData = cloneDeep(data)
  console.log('接收到异常工时数据:', processedData)
  // PC端
  if (!processedData || processedData.length === 0) {
    bodyData.value.forEach((item: any) => {
      item.abnormalWork = 0
      item.abnormalRemark = ''
      item.abnormalCountermeasures = ''
      item.abnormalTimeStart = ''
      item.abnormalTimeEnd = ''
      item.abnormalReportNum = 0
      item.displayAbnormal = 1
    })
    return
  }

  // 存储已处理过的索引，便于后续清理未匹配的行
  const matchedIndexes = new Set<number>()

  // 步骤一：更新匹配的行
  processedData.forEach((row: any) => {
    if (!row.isNew) {
      let matchedIndex = -1
      // 精确匹配逻辑 - 优先使用 ID 匹配，然后使用完整属性匹配
      if (row.id) {
        // 如果有 ID，优先使用 ID 匹配
        matchedIndex = bodyData.value.findIndex((item: any) => item.id === row.id)
      } else {
        // 没有 ID 时，使用完整属性匹配
        matchedIndex = bodyData.value.findIndex((item: any) => 
          item.productionTimeStart === row.productionTimeStart &&
          item.productionTimeEnd === row.productionTimeEnd &&
          item.productionOrderCode === row.productionOrderCode &&
          item.salesOrderCode === row.salesOrderCode &&
          item.productNo === row.productNo &&
          item.type === row.type
        )
      }

      // 如果找到了匹配的行
      if (matchedIndex !== -1) {
        matchedIndexes.add(matchedIndex)
        const item = bodyData.value[matchedIndex]
        
        // 使用深拷贝确保值不会被共享引用
        item.abnormalReportNum = cloneDeep(row.abnormalReportNum)
        item.abnormalNum = cloneDeep(row.abnormalNum)
        item.abnormalWork = cloneDeep(row.abnormalWork)
        item.abnormalRemark = cloneDeep(row.abnormalRemark)
        item.abnormalCountermeasures = cloneDeep(row.abnormalCountermeasures)
        item.abnormalTimeStart = cloneDeep(row.abnormalTimeStart)
        item.abnormalTimeEnd = cloneDeep(row.abnormalTimeEnd)
        item.displayAbnormal = cloneDeep(row.displayAbnormal)
        console.log('匹配行:', item)
      }
    }
  })

  // 步骤二：清空未匹配的行中的异常字段
  for (let i = 0; i < bodyData.value.length; i++) {
    if (!matchedIndexes.has(i)) {
      bodyData.value[i].abnormalNum = 0
      bodyData.value[i].abnormalWork = 0
      bodyData.value[i].abnormalRemark = ''
      bodyData.value[i].abnormalCountermeasures = ''
      bodyData.value[i].abnormalTimeStart = ''
      bodyData.value[i].abnormalTimeEnd = ''
      bodyData.value[i].displayAbnormal = 1
    }
  }
  //赋值批次ID
  headerData.value.batchesId = processedData[0].batchesId
}
//处理出勤工时表单提交成功事件
const handleAttendanceSuccess = (data: any, workData: any) => {
  headerData.value.batchesId = data.batchesId
  headerData.value.numberWork = data.attendanceWorkingHours
  headerData.value.assembledTotal = data.assembledTotal
  console.log('接收到校验数据:', workData)
  officiallyList.value = workData.officially
  temporaryList.value = workData.temporary
  borrowList.value = workData.borrow
}

const validateBodyForm = () => {
  // 检查：同一工单号 + 同一工序不能重复添加
  // const uniqueKeys = new Set<string>();
  if (!headerData.value.numberWork || headerData.value.numberWork <= 0) {
    message.error('请填写正确的出勤工时！')
    return false
  }
  for (let i = 0; i < bodyData.value.length; i++) {
    const row = bodyData.value[i]
    if (!row.productionTimeStart || !row.productionTimeEnd) {
      message.error(`第${i + 1}行：请选择时间段`)
      return false
    }
    if (!row.workOrderNum) {
      message.error(`第${i + 1}行：工单数量不能为空，请选择生产工单号或销售订单号`)
      return false
    }
    if (!row.number) {
      message.error(`第${i + 1}行：人数不能为空，请输入出勤人数`)
      return false
    }
    if (!row.hoursReportNum) {
      message.error(`第${i + 1}行：请输入生产数`)
      return false
    }
  }
  return true
}

const handleClose = (done: () => void) => {
  ElMessageBox.confirm('内容还未保存，确定关闭吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 用户点击确定，执行关闭
      done()
    })
    .catch(() => {
      // 用户点击取消，不执行 done()，即不关闭
    })
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于成功后的回调

const errorMessages = ref<string[]>([])

const cancel = async () => {
  ElMessageBox.confirm('内容还未保存，确定关闭吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 用户点击确定，执行关闭
      dialogVisible.value = false
    })
    .catch(() => {
      // 用户点击取消，不关闭
    })
}

//提交表单
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  if (bodyData.value.length === 0) {
    message.error('请至少添加一行表身数据')
    return
  }
  let totalRowNum = 0
  bodyData.value.forEach((item: any) => {
    totalRowNum += item.number
  })

  //同订单或工单同工序，累计数量不能超过订单数量
  if (!validateOrderAndWorkOrder()) {
    return
  }
  // 校验表身数据
  if (!validateBodyForm()) {
    return
  }

  errorMessages.value = []
  //验证出勤人数与行生产人数是否一致
  //一行一行的验证主表时间段内 人数是否与出勤人数一致
  for (let i = 0; i < bodyData.value.length; i++) {
    let totalNumber = 0
    // 主表时间段
    const mainStart = bodyData.value[i].productionTimeStart // 如 "08:30"
    const mainEnd = bodyData.value[i].productionTimeEnd
    const mainStartMin = timeToMinutes(mainStart)
    const mainEndMin = timeToMinutes(mainEnd)
    // 遍历 officiallyList 检查时间范围
    officiallyList.value.forEach((entry) => {
      const [entryStart, entryEnd] = entry.officiallyTimeRange.split('-')
      const entryStartMin = timeToMinutes(entryStart)
      const entryEndMin = timeToMinutes(entryEnd)
      // 判断主表时间段是否完全包含在 entry 的时间段内
      if (mainStartMin >= entryStartMin && mainEndMin <= entryEndMin) {
        totalNumber += entry.officiallyNumber || 0
      }
    })

    temporaryList.value.forEach((entry) => {
      const [entryStart, entryEnd] = entry.temporaryTimeRange.split('-')
      const entryStartMin = timeToMinutes(entryStart)
      const entryEndMin = timeToMinutes(entryEnd)

      // 判断主表时间段是否完全包含在 entry 的时间段内
      if (mainStartMin >= entryStartMin && mainEndMin <= entryEndMin) {
        totalNumber += entry.temporaryNumber || 0
      }
    })

    borrowList.value.forEach((entry) => {
      const [entryStart, entryEnd] = entry.borrowTimeRange.split('-')
      const entryStartMin = timeToMinutes(entryStart)
      const entryEndMin = timeToMinutes(entryEnd)

      // 判断主表时间段是否完全包含在 entry 的时间段内
      if (mainStartMin >= entryStartMin && mainEndMin <= entryEndMin) {
        totalNumber += entry.borrowNumber || 0
      }
    })
    if (totalNumber !== bodyData.value[i].number) {
      errorMessages.value.push(`第${i + 1}行：出勤人数与行生产人数不一致，请检查！`)
    }
  }
  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      // 如果有错误信息，显示确认对话框
      if (errorMessages.value.length > 0) {
        // 构建错误信息HTML
        const errorHtml = errorMessages.value.map((msg) => `<p>${msg}</p>`).join('')

        // 使用Promise控制流程
        const confirmResult = await ElMessageBox.confirm(
          `<div style="text-align: left;">以下错误需要检查：<br>${errorHtml}</div>`,
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        ).catch(() => 'cancel')

        // 如果用户取消，则终止提交
        if (confirmResult === 'cancel') {
          return
        }
      }
      // 新增时，将表头数据合并到每一行表身数据中，组成数组一次性提交
      const createData = bodyData.value
        .filter((bodyRow) => !bodyRow.isAdd)
        .map((bodyRow) => ({
          ...headerData.value,
          ...bodyRow,
          productionTime: `${bodyRow.productionTimeStart || ''}-${bodyRow.productionTimeEnd || ''}`,
          abnormalTime: `${bodyRow.abnormalTimeStart || ''}-${bodyRow.abnormalTimeEnd || ''}`,
          type: bodyRow.type
        })) as DayVO[]
      if (createData.length > 0) {
        await DayApi.createDay(createData)
      }
      message.success(t('common.createSuccess'))
    } else {
      // 修改时
      await DayApi.batchUpdateDays(
        bodyData.value.map((row) => ({
          ...headerData.value,
          ...row,
          type: row.type,
          productionTime: `${row.productionTimeStart || ''}-${row.productionTimeEnd || ''}`,
          abnormalTime: `${row.abnormalTimeStart || ''}-${row.abnormalTimeEnd || ''}`
        }))
      )
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 将时间字符串转换为分钟数（例如 "08:30" -> 510）
function timeToMinutes(timeStr: string): number {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}
/* 单头组装人数、标准工时填写同步至单身 */
const syncBodyNumber = () => {
  bodyData.value.forEach((row) => {
    row.number = headerData.value.assembledNum
    row.standardWork = headerData.value.standardWork
  })
}

const validateOrderAndWorkOrder = () => {
  const groupMap = new Map()

  for (let i = 0; i < bodyData.value.length; i++) {
    const row = bodyData.value[i]
    if (row.isRework === 1) {
      continue
    }
    //确保必要字段存在
    // 构造唯一 key：工序 + 工单号 + 销售订单号 + 品号 + 行号
    const key = `${row.type}-${row.productionOrderCode}-${row.salesOrderCode}-${row.productNo}-${row.lineNo}`
    console.log(key)
    // 初始化该组数据
    if (!groupMap.has(key)) {
      groupMap.set(key, {
        totalHoursReportNum: 0,
        workOrderNum: row.workOrderNum || 0
      })
    }

    const group = groupMap.get(key)
    group.totalHoursReportNum += Number(row.hoursReportNum) || 0

    // 如果当前行是该组的第一个条目，记录工单数量
    if (group.firstRow === undefined) {
      group.firstRow = i
      group.workOrderNum = row.workOrderNum || 0
    }
  }

  // 遍历检查每个组是否超量
  for (const [key, group] of groupMap.entries()) {
    if (group.totalHoursReportNum > group.workOrderNum) {
      message.error(
        `第${group.firstRow + 1}行：同订单/工单/工序/行号，累计生产数（${group.totalHoursReportNum}）不能超过工单数量（${group.workOrderNum}）`
      )
      return false
    }
  }
  return true
}

// 快速录入相关方法
/** 打开快速录入对话框 */
const openQuickEntry = () => {
  quickEntryVisible.value = true
  salesOrderCode.value = ''
  salesOrderData.value = []
  selectedRows.value = []
  selectAll.value = false
  isIndeterminate.value = false
}

/** 快速录入销售订单号联想查询 */
const queryQuickEntrySalesOptions = (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  const loadingItem = { salesOrderCode: '加载中...', loading: true }
  const noDataItem = { salesOrderCode: '暂无数据', noData: true }
  const errorItem = { salesOrderCode: '查询失败', error: true }

  // 先同步返回加载状态
  cb([loadingItem])
  console.log('cb', cb)
  // 调用联想接口
  const data = {
    codeList: [],
    docNo: queryString,
    workSelectCode: queryString,
    type: 1
  }

  DayApi.getWorkSalesOptions(data)
    .then((response) => {
      const suggestions = response || []
      cb(suggestions.length > 0 ? suggestions : [noDataItem])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([errorItem])
    })
}

/** 选择后 销售订单号的处理 */
const handleQuickEntrySalesOrderSelect = async (item: any) => {
  // 忽略加载状态、无数据状态、错误状态的选择
  if (item.loading || item.noData || item.error) {
    return
  }

  // 选择销售订单号后，立即调用获取详细数据的接口
  salesOrderCode.value = item.salesOrderCode
  await fetchSalesOrderData()
}

/** 根据销售订单号获取数据 */
const fetchSalesOrderData = async () => {
  if (!salesOrderCode.value.trim()) {
    message.warning('请输入销售订单号')
    return
  }

  quickEntryLoading.value = true
  try {
    const response = await DayApi.getSalesOrderInfo(salesOrderCode.value.trim())
    if (response && response.length > 0) {
      salesOrderData.value = response

      // 使用nextTick确保DOM更新后再设置选中状态
      await nextTick()

      // 默认全选所有数据
      selectedRows.value = [...response]
      selectAll.value = true
      isIndeterminate.value = false

      // 手动设置表格选中状态
      if (quickEntryTableRef.value) {
        response.forEach((row) => {
          quickEntryTableRef.value.toggleRowSelection(row, true)
        })
      }
    } else {
      salesOrderData.value = []
      selectedRows.value = []
      selectAll.value = false
      isIndeterminate.value = false
      message.info('未找到相关数据')
    }
  } catch (error) {
    console.error('查询销售订单数据失败:', error)
    message.error('查询失败，请检查网络连接')
    salesOrderData.value = []
    selectedRows.value = []
    selectAll.value = false
    isIndeterminate.value = false
  } finally {
    quickEntryLoading.value = false
  }
}
/** 处理选择变化 */
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
  const total = salesOrderData.value.length
  const selected = selection.length

  selectAll.value = selected === total
  isIndeterminate.value = selected > 0 && selected < total
}

/** 添加选中的行到表身 */
const addSelectedRows = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请选择要添加的数据')
    return
  }

  // 获取当前最后一行的时间信息
  let lastEndTime = ''
  let lastStrTime = ''
  if (bodyData.value.length > 0) {
    const lastRow = bodyData.value[bodyData.value.length - 1]
    lastEndTime = lastRow.productionTimeEnd || ''
    lastStrTime = lastRow.productionTimeStart || ''
  }

  const number = headerData.value.assembledNum || 0
  const standardWork = headerData.value.standardWork || 0
  const type = headerData.value.type || 0

  // 为每个选中的行创建表身数据
  const newRows: any[] = []
  selectedRows.value.forEach((item) => {
    const newRow = createEmptyBodyRow(lastEndTime, lastStrTime, number, standardWork, type)

    // 填充从销售订单获取的数据
    newRow.salesOrderCode = item.salesOrderCode
    newRow.productNo = item.productNo
    newRow.modelsOrColor = item.modelsOrColor
    newRow.specs = item.specs || ''
    newRow.workOrderNum = item.workOrderNum
    newRow.units = item.units
    newRow.pmcRemark = item.pmcRemark

    // 添加额外的字段（这些字段在createEmptyBodyRow中没有定义）
    ;(newRow as any).lineNo = item.lineNo
    ;(newRow as any).demand = item.demand

    newRows.push(newRow)
  })

  // 批量添加新行并触发响应式更新
  bodyData.value = [...bodyData.value, ...newRows]

  // 关闭对话框
  quickEntryVisible.value = false
  message.success(`成功添加 ${selectedRows.value.length} 条数据`)
}

// 限制日期选择范围 - 只能选择当年
const disabledDate = (time: Date) => {
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  
  // 设置当年的开始和结束日期
  const startOfYear = new Date(currentYear, 0, 1) // 当年第一天
  const endOfYear = new Date(currentYear, 11, 31) // 当年最后一天
  
  // 禁用不在当年范围内的日期
  return time < startOfYear || time > endOfYear
}

/** 重置表单 */
const resetForm = () => {
  // 清理所有监听器
  rowWatchers.value.forEach((stop) => {
    stop()
  })
  rowWatchers.value.clear()

  // 清理查询缓存
  queryCache.clear()
  cacheTimestamps.clear()

  headerData.value = {
    teamLeader: '',
    dateStr: '',
    requiredAttendanceNum: undefined,
    actualAttendanceNum: undefined,
    assembledNum: undefined,
    assembledTotal: undefined,
    remarkHead: undefined,
    standardWork: undefined,
    batchesId: undefined,
    numberWork: undefined,
    type: 1,
    productionLine: 1
  }
  bodyData.value = []
  originalReportNums.value = {}
  rowTotalReportNums.value = {}
  // 重置移动端渲染数据
  renderedBodyData.value = []
  currentIndex.value = 0
  forceLoadAll.value = false // 重置强制加载标志

  formRef.value?.resetFields()
}

// 存储每个工单的原始生产数值
const originalReportNums = ref<Record<string, number>>({})
// 记录每行的累计数
const rowTotalReportNums = ref<Record<string, number>>({})
const validateHoursReportNum = (val: string, row: any) => {
  const numVal = Number(val)

  if (numVal > row.workOrderNum) {
    row.hoursReportNum = row.workOrderNum
    message.warning('小时完成数量不能超过工单数量')
  } else {
    const orderId = `${row.productionOrderCode}-${row.type}`

    const currentOriginal = originalReportNums.value[orderId] || 0
    const change = numVal - currentOriginal

    // 如果是返工，则不参与累计计算
    if (row.isRework === 1) {
      // 返工情况下，totalReportNum 不变化，保持原值
      row.totalReportNum = rowTotalReportNums.value[orderId] || 0
    } else {
      // 更新该行的累计值
      rowTotalReportNums.value[orderId] = (rowTotalReportNums.value[orderId] || 0) + change

      // 将当前累计值写入 row.totalReportNum
      row.totalReportNum = rowTotalReportNums.value[orderId]
    }

    // 更新原始值记录
    originalReportNums.value[orderId] = numVal
  }
}

// 监听滚动事件，提前加载数据
const handleScroll = debounce(() => {
  if (!mobile.value || isLoading.value) return

  const container = document.querySelector('.el-card__body')
  if (container) {
    const { scrollTop, scrollHeight, clientHeight } = container
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight

    // 当滚动到80%时提前加载
    if (scrollPercentage > 0.8 && hasMoreData.value) {
      loadMoreData()
    }
  }
}, 200)

// 生命周期钩子
onMounted(() => {
  // 只在移动端监听屏幕旋转
  if (mobile.value) {
    window.addEventListener('orientationchange', handleOrientationChange)
    const container = document.querySelector('.el-card__body')
    if (container) {
      container.addEventListener('scroll', handleScroll)
    }
  }
})

onUnmounted(() => {
  if (mobile.value) {
    window.removeEventListener('orientationchange', handleOrientationChange)
    const container = document.querySelector('.el-card__body')
    if (container) {
      container.removeEventListener('scroll', handleScroll)
    }
  }
  // 清空数据（组件卸载或重置时）
  renderedBodyData.value = []
  currentIndex.value = 0
})
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 16px;
}

// 自动完成组件样式
.autocomplete-item {
  .main-text {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .sub-text {
    font-size: 12px;
    color: #909399;
    line-height: 1.2;
  }
}

.no-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 8px 0;
}

.time-range-row {
  display: flex;
  align-items: center;

  .time-select {
    margin: 0 4px;
  }

  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}

.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  height: 40px;
  .card-title-actions {
    display: flex;
    align-items: center;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .mobile-body-form :deep(.el-form-item__label) {
    text-align: right !important;
    width: 80px !important;
    display: inline-block;
  }

  :deep(.el-form-item__content .el-input-group) {
    margin-top: 5px !important;
  }
  .mobile-body-info {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-top: 10px;

    .info-row {
      display: flex;
      margin-bottom: 8px;
      font-size: 13px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-label {
      width: 80px;
      font-weight: 500;
      color: #606266;
      text-align: right;
      font-size: 13px;
      margin-left: -25px;
    }

    .info-value {
      flex: 1;
      color: #000000;
      word-break: break-all;
      margin-left: 12px;
      font-size: 14px;
    }
  }
}

:deep(.el-form-item__label) {
  font-size: 12px !important;
}

:deep(.el-card__body),
:deep(.el-card__header) {
  padding: 2px !important;
}

:deep(.el-scrollbar__thumb) {
  background-color: #000000 !important;
}

:deep(.el-form-item) {
  margin-bottom: 2px !important;
}

:deep(.el-table__cell) {
  padding: 1px 0 !important;

  .cell {
    padding: 1px 0 !important;
  }

  .el-select__wrapper {
    box-shadow: none !important;
  }
}

.custom-card :deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.custom-card) {
  transform: translateY(-17px);
}

:deep(.el-input__prefix) {
  display: none !important;
}

:deep(.el-select__prefix) {
  display: none !important;
}

/* 新增针对数字输入框的样式 */
:deep(.no-spin-input) {
  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
    /* Firefox */
  }
}

.menuDiv {
  display: none;
  position: absolute;

  .menuUl {
    height: auto;
    width: auto;
    font-size: 14px;
    text-align: left;
    border-radius: 4px;
    border: 1px solid #dadada;
    /* 浅灰色细边框 */
    background-color: #f5f5f5;
    color: #333333;
    list-style: none;
    padding: 0 10px;

    li {
      width: 140px;
      height: 35px;
      line-height: 35px;
      cursor: pointer;
      border-bottom: 1px solid #eaeaea;
      /* 分隔线 */

      &:hover {
        background-color: #e6f7ff;
        /* 浅蓝渐变高亮 */
        color: #2c3e50;
        /* 深蓝色字体 */
      }
    }
  }
}
</style>

<style lang="scss">
// 移动端弹窗样式 - 屏幕旋转优化
@media screen and (max-width: 768px) {
  .el-dialog {
    margin: 0 !important;
    height: 100vh;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
    // 修复屏幕旋转后的显示问题
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    // iOS Safari兼容
    @supports (-webkit-touch-callout: none) {
      height: -webkit-fill-available;
    }
  }
  .el-dialog__body {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
  }

  .el-dialog__header {
    padding: 10px;
    margin-right: 0;
  }

  .el-dialog__footer {
    padding: 10px;
    border-top: 1px solid #dcdfe6;
  }

  .el-input__wrapper {
    box-shadow: none !important;
    padding: 0px 0px !important;
  }

  .el-select__wrapper {
    box-shadow: none !important;
    margin-left: -10px;
  }

  .el-form-item {
    box-shadow: 0 2px 6px rgba(64, 64, 65, 0.1); // 更淡的蓝紫色阴影
    transition: box-shadow 0.2s ease; // 缩短过渡时间
    margin-bottom: 0px !important;
    height: 40px !important;
  }

  .el-form-item__label,
  .el-form-item__content {
    height: 40px !important;
    align-items: center !important;
    line-height: 40px !important;
  }
}

// 全局样式，用于自动完成下拉框
.production-order-autocomplete,
.sales-order-autocomplete {
  .el-autocomplete-suggestion__list {
    max-height: 200px;
    overflow-y: auto;
  }

  .el-autocomplete-suggestion__item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
      background-color: #f5f7fa;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

// 快速录入对话框样式
.quick-entry-container {
  .el-table {
    margin-top: 8px;
  }

  .el-form-item {
    margin-bottom: 0;
  }

  .el-checkbox {
    font-weight: 500;
  }
}
.el-input-group__append {
  padding: 0 5px !important;
}
</style>
