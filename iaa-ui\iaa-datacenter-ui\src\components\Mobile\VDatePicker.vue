<template>
  <div class="p-10px">
    <van-field
      :model-value="display"
      :label="label"
      is-link
      readonly
      :placeholder="placeholder"
      @click="!readonly && (show = true)" 
      :clearable="!readonly"
      @clear="onClear"
    />
    <!-- 只读时不显示弹窗 -->
    <van-popup v-model:show="show" round position="bottom" v-if="!readonly">
      <van-date-picker :model-value="dateArray" title="选择日期" @confirm="onConfirm" @cancel="show = false" />
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'

const props = withDefaults(defineProps<{
  modelValue?: string
  label?: string
  placeholder?: string
  readonly?: boolean // 添加只读属性
}>(), {
  modelValue: '',
  label: '收款日期',
  placeholder: '选择日期',
  readonly: false // 默认非只读
})

const emits = defineEmits(['update:modelValue'])
const show = ref(false)

const display = computed(() => props.modelValue || '')
const dateArray = computed(() => {
  if (!props.modelValue) return []
  const d = dayjs(props.modelValue)
  return [d.year().toString(), (d.month() + 1).toString().padStart(2, '0'), d.date().toString().padStart(2, '0')]
})

const onConfirm = ({ selectedValues }) => {
  const [y, m, d] = selectedValues
  emits('update:modelValue', `${y}-${m}-${d}`)
  show.value = false
}

const onClear = () => emits('update:modelValue', '')
</script>

<style scoped>
</style>

