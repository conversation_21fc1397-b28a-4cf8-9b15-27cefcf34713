import { getIntDictOptions, getStrDictOptions } from '@/utils/dict'

export const textFilterRender = reactive({
  name: 'TextFilter'
})

export const userFilterRender = reactive({
  name: 'UserFilter'
})

export const dateRangeFilterRender = reactive({
  name: 'DateRangeFilter'
})
export const numberFilterRender = reactive({
  name: 'NumberFilter'
})

export const userOrDeptFilterRender = reactive({
  name: 'UserOrDeptFilter',
  props: {
    default: 'dept'
  }
})

export const productsModelOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('products_model')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const operationModeOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('operation_mode')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const chassisColorOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('chassis_color')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const mainModelOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('main_model')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const standardTypeOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('standard_type')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const productsTypeOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('products_type')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const sceneTypeOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('scene_type')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const technologyTypeOptions: any = computed<any[]>(() => {
  return getStrDictOptions('technology_type')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const lifeCycleOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('life_cycle')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const customizedNeutralOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('customized_neutral')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const productsGroupOptions: any = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('products_group')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const erpOrg = [
  { label: '驰扬', value: '1002211090000331' },
  { label: '程豪', value: '1002211090000570' },
  { label: '檬檬', value: '1002211090000820' },
  { label: '菊子', value: '1002211090001059' },
  { label: '书香', value: '1002211090001299' },
  { label: '千逸', value: '1002211090001538' }
]

export const erpRecPaymentProperty = [
  { label: '标准', value: '0' },
  { label: '暂收款', value: '2' },
  { label: '预收款', value: '3' },
  { label: '杂项', value: '6' }
]

export const erpCurrency = [
  { label: '人民币元', value: '1' },
  { label: '新加坡元', value: '2' },
  { label: '港元', value: '3' },
  { label: '澳大利亚元', value: '4' },
  { label: '欧元', value: '5' },
  { label: '印度卢比', value: '6' },
  { label: '英镑', value: '7' },
  { label: '法郎', value: '8' },
  { label: '美元', value: '9' },
  { label: '加元', value: '10' },
  { label: '迪拉姆', value: '1002304040074343' },
  { label: '墨西哥比索', value: '1002305031527223' },
  { label: '沙特里亚尔', value: '1002307071273690' },
  { label: '土耳其里拉', value: '1002307250146858' },
  { label: '巴西雷亚尔', value: '1002405311720524' }
]
