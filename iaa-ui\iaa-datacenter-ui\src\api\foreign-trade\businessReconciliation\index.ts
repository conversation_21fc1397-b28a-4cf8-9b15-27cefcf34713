import request from '@/config/axios'

// 收款信息 API
export const BusinessReconciliationApi = {
  // 查询当前业务员对应的客户列表
  getCustomers: async (params: any) => {
    return await request.get({
      url: `/collection/reconciliation/getCustomers?customersName=${params}`
    })
  },

  getOrderReceivablePage: async (params: any) => {
    return await request.post({
      url: `/collection/reconciliation/getOrderReceivablePage`,
      data: params
    })
  },
  getExpenseReceivablePage: async (params: any) => {
    return await request.post({
      url: `/collection/reconciliation/getExpenseReceivablePage`,
      data: params
    })
  },
  getTotalAmount: async (params: any) => {
    return await request.post({
      url: `/collection/reconciliation/getTotalAmount`,
      data: params
    })
  }
}
