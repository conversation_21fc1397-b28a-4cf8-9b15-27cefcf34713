<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="50%">
      <el-descriptions title="生产日报表日志详情" :column="1" border>
          <el-descriptions-item label="操作内容">
        <div v-for="(item, index) in formattedOperationContent" :key="index">{{ item }}</div>
      </el-descriptions-item>
    <el-descriptions-item label="操作类型">{{ formData.operationType }}</el-descriptions-item>
    <el-descriptions-item label="操作人">{{ formData.operationName }}</el-descriptions-item>
    <el-descriptions-item label="操作时间">{{ formatToDateTime(formData.createTime) }}</el-descriptions-item>
  </el-descriptions>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DayLogApi } from '@/api/report/technology/productionlog/index'
import { any } from 'vue-types';
import { formatToDateTime } from '@/utils/dateUtil'
 

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('日报详情') // 弹窗的标题
const formData = ref({
  id: undefined,
  operationContent: '',
  operationName: undefined,
  operationType: undefined,
  createTime: undefined,
})
const formLoading = ref(false) //加载中

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DayLogApi.getDayLog(id)
    } finally {
      formLoading.value = false
    }
  }
}

// 定义一个计算属性来处理操作内容字符串
const formattedOperationContent = computed(() => {
  if (!formData.value.operationContent) return '';
  return formData.value.operationContent.split('; ').map(item => item.trim());
})
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
/* 使用 ::v-deep 来覆盖 el-descriptions-item 的默认样式 */
::v-deep(.el-descriptions__label) {
  width: 100px !important; /* 设置标签的固定宽度 */
  text-align: right; /* 标签文本右对齐 */
  padding-right: 12px; /* 标签右侧内边距 */
}

/* 可选：调整 el-descriptions-item 的内容部分样式 */
::v-deep(.el-descriptions__content) {
  word-break: break-all; /* 内容过长时换行显示 */
}
</style>