<template>
  <div class="pdf-preview">
    <div class="pdf-content" ref="contentRef" @wheel="handleWheel">
      <div
        class="pdf-wrapper"
        :style="pdfWrapperStyle"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
      >
        <vue-pdf-embed
          v-if="state.source"
          annotation-layer
          text-layer
          :source="state.source"
          :page="state.pageNum"
          :rotation="state.rotation"
          :scale="5"
          class="vue-pdf-embed"
          @loaded="handlePdfLoaded"
        />
      </div>
    </div>
    <div class="page_tool">
      <div class="page_tool-item" @click="lastPage">上一页</div>
      <div class="page_tool-item" @click="nextPage">下一页</div>
      <div class="page_tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page_tool-item" @click="pageZoomIn">放大</div>
      <div class="page_tool-item" @click="pageZoomOut">缩小</div>
      <div class="page_tool-item" @click="pageRotation">旋转</div>
      <div class="page_tool-item" @click="resetView">重置</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import axios from 'axios'
import VuePdfEmbed from 'vue-pdf-embed'
import 'vue-pdf-embed/dist/styles/annotationLayer.css'
import 'vue-pdf-embed/dist/styles/textLayer.css'

const props = defineProps({
  pdfUrl: String
})

const contentRef = ref<HTMLElement | null>(null)

const state = ref({
  source: undefined as any,
  pageNum: 1,
  scale: 3,
  numPages: 0,
  rotation: 0
})

// 拖拽和位置状态
const viewState = ref({
  isDragging: false,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0,
  startOffsetX: 0,
  startOffsetY: 0
})

// PDF包装器样式 - 控制缩放和位置
const pdfWrapperStyle = computed(() => ({
  transform: `scale(${state.value.scale}) translate(${viewState.value.offsetX}px, ${viewState.value.offsetY}px)`,
  transformOrigin: 'center center',
  transition: viewState.value.isDragging ? 'none' : 'transform 0.2s ease',
  display: 'inline-block',
  cursor: viewState.value.isDragging ? 'grabbing' : 'grab'
}))

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()
  const delta = e.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(0.1, Math.min(5, state.value.scale + delta))
  state.value.scale = newScale
}

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return // 只处理左键

  viewState.value.isDragging = true
  viewState.value.startX = e.clientX
  viewState.value.startY = e.clientY
  viewState.value.startOffsetX = viewState.value.offsetX
  viewState.value.startOffsetY = viewState.value.offsetY

  e.preventDefault()
}

// 鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (!viewState.value.isDragging) return

  e.preventDefault()

  const deltaX = e.clientX - viewState.value.startX
  const deltaY = e.clientY - viewState.value.startY

  viewState.value.offsetX = viewState.value.startOffsetX + deltaX / state.value.scale
  viewState.value.offsetY = viewState.value.startOffsetY + deltaY / state.value.scale
}

// 鼠标释放事件
const handleMouseUp = () => {
  viewState.value.isDragging = false
}

// 重置视图
const resetView = () => {
  state.value.scale = 1
  viewState.value.offsetX = 0
  viewState.value.offsetY = 0
}

const lastPage = () => {
  if (state.value.pageNum > 1) {
    state.value.pageNum -= 1
    resetView()
  }
}

const nextPage = () => {
  if (state.value.pageNum < state.value.numPages) {
    state.value.pageNum += 1
    resetView()
  }
}

const pageZoomOut = () => {
  state.value.scale = Math.max(0.1, state.value.scale - 0.5)
}

const pageZoomIn = () => {
  state.value.scale = Math.min(5, state.value.scale + 0.5)
}

const pageRotation = () => {
  if (state.value.rotation == 270) {
    state.value.rotation = 0
  } else {
    state.value.rotation += 90
  }
  resetView()
}

const handlePdfLoaded = (pdf: { numPages: number }) => {
  state.value.numPages = pdf.numPages
  resetView()
}

watch(
  () => props.pdfUrl,
  async () => {
    if (!props.pdfUrl) return
    try {
      const response = await axios.get(props.pdfUrl, {
        responseType: 'blob'
      })

      const blob = new Blob([response.data], { type: 'application/pdf' })
      state.value.source = {
        url: URL.createObjectURL(blob),
        cMapUrl: '/cmaps/',
        cMapPacked: true
      }
    } catch (error) {
      console.error('加载PDF失败:', error)
    }
  },
  { immediate: true }
)

onUnmounted(() => {
  if (state.value.source) {
    URL.revokeObjectURL(state.value.source)
  }
})
</script>

<style lang="scss" scoped>
.pdf-preview {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  background-color: #e9e9e9;
  overflow: hidden;
}

.pdf-content {
  height: calc(100% - 60px);
  width: 120%;
  overflow: auto;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
}

.pdf-content:active {
  cursor: grabbing;
}

.pdf-wrapper {
  display: inline-block;
  will-change: transform;
}

.vue-pdf-embed {
  text-align: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform-origin: center center;
}

.page_tool {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 15px;
  display: flex;
  align-items: center;
  background: rgba(66, 66, 66, 0.9);
  color: white;
  border-radius: 25px;
  z-index: 100;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.page_tool-item {
  padding: 8px 15px;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.page_tool-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
