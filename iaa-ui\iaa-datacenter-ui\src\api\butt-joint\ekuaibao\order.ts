import request from '@/config/axios'

/** 获取合思单据列 */
export const getTableColumn = async () => {
  return await request.get({ url: '/butt-joint/ekuaibao/order/get-columns' })
}


/** 获取合思单据分页数据 */
export const page = async (params: any) => {
  return await request.get({ url: '/butt-joint/ekuaibao/order/page', params })
}
/** 从合思获取单据数据 */
export const updateOrder = async (orderId:string)=>{
  return await request.get({ url: '/butt-joint/ekuaibao/order/update-order/'+orderId })
}
/** 同步合思单据数据到ERP */
export const syncOrder = async (orderId:string)=>{
  return await request.get({ url: '/butt-joint/ekuaibao/order/sync-order/'+orderId })
}

/** 不同步合思单据数据到ERP */
export const unSyncOrder = async (orderId:string)=>{
  return await request.get({ url: '/butt-joint/ekuaibao/order/un-sync-order/'+orderId })
}

export const downloadOrder = async (code:string)=>{
  return await request.get({url:'/butt-joint/ekuaibao/order/download-order/'+code})
}