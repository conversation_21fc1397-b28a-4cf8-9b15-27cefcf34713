<template>
  <Dialog v-model="dialogVisible" title="添加自定义任务">
    <el-form ref="formRef" label-width="120" :model="formData" :rules="formRules">
      <el-form-item label="业务员">
        <el-input v-model="formData.seller" />
      </el-form-item>
      <el-form-item label="客户编码">
        <el-autocomplete
          v-model="formData.customerCode"
          :fetch-suggestions="
            (queryString: any, cb: any) => getCustomerInfo(queryString, cb, 'code')
          "
          clearable
          @select="(item: any) => handleCustomerSelect(item, 'code')"
          @clear="
            () => {
              formData.customerName = undefined
            }
          "
        />
      </el-form-item>
      <el-form-item label="客户名称">
        <el-autocomplete
          v-model="formData.customerName"
          :fetch-suggestions="
            (queryString: any, cb: any) => getCustomerInfo(queryString, cb, 'name')
          "
          clearable
          @select="(item: any) => handleCustomerSelect(item, 'name')"
          @clear="
            () => {
              formData.customerCode = undefined
            }
          "
        />
      </el-form-item>
      <el-form-item label="定制内容" prop="custom">
        <el-select v-model="formData.custom">
          <el-option
            v-for="dict in getStrDictOptions(`eng_${props.type}_dict`)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务" prop="description">
        <el-input type="textarea" v-model="formData.description" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" plain @click="save" :loading="loading">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { CustomerApi } from '@/api/report/erp/order-bom'
import { getStrDictOptions } from '@/utils/dict'

import { debounce } from 'lodash-es'
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const message = useMessage()
const formData = ref({
  id: undefined,
  seller: undefined,
  custom: undefined,
  customerCode: undefined,
  customerName: undefined,
  description: undefined
})
const formRules = reactive({
  description: [{ required: true, message: '任务不能为空', trigger: 'blur' }],
  custom: [{ required: true, message: '定制内容不能为空', trigger: 'blur' }]
})
const props = defineProps({
  type: propTypes.string.isRequired
})

const emits = defineEmits(['success'])

/** 获取客户信息 */
const getCustomerInfo: any = debounce(async (queryString: string, cb: any, type: string) => {
  let query = {} as any
  query[type] = queryString
  const res = await CustomerApi.getCustomerList(query)
  cb(res.map((item) => ({ value: item.code, label: item.name })))
}, 500)
/** 客户信息选择项 */
const handleCustomerSelect = (item: any, type: string) => {
  if (type === 'code') {
    formData.value.customerName = item.label
  } else {
    formData.value.customerCode = item.value
  }
}

const save = async () => {
  loading.value = true
  try {
    await formRef.value.validate()
    await CustomerApi.saveDetail({
      ...formData.value,
      type: props.type
    })
    refresh()
    message.success('保存成功')
    dialogVisible.value = false
    emits('success')
  } finally {
    loading.value = false
  }
}

const openForm = () => {
  refresh()
  dialogVisible.value = true
}

const refresh = () => {
  formData.value = {
    id: undefined,
    seller: undefined,
    custom: undefined,
    customerCode: undefined,
    customerName: undefined,
    description: undefined
  }
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 10px !important;
}
:deep(.el-form-item__content) {
  width: 100%;
  background-color: #f5f7f9;
  padding: 1px 5px;
}
</style>
