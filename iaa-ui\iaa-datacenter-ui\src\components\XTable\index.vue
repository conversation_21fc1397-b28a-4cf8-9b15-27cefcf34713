<template>
  <div>
    <div class="header-bar">
      <div>
        <slot name="x-table-header"></slot>
      </div>
      <div class="visible-column">
        <el-tooltip content="清空筛选">
          <el-button :icon="Delete" circle size="small" @click="onClearSearch" />
        </el-tooltip>
        <el-tooltip content="刷新">
          <el-button :icon="Refresh" circle size="small" @click="emits('refresh')" />
        </el-tooltip>
        <!-- <el-tooltip content="导出">
          <el-button :icon="Download" circle size="small" />
        </el-tooltip> -->
        <!--显示隐藏列操作按钮-->
        <el-popover trigger="click" placement="bottom-start">
          <template #reference>
            <el-button :icon="Edit" circle size="small" />
          </template>
          <el-checkbox-group
            v-model="visiableColumns"
            size="small"
            @change="onColumnVisiable(visiableColumns)"
          >
            <el-checkbox
              v-for="column in props.columns"
              :key="column.prop"
              :label="column.prop"
              :value="column.prop"
            >
              {{ column.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-popover>
      </div>
    </div>
    <!--  列头查询 -->
    <el-table
      :data="serchData"
      :header-cell-style="{
        fontSize: '12px',
        padding: '4px',
        backgroundColor: '#fcfcfc',
        borderColor: '#ccc',
        color: '#333'
      }"
      :cell-style="{ padding: 0, borderColor: '#ccc' }"
      border
      ref="queryTableRef"
      :key="tableKey"
      class="query-table"
      @header-dragend="onColumnWidthChange"
      @select-all="onSelectAll"
    >
      <el-table-column
        v-if="$slots['selection']"
        width="55"
        fixed="left"
        type="selection"
        align="center"
      />
      <el-table-column
        v-for="column in virtualColumns"
        :key="column.prop"
        :label="column.label"
        :width="column.width == -1 ? 'auto' : column.width"
        :align="column.align"
      >
        <template #default="{ row }">
          <template v-if="!column.noSearch">
            <template v-if="column.dict">
              <el-select
                v-model="row[column.prop]"
                @change="emitsQuery(row)"
                size="small"
                clearable
              >
                <el-option
                  v-for="(dict, dictIndex) in getStrDictOptions(column.dict)"
                  :key="dictIndex"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template v-else>
              <slot
                v-if="$slots[column.prop + '-search']"
                :name="`${column.prop}-search`"
                :row="row"
                :column="column"
              ></slot>

              <el-date-picker
                v-model="row[column.prop]"
                v-else-if="column.date"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="emitsQuery(row)"
                clearable
                size="small"
                :style="`width:${column.width == -1 ? 240 : column.width}px`"
              />
              <el-input
                v-else
                v-model="row[column.prop]"
                @change="emitsQuery(row)"
                size="small"
                :suffix-icon="Search"
              />
            </template>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        v-if="$slots['operation']"
        :width="props.operationWidth"
        fixed="right"
      />
    </el-table>

    <!-- 表格数据 -->
    <el-table
      :data="props.data"
      :show-header="false"
      :cell-style="{ fontSize: '12px', padding: 0, color: 'rgb(51,51,51)' }"
      show-overflow-tooltip
      border
      scrollbar-always-on
      ref="scrollTableRef"
      :height="props.height"
      :stripe="props.stripe"
      :highlight-current-row="props.highlightCurrent"
      @current-change="(row: any, oldRow: any) => emits('current', row)"
      @contextmenu.prevent
      @cell-contextmenu="onContextMenu"
      @selection-change="onSelectChange"
    >
      <el-table-column
        v-if="$slots['selection']"
        width="55"
        fixed="left"
        type="selection"
        align="center"
      />
      <el-table-column
        v-for="(column, index) in virtualColumns"
        :key="index"
        :label="column.label"
        :prop="column.prop"
        :width="column.width == -1 ? 'auto' : column.width"
        :align="column.align"
      >
        <template #default="{ row }">
          <dict-tag :type="column.dict" :value="row[column.prop]" v-if="column.dict" />
          <template v-else>
            <slot v-if="$slots[column.prop]" :name="column.prop" :row="row" :column="column"></slot>
            <template v-else>
              {{ row[column.prop] }}
            </template>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        v-if="$slots['operation']"
        :width="props.operationWidth"
        fixed="right"
      >
        <template #default="{ row }">
          <slot name="operation" :row="row"></slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页数据 -->
    <Pagination
      v-model:limit="pageData.pageSize"
      v-model:page="pageData.pageNo"
      :total="props.total"
      @pagination="emits('pagination', pageData)"
      size="small"
    />

    <right-menu :items="virtualRightMenuItems" :click-x-y="rightClickXY" />
  </div>
</template>

<script lang="ts" setup>
import rightMenu from './RightMenu.vue'
import { getStrDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { Edit, Download, Refresh, Delete, Search } from '@element-plus/icons-vue'

const props = defineProps({
  data: propTypes.oneOfType<any[]>([Array<any>]).isRequired,
  columns: propTypes.oneOfType<TableColumn[]>([Array<TableColumn>]).isRequired,
  page: propTypes.any.isRequired,
  total: propTypes.number.isRequired,
  height: propTypes.oneOfType([String, Number]).def(500),
  excludeQueryColumn: propTypes.array.def([]),
  stripe: propTypes.bool.def(false),
  highlightCurrent: propTypes.bool.def(false),
  rightMenuItems: propTypes.arrayOf<any>(Array<any>).def([]),
  operationWidth: propTypes.number.def(100)
})

const rightClickXY = ref({})
const virtualRightMenuItems = ref<any[]>([])
const tableKey = ref(0)

const forceRerender = () => {
  tableKey.value += 1
}
// 分页参数
const pageData = computed(() => {
  if (serchData.value && serchData.value.length > 0) {
    for (let key in props.page) {
      if (serchData.value[0].hasOwnProperty(key)) {
        serchData.value[0][key] = props.page[key]
      }
    }
  }
  return props.page
})
// 查询参数
const serchData = computed(() => {
  const initialObj: Record<string, string> = props.columns.reduce(
    (acc, item) => {
      acc[item.prop] = ''
      return acc
    },
    {} as Record<string, string>
  )
  return [initialObj]
})
const emits = defineEmits(['pagination', 'search', 'refresh', 'current', 'clrear'])

const queryTableRef = ref<any>()
const scrollTableRef = ref<any>()
const visiableColumns = ref<string[]>([])
const virtualColumns = ref<TableColumn[]>([])

// 列 显示隐藏方法
const onColumnVisiable = (columns: string[]) => {
  virtualColumns.value = []
  for (let column of props.columns) {
    if (columns.includes(column.prop)) {
      virtualColumns.value.push(column)
    }
  }
}

const onColumnWidthChange = (newWidth: number, oldWidth: number, column: any) => {
  const tempColumn = virtualColumns.value.find((item) => item.prop === column.rawColumnKey)
  tempColumn && (tempColumn.width = newWidth)
}

const emitsQuery = (row: any) => {
  let tempRow: any = {}
  for (let key in row) {
    if (props.excludeQueryColumn.includes(key)) continue
    tempRow[key] = row[key]
  }

  emits('search', tempRow)
}

const onClearSearch = () => {
  if (serchData.value && serchData.value.length > 0) {
    for (let key in serchData.value[0]) {
      serchData.value[0][key] = ''
    }
    emits('search', serchData.value[0])
    emits('clrear')
    forceRerender()
  }
}

const onSelectAll = (selection: any[]) => {
  const currentSelection = scrollTableRef.value?.getSelectionRows()
  console.log(selection.length, currentSelection.length, props.data.length)
  if (selection.length > 0 && currentSelection.length != props.data.length) {
    scrollTableRef.value?.toggleAllSelection()
  } else if (selection.length == 0) {
    scrollTableRef.value?.clearSelection()
  }
}

const onSelectChange = (selection: any[]) => {
  const currentSelection = queryTableRef.value?.getSelectionRows()
  if (currentSelection.length == 0 && selection.length == props.data.length) {
    queryTableRef.value?.toggleAllSelection()
  }else if(currentSelection.length == 1 && selection.length != props.data.length){
    scrollTableRef.value?.clearSelection()
  }
}

const getSelectionRows = () => {
  return scrollTableRef.value?.getSelectionRows()
}

defineExpose({
  getSelectionRows
})

const onContextMenu = (row: any, column: any, cell: any, event: any) => {
  rightClickXY.value = {
    position: {
      x: event.clientX,
      y: event.clientY
    }
  }
  virtualRightMenuItems.value = []
  props.rightMenuItems.forEach((item) => {
    virtualRightMenuItems.value.push({ ...item, params: { row, column } })
  })
}

onMounted(() => {
  if (queryTableRef.value && scrollTableRef.value) {
    let firstTable = queryTableRef.value.$el.querySelector('.el-scrollbar__wrap')
    let secondTable = scrollTableRef.value.$el.querySelector('.el-scrollbar__wrap')

    if (firstTable && secondTable) {
      secondTable.addEventListener('scroll', () => {
        firstTable.scrollLeft = secondTable.scrollLeft
      })

      firstTable.addEventListener('scroll', () => {
        secondTable.scrollLeft = firstTable.scrollLeft
      })
    }
  }
})

onBeforeMount(() => {
  if (queryTableRef.value && scrollTableRef.value) {
    let firstTable = queryTableRef.value.$el.querySelector('.el-scrollbar__wrap')
    let secondTable = scrollTableRef.value.$el.querySelector('.el-scrollbar__wrap')

    firstTable.removeEventListener('scroll')
    secondTable.removeEventListener('scroll')
  }
})

watch(
  () => props.columns,
  (newValue) => {
    if (newValue) {
      visiableColumns.value = newValue.map((item) => item.prop)
      onColumnVisiable(visiableColumns.value)
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.query-table {
  :deep(.el-scrollbar__thumb) {
    display: none;
  }

  :deep(tbody .el-checkbox) {
    display: none;
  }
}

:deep(.cell) {
  padding: 0 2px !important;
}

.header-bar {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-table__body tr.current-row > td.el-table__cell) {
  background-color: #fff5b3 !important;
}

:deep(.el-table__cell) {
  .el-select__wrapper,
  .el-input__wrapper {
    box-shadow: none;
  }
}
:deep(.el-table-column--selection > .cell) {
  display: block;
}
</style>
