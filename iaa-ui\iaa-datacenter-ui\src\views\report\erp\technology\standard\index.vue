import { set, remove } from 'lodash-es';
<template>
  <ContentWrap>
    <vxe-toolbar custom ref="toolbarRef" size="mini">
      <template #buttons>
        <el-button
          type="primary"
          plain
          size="small"
          @click="addEvent"
          v-hasPermi="['standard:work:create']"
        >
          新增
        </el-button>
        <el-button
          type="success"
          plain
          size="small"
          @click="saveEvent"
          v-hasPermi="['standard:work:update', 'standard:work:create']"
        >
          保存
        </el-button>
        <el-button
          type="info"
          plain
          size="small"
          @click="showPasteParser = true"
          v-hasPermi="['standard:work:create']"
        >
          批量粘贴解析
        </el-button>

        <el-button
          @click="handleExport"
          type="warning"
          plain
          :loading="exportLoading"
          v-hasPermi="['standard:work:export']"
          style="margin-left: 30px"
          size="small"
          >导出</el-button
        >
      </template>
    </vxe-toolbar>

    <div class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 25, keyField: 'id' }"
        ref="tableRef"
        :data="list"
        :header-cell-style="{ padding: 0 }"
        border
        stripe
        align="center"
        height="100%"
        max-height="100%"
        show-overflow="title"
        :column-config="{ resizable: true }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        :loading="loading"
        :menu-config="menuConfig"
        @menu-click="menuClickEvent"
        :checkbox-config="{ reserve: true, highlight: true, range: true }"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
        :filter-config="{}"
        show-footer
        keep-source
        :footer-cell-style="{ padding: 0, background: '#dcefdc', border: '1px solid #ebeef5' }"
        :mouse-config="{ selected: true }"
        @filter-change="handleFilterChange"
        @keydown.stop="handleKeydown"
        tabindex="0"
        size="mini"
      >
        <vxe-column type="checkbox" min-width="40" field="id" fixed="left" />
        <vxe-column
          field="model"
          title="型号"
          width="120"
          align="left"
          :edit-render="modelEditRender"
          :filters="modelOptions"
          :filter-render="FilterValue.textFilterRender"
          fixed="left"
        >
          <template #edit="{ row }">
            <vxe-select
              v-model="row.model"
              :options="modelOptionsList"
              filterable
              clearable
              @change="updateRowStatus({ row })"
              :disabled="isExistingRecord(row)"
              :loading="modelLoading"
              @focus="loadModelOptions"
            />
          </template>
          <template #default="{ row }">
            <span>{{ row.model }}</span>
          </template>
        </vxe-column>
        <vxe-column
          title="分类"
          field="classification"
          min-width="100"
          :filters="FilterValue.standardTypeOptions"
          :edit-render="{
            name: '$select',
            options: getIntDictOptions(DICT_TYPE.STANDARD_TYPE),
            props: { value: 'value', label: 'label' }
          }"
        >
          <template #default="{ row }">
            <DictTag :type="'standard_type'" :value="row.classification" />
          </template>
        </vxe-column>
        <vxe-column
          title="组装"
          field="assembledWork"
          min-width="100"
          :edit-render="{ name: 'input' }"
          :filters="assembledWorkOptions"
          :filter-render="FilterValue.textFilterRender"
        >
          <template #edit="scope">
            <vxe-input
              type="number"
              v-model="scope.row.assembledWork"
              min="0"
              @input="updateRowStatus(scope)"
              :controls="false"
            />
          </template>
        </vxe-column>
        <vxe-column
          title="包装"
          field="packagingWork"
          min-width="100"
          :edit-render="{ name: 'input' }"
          :filters="packagingWorkOptions"
          :filter-render="FilterValue.textFilterRender"
        >
          <template #edit="scope">
            <vxe-input
              type="number"
              v-model="scope.row.packagingWork"
              min="0"
              @input="updateRowStatus(scope)"
              :controls="false"
            />
          </template>
        </vxe-column>
        <vxe-column
          title="组包"
          field="standardWork"
          min-width="100"
          :edit-render="{ name: 'input' }"
          :filters="standardWorkOptions"
          :filter-render="FilterValue.textFilterRender"
        >
          <template #edit="scope">
            <vxe-input
              type="number"
              v-model="scope.row.standardWork"
              min="0"
              @input="updateRowStatus(scope)"
              :controls="false"
            />
          </template>
        </vxe-column>
        <vxe-column
          title="包膜"
          field="throughMembrane"
          min-width="100"
          :edit-render="{ name: 'input' }"
          :filters="throughMembraneOptions"
          :filter-render="FilterValue.textFilterRender"
        >
          <template #edit="scope">
            <vxe-input
              type="number"
              v-model="scope.row.throughMembrane"
              min="0"
              @input="updateRowStatus(scope)"
              :controls="false"
            />
          </template>
        </vxe-column>
        <vxe-column
          title="组包膜"
          field="capsule"
          min-width="100"
          :edit-render="{ name: 'input' }"
          :filters="capsuleOptions"
          :filter-render="FilterValue.textFilterRender"
        >
          <template #edit="scope">
            <vxe-input
              type="number"
              v-model="scope.row.capsule"
              @input="updateRowStatus(scope)"
              min="0"
              :controls="false"
            />
          </template>
        </vxe-column>

        <vxe-column
          title="备注"
          field="remark"
          min-width="100"
          :edit-render="{ name: 'input' }"
          :filters="remarkOptions"
          :filter-render="FilterValue.textFilterRender"
        >
          <template #edit="scope">
            <vxe-input type="text" v-model="scope.row.remark" @input="updateRowStatus(scope)" />
          </template>
        </vxe-column>

        <vxe-column
          title="创建人"
          min-width="100"
          field="creator"
          :filters="createUserOptions"
          :filter-render="FilterValue.userFilterRender"
        >
          <template #default="{ row }">
            {{ getUserNickName(row.creator) }}
          </template>
        </vxe-column>

        <vxe-column field="createTime" min-width="140" title="创建时间">
          <template #default="{ row }">
            {{ row.createTime && formatToDateTime(row.createTime) }}
          </template>
        </vxe-column>

        <vxe-column
          title="修改人"
          min-width="100"
          field="updater"
          :filters="updateUserOptions"
          :filter-render="FilterValue.userFilterRender"
        >
          <template #default="{ row }">
            {{ getUserNickName(row.updater) }}
          </template>
        </vxe-column>

        <vxe-column field="updateTime" min-width="140" title="修改时间">
          <template #default="{ row }">
            {{ row.updateTime && formatToDateTime(row.updateTime) }}
          </template>
        </vxe-column>

        <vxe-column title="操作" min-width="80" fixed="right">
          <template #default="{ row }">
            <el-button
              @click="handleDelete(row.id)"
              link
              type="danger"
              v-hasPermi="['standard:work:delete']"
              >删除</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 批量删除确认对话框 -->
  <Dialog title="批量删除确认" v-model="batchDeleteVisible">
    <div>当前选中：{{ selectionData.length }} 条数据，确认要删除吗？</div>
    <template #footer>
      <el-button type="danger" @click="confirmBatchDelete()">确认删除</el-button>
      <el-button @click="batchDeleteVisible = false">取消</el-button>
    </template>
  </Dialog>
  <!-- 粘贴解析器弹窗 -->
  <Dialog
    title="数据批量粘贴解析"
    v-model="showPasteParser"
    width="90%"
    :before-close="() => (showPasteParser = false)"
  >
    <PasteParser
      :target-fields="targetFields"
      :mappings="mappings"
      @data-parsed="handleParsedData"
    />
  </Dialog>
</template>
<script lang="ts" setup>
import { formatToDateTime, formatToDate } from '@/utils/dateUtil'
import { StandardApi } from '@/api/report/technology/standard/index'
import download from '@/utils/download'
import PasteParser from './PasteParser.vue'
import { checkPermission } from '@/store/modules/permission'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import * as FilterValue from '@/utils/Filter'
import { onBeforeRouteLeave } from 'vue-router'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ElMessage, ElMessageBox } from 'element-plus'

const modelOptions = ref([{ data: '' }])
const assembledWorkOptions = ref([{ data: '' }])
const packagingWorkOptions = ref([{ data: '' }])
const standardWorkOptions = ref([{ data: '' }])
const throughMembraneOptions = ref([{ data: '' }])
const capsuleOptions = ref([{ data: '' }])
const remarkOptions = ref([{ data: '' }])
const createUserOptions = ref([{ data: {} }])
const updateUserOptions = ref([{ data: {} }])

const userList = ref<UserVO[]>([])

const modelOptionsList = ref<any[]>([])
const modelLoading = ref(false)
// 添加型号编辑渲染配置
const modelEditRender = reactive({
  name: '$select',
  props: {
    filterable: true,
    clearable: true,
    options: modelOptionsList.value
  }
})

// 加载型号选项列表
const loadModelOptions = async () => {
  // 如果已经有数据则不重复加载
  if (modelOptionsList.value.length > 0) return
  
  try {
    modelLoading.value = true
    // 调用API获取所有型号选项
    // 注意：你需要根据实际的API接口调整这里的调用方法
    const models = await StandardApi.getAllModels() // 需要实现此方法
    console.log('获取到的型号列表:', models)
    // 转换为选项格式
    modelOptionsList.value = models.map(model => ({
      value: model,
      label: model
    }))
  } catch (error) {
    console.error('加载型号选项失败:', error)
    message.error('加载型号选项失败')
  } finally {
    modelLoading.value = false
  }
}

const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 30,
  model: undefined,
  classification: undefined,
  assembledWork: undefined,
  packagingWork: undefined,
  standardWork: undefined,
  throughMembrane: undefined,
  capsule: undefined,
  remark: undefined,
  createUser: undefined,
  updateUser: undefined,
  createTime: [],
  updateTime: []
})

const onListUser = async () => {
  userList.value = await getSimpleUserList()
}
/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname)
    .join(',')
}
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

//筛选时间
const handleFilterChange = (params: any) => {
  console.log(params)

  // 特定字段列表
  const specialFields = ['classification']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}
// 目标字段选项
const targetFields = [
  { label: '型号', value: 'model' },
  { label: '分类', value: 'classification' },
  { label: '组装标准工时(分)', value: 'assembledWork' },
  { label: '包装标准工时（分钟/台）', value: 'packagingWork' },
  { label: '标准工时（组装+包装）', value: 'standardWork' },
  { label: '过膜', value: 'throughMembrane' },
  { label: '组包膜', value: 'capsule' },
  { label: '备注', value: 'remark' }
]
// 根据示例数据和位置推断字段类型
const mappings = [
  'model',
  'classification',
  'assembledWork',
  'packagingWork',
  'standardWork',
  'throughMembrane',
  'capsule',
  'remark'
]

const disabledModel = ref(true)
const updateRowStatus = (params: any) => {
  const $table = tableRef.value
  if ($table) {
    return $table.updateStatus(params)
  }
}

const addEvent = async () => {
  const $table = tableRef.value
  if ($table) {
    const { row: newRow } = await $table.insert()
    // 激活不自动聚焦
    $table.setEditRow(newRow)
        // 加载型号选项
    await loadModelOptions()
  }
}

const saveEvent = async () => {
  const $table = tableRef.value
  if ($table) {
    const updateRecords = $table.getUpdateRecords()
    const insertRecords = $table.getInsertRecords()
    const rawRecords = updateRecords.map((record) => toRaw(record))
    // 过滤掉 id 字段
    const rawInsertRecords = insertRecords.map((record) => {
      const rawRecord = toRaw(record)
      const { id, ...rest } = rawRecord
      return rest
    })

    // 校验新增记录的必填字段
    if (rawInsertRecords.length > 0) {
      const invalidRecord = rawInsertRecords.find(
        (record) => !record.model || record.model.trim() === ''
      )

      if (invalidRecord) {
        message.error('型号和分类为必填项，请填写完整')
        return
      }
    }

    // 校验更新记录的必填字段
    if (rawRecords.length > 0) {
      const invalidRecord = rawRecords.find((record) => !record.model || record.model.trim() === '')

      if (invalidRecord) {
        message.error('型号为必填项，请填写完整')
        return
      }
    }

    // 修改此处逻辑，让新增和更新可以同时进行
    if (rawRecords.length > 0 || rawInsertRecords.length > 0) {
      try {
        loading.value = true
        const promises: Promise<any>[] = []

        // 如果有更新记录，添加更新操作到待执行队列
        if (rawRecords.length > 0) {
          if (!checkPermission(['standard:work:update'])) {
            message.error('您没有修改数据的权限，请联系管理员！')
            await getList()
            return
          }
          promises.push(StandardApi.updateBatch(rawRecords))
        }

        // 如果有新增记录，添加新增操作到待执行队列
        if (rawInsertRecords.length > 0) {
          if (!checkPermission(['standard:work:create'])) {
            message.error('您没有新增数据的权限，请联系管理员！')
            await getList()
            return
          }
          promises.push(StandardApi.createStandard(rawInsertRecords))
        }

        // 并行执行所有操作
        await Promise.all(promises)

        loading.value = false
        // 等待数据更新完成后再刷新列表
        await getList()
        message.success('保存成功')
      } finally {
        loading.value = false
      }
    } else {
      message.info('没有更新数据')
    }
  }
}

// 判断是否为已有记录（非新增记录）
const isExistingRecord = (row: any) => {
  // 如果行数据有 id 且 id 是数字类型，则为已有记录
  return row.id && typeof row.id === 'number'
}
/** 处理粘贴解析的数据 */
const handleParsedData = async (parsedItems: any[]) => {
  console.log('parsedItems', parsedItems)

  const res = await StandardApi.batchCreate(parsedItems)
  // 处理返回的提示信息数组
  if (Array.isArray(res)) {
    // 直接用 <br> 连接数组元素
    const messageText = res.map((item) => item.replace(/;\s*$/, '')).join('<br>')
    ElMessageBox.alert(messageText, '提示', {
      dangerouslyUseHTMLString: true // 开启 HTML 解析
    })
  }
  // 关闭粘贴解析器弹窗
  showPasteParser.value = false
  getList()
}

const showPasteParser = ref(false)
const tableRef = ref()
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await StandardApi.getStandardPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 右键菜单 */
const menuConfig = reactive<any>({
  body: {
    options: [[{ code: 'batchDelete', name: '批量删除' }]]
  }
})
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 判断是否为临时新增的数据（前端生成的ID）
    const $table = tableRef.value
    if (typeof id === 'string') {
      // 临时新增数据，直接从表格中移除，不调用后端接口
      // 直接根据 id 查找并删除行
      const insertRecords = $table.getInsertRecords()
      const rowToRemove = insertRecords.find((record) => record.id === id)
      if (rowToRemove) {
        await $table.remove(rowToRemove)
        message.success('删除成功')
      }
      return
    }
    // 发起删除
    await StandardApi.deleteStandard([id])
    message.success('删除成功')
    await getList()
  } catch {}
}

// 批量删除相关
const batchDeleteVisible = ref(false)
const selectionData = ref<any[]>([])
// 右键菜单点击事件
const menuClickEvent = ({ menu }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'batchDelete':
      const rows = $table.getCheckboxRecords()
      if (rows.length === 0) {
        message.alertError('请选择要删除的数据')
        selectionData.value = []
        return
      }
      selectionData.value = rows
      batchDeleteVisible.value = true
      break
  }
}

// 批量删除确认
const confirmBatchDelete = async () => {
  loading.value = true
  try {
    if (!checkPermission(['standard:work:delete'])) {
      message.error('您没有删除数据的权限，请联系管理员！')
      return
    } else {
      const $table = tableRef.value
      if (!$table) return
      // 分离临时新增的数据和已保存的数据
      const tempIds: string[] = [] // 临时新增数据的ID（字符串类型）
      const savedIds: number[] = [] // 已保存数据的ID（数字类型）
      selectionData.value.forEach((item) => {
        if (typeof item.id === 'string') {
          tempIds.push(item.id)
        } else {
          savedIds.push(item.id)
        }
      })

      // 删除临时新增的数据
      if (tempIds.length > 0) {
        const insertRecords = $table.getInsertRecords()
        const rowsToRemove = insertRecords.filter((record) => tempIds.includes(record.id))
        if (rowsToRemove.length > 0) {
          await $table.remove(rowsToRemove)
        }
      }
      // 删除已保存的数据
      if (savedIds.length > 0) {
        await StandardApi.deleteStandard(savedIds)
      }

      batchDeleteVisible.value = false
      selectionData.value = []
      await getList()
      message.success('批量删除成功')
    }
  } catch {
    message.error('批量删除失败')
  } finally {
    loading.value = false
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await StandardApi.exportStandard(queryParams)
    download.excel(data, '标准工时录入.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 添加可编辑列的字段列表（按顺序）
const editableFields = [
  'model',
  'classification',
  'assembledWork',
  'packagingWork',
  'standardWork',
  'throughMembrane',
  'capsule',
  'remark'
]
// 键盘事件处理（增强版）
const handleKeydown = (event) => {
  const $table = tableRef.value
  if (!$table) return

  // 只在编辑状态下处理方向键
  const activeRecord = $table.getActiveRecord()
  if (!activeRecord) return

  const { row, column } = activeRecord
  const rowIndex = list.value.findIndex((item) => item.id === row.id)

  // 只处理特定的按键
  if (!['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Enter', 'Tab'].includes(event.key)) {
    return
  }

  switch (event.key) {
    case 'ArrowLeft':
      moveEditCell($table, row, column, 'left')
      event.preventDefault()
      break
    case 'Tab': // Shift+Tab 向左
      if (event.shiftKey) {
        moveEditCell($table, row, column, 'left')
      } else {
        // Tab 向右
        moveEditCell($table, row, column, 'right')
      }
      event.preventDefault()
      break
    case 'ArrowRight':
      moveEditCell($table, row, column, 'right')
      event.preventDefault()
      break
    case 'ArrowUp':
      moveEditCell($table, row, column, 'up', rowIndex)
      event.preventDefault()
      break
    case 'ArrowDown':
    case 'Enter':
      moveEditCell($table, row, column, 'down', rowIndex)
      event.preventDefault()
      break
  }
}

// 增强版移动函数
const moveEditCell = ($table, currentRow, currentColumn, direction, rowIndex = -1) => {
  try {
    let targetRow = currentRow
    let targetColumn = currentColumn

    const currentIndex = editableFields.indexOf(currentColumn.field)

    switch (direction) {
      case 'left':
        // 向左移动到前一个可编辑列
        if (currentIndex > 0) {
          const prevField = editableFields[currentIndex - 1]
          targetColumn = $table.getColumnByField(prevField)
        } else {
          // 如果是第一个可编辑列，移动到上一行的最后一个可编辑列
          if (rowIndex > 0) {
            targetRow = list.value[rowIndex - 1]
            targetColumn = $table.getColumnByField(editableFields[editableFields.length - 1])
          } else {
            return // 已经是第一行第一列，无法继续向左
          }
        }
        break

      case 'right':
        // 向右移动到后一个可编辑列
        if (currentIndex < editableFields.length - 1) {
          const nextField = editableFields[currentIndex + 1]
          targetColumn = $table.getColumnByField(nextField)
        } else {
          // 如果是最后一个可编辑列，移动到下一行的第一个可编辑列
          if (rowIndex < list.value.length - 1) {
            targetRow = list.value[rowIndex + 1]
            targetColumn = $table.getColumnByField(editableFields[0])
          } else {
            // 可以选择添加新行或保持当前位置
            return
          }
        }
        break

      case 'up':
        // 向上移动到上一行
        if (rowIndex > 0) {
          targetRow = list.value[rowIndex - 1]
        } else {
          return // 已经是第一行，无法继续向上
        }
        break

      case 'down':
        // 向下移动到下一行
        if (rowIndex < list.value.length - 1) {
          targetRow = list.value[rowIndex + 1]
        } else {
          // 可以选择添加新行或保持当前位置
          return
        }
        break
    }

    // 设置新的编辑单元格
    if (targetRow && targetColumn && editableFields.includes(targetColumn.field)) {
      $table.setEditCell(targetRow, targetColumn.field)
    }
  } catch (error) {
    console.warn('移动编辑单元格失败:', error)
  }
}

// 检查是否有未保存的修改
const hasUnsavedChanges = () => {
  //判断是否有权限
  if (!checkPermission(['standard:work:update'])) {
    return false
  }
  const $table = tableRef.value
  if ($table) {
    const updateRecords = $table.getUpdateRecords()
    return updateRecords.length > 0
  }
  return false
}

/** 初始化 **/
onMounted(() => {
  onListUser()
  getList()
  // 确保表格可以获取焦点
  nextTick(() => {
    if (tableRef.value) {
      tableRef.value.$el.setAttribute('tabindex', '0')
    }
  })
})

onBeforeRouteLeave(async (to, from, next) => {
  if (hasUnsavedChanges()) {
    try {
      await ElMessageBox.confirm('您有未保存的修改，是否保存后再离开？', '提示', {
        confirmButtonText: '保存并离开',
        cancelButtonText: '不保存离开',
        type: 'warning',
        distinguishCancelAndClose: true
      })

      // 用户选择保存
      await saveEvent()
      next()
    } catch (action) {
      // 用户选择不保存或者关闭对话框
      if (action === 'cancel') {
        // 不保存离开
        next()
      } else {
        // 关闭对话框，不离开
        next(false)
      }
    }
  } else {
    next()
  }
})
</script>

<style lang="css" scoped>
:deep(.vxe-input){
  height: 24px !important;
}

:deep(.vxe-cell){
  padding: 0px !important;
}
</style>
