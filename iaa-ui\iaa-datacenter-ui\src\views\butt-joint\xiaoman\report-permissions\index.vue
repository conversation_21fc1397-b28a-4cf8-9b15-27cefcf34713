<template>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap>
        <div class="head-container">
          <el-input
            v-model="queryUserParams.nickname"
            class="mb-20px"
            clearable
            placeholder="请输入用户名称"
          >
            <template #prefix>
              <Icon icon="ep:search" />
            </template>
          </el-input>
        </div>
        <div class="head-container">
          <el-tree
            ref="treeRef"
            :data="userList"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :props="userProps"
            :default-expanded-keys="[100]"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
          />
        </div>
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <ContentWrap>
        <el-alert
          type="warning"
          center
          :description="`当前选择用户：${userList.find((el: any) => el.id === xiaomanPermissions.backend_user_id)?.nickname || ''}`"
          :closable="false"
        />
        <el-transfer
          v-if="xiaomanPermissions.backend_user_id"
          v-model="xiaomanPermissions.xiaoman_user_ids"
          :data="xiaomanUserList"
          :props="{ key: 'user_id', label: 'chinese_name' }"
          :titles="['小满用户', '可查看以下用户产生数据']"
          @change="handleChange"
          class="mt-20px"
          filterable
          :filter-method="filterMethod"
        />
        <el-empty v-else> 请选择左侧用户 </el-empty>
      </ContentWrap>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import * as PermissionsApi from '@/api/butt-joint/xiaoman/permissions'
const queryUserParams = ref({
  pageSize: 1000,
  pageNo: 1,
  nickname: ''
})

const message = useMessage()

const userList = ref<any[]>([])
const treeRef = ref()

const xiaomanUserList = ref<any[]>([])

const xiaomanPermissions = ref({
  backend_user_id: undefined,
  xiaoman_user_ids: [] as string[]
})

const onPageUser = async () => {
  const res = await PermissionsApi.pageUser(queryUserParams.value)
  userList.value = res.list
}

const onListXiaomanUser = async () => {
  const res = await PermissionsApi.listXiaomanUser()
  xiaomanUserList.value = res
}

const handleChange = async () => {
  await PermissionsApi.setXiaomanViewAuthority(xiaomanPermissions.value)
  message.success('设定成功')
}

const userProps = {
  children: 'children',
  label: 'nickname',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: false
}

/** 基于名字过滤 */
const filterNode = (name: string, data: any) => {
  if (!name) return true
  return data.nickname.includes(name)
}

/** 处理用户被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  xiaomanPermissions.value.backend_user_id = row.id
  const res = await PermissionsApi.getXiaomanViewAuthorityDO(row.id)
  xiaomanPermissions.value.xiaoman_user_ids = res?.xiaoman_user_ids
}

/** 监听deptName */
watch(
  () => queryUserParams.value.nickname,
  (val) => {
    treeRef.value!.filter(val)
  }
)

const filterMethod = (query, item) => {
  return item.chinese_name.toLowerCase().includes(query.toLowerCase())
}

onMounted(() => {
  onPageUser()
  onListXiaomanUser()
})
</script>

<style lang="scss" scoped>
:deep(.el-transfer) {
  height: calc(100vh - 190px);
}

:deep(.el-transfer-panel__body) {
  height: calc(100vh - 240px);
}

:deep(.el-transfer-panel) {
  width: calc(50% - 82px);
}

:deep(.el-transfer-panel__list) {
  height: calc(100% - 64px);
}
</style>
