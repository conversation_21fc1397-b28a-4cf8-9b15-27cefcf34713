import { store } from '@/store'
import { defineStore } from 'pinia'
import { getAccessToken, removeToken } from '@/utils/auth'
import { checkIntranet } from '@/api/system/permission'
import { CACHE_KEY, useCache, deleteUserCache } from '@/hooks/web/useCache'
import { getInfo, loginOut } from '@/api/login'
import { useWatermark } from '@/hooks/web/useWatermark'

const { wsCache } = useCache()

const { setWatermark, clear } = useWatermark()

interface UserVO {
  id: number
  avatar: string
  nickname: string
  deptId: number
  loginCount: number
}

interface UserInfoVO {
  // USER 缓存
  permissions: string[]
  roles: string[]
  isSetUser: boolean
  user: UserVO
  isIntranet: boolean
  isSetIntranet: boolean
}

export const useUserStore = defineStore('admin-user', {
  state: (): UserInfoVO => ({
    permissions: [],
    roles: [],
    isSetUser: false,
    user: {
      id: 0,
      avatar: '',
      nickname: '',
      deptId: 0,
      loginCount: 0
    },
    isIntranet: false,
    isSetIntranet: false
  }),
  getters: {
    getPermissions(): string[] {
      return this.permissions
    },
    getRoles(): string[] {
      return this.roles
    },
    getIsSetUser(): boolean {
      return this.isSetUser
    },
    getUser(): UserVO {
      return this.user
    },
    getIsSetIntranet(): boolean {
      return this.isSetIntranet
    },
    getIsIntranet(): boolean {
      return this.isIntranet
    }
  },
  actions: {
    async setUserInfoAction() {
      if (!getAccessToken()) {
        this.resetState()
        return null
      }
      let userInfo = wsCache.get(CACHE_KEY.USER)
      if (!userInfo) {
        userInfo = await getInfo()
      }
      this.permissions = userInfo.permissions
      this.roles = userInfo.roles
      this.user = userInfo.user
      this.isSetUser = true
      wsCache.set(CACHE_KEY.USER, userInfo)
      wsCache.set(CACHE_KEY.ROLE_ROUTERS, userInfo.menus)
    },
    async setUserAvatarAction(avatar: string) {
      const userInfo = wsCache.get(CACHE_KEY.USER)
      // NOTE: 是否需要像`setUserInfoAction`一样判断`userInfo != null`
      this.user.avatar = avatar
      userInfo.user.avatar = avatar
      wsCache.set(CACHE_KEY.USER, userInfo)
    },
    async setUserNicknameAction(nickname: string) {
      const userInfo = wsCache.get(CACHE_KEY.USER)
      // NOTE: 是否需要像`setUserInfoAction`一样判断`userInfo != null`
      this.user.nickname = nickname
      userInfo.user.nickname = nickname
      wsCache.set(CACHE_KEY.USER, userInfo)
    },
    async setIntranet() {
      const isIntranet = wsCache.get(CACHE_KEY.IS_INTRANET)
      if (isIntranet !== null && isIntranet !== undefined) {
        this.isIntranet = isIntranet
        this.isSetIntranet = true
      } else {
        const res = await checkIntranet()
        this.isIntranet = res.data
        wsCache.set(CACHE_KEY.IS_INTRANET, res.data, { exp: 60 })
        this.isSetIntranet = true
      }
      if (!this.isIntranet) {
        setWatermark(`IAA国际香氛-${this.user.nickname}`)
      } else {
        clear()
      }
    },
    async loginOut() {
      await loginOut()
      removeToken()
      deleteUserCache() // 删除用户缓存
      this.resetState()
    },
    resetState() {
      this.permissions = []
      this.roles = []
      this.isSetUser = false
      this.user = {
        id: 0,
        avatar: '',
        nickname: '',
        deptId: 0,
        loginCount: 0
      }
    }
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
