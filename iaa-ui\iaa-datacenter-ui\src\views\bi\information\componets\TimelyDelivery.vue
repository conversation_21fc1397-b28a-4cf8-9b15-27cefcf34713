<template>
  <div class="h-[calc(100vh-180px)] overflow-auto">
    <el-form inline class="custom-form" size="small">
      <el-form-item label="日期">
        <el-radio-group v-model="queryParams.dateType" @change="onList">
          <el-radio-button label="年" value="year" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
        <el-date-picker
          class="!w-100px"
          :type="queryParams.dateType"
          v-model="queryParams.date"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="onList"
        />
      </el-form-item>
    </el-form>
    <el-row>
      <el-col
        v-for="dict in getIntDictOptions('information_person')"
        :key="dict.value"
        :span="6"
        :xs="24"
        :sm="24"
        :md="12"
        :lg="6"
        :xl="6"
      >
        <div
          :id="`person-${dict.value}-line`"
          class="h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"
        ></div>
      </el-col>
    </el-row>
    <div class="h-[calc(100%-200px-60px)]">
      <vxe-table
        height="100%"
        align="center"
        border
        :loading="loading"
        :data="dataList"
        show-overflow
        stripe
      >
        <vxe-column title="流程模板" field="flowTemplate" width="200" />
        <vxe-column title="流程编码" field="flowNumber" width="100" />
        <vxe-column title="流程名称" field="flowName" min-width="300" align="left">
          <template #default="{ row }">
            <el-link type="primary" @click="toFlowDetails(row.flowId)">{{ row.flowName }}</el-link>
          </template>
        </vxe-column>
        <vxe-column title="流程发起人" field="flowCreator" width="100" />
        <vxe-column title="流程发起时间" field="flowCreateTime" width="150">
          <template #default="{ row }">
            {{ formatToDateTime(row.flowCreateTime) }}
          </template>
        </vxe-column>
        <vxe-column title="流程期望完成时间" field="flowPlanDate" width="150">
          <template #default="{ row }">
            {{ formatToDate(row.flowPlanDate) }}
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="flowPerson" width="150">
          <template #default="{ row }">
            <el-select
              v-model="row.flowPerson"
              multiple
              v-if="checkPermi(['report:ekp-flow:save-person'])"
              @change="changePerson(row)"
              collapse-tags
            >
              <el-option
                v-for="dict in getIntDictOptions('information_person')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <span v-else>{{
              row.flowPerson.map((item) => getDictLabel('information_person', item)).join(',')
            }}</span>
          </template>
        </vxe-column>
        <vxe-column title="流程结束时间" field="flowPublishTime" width="150">
          <template #default="{ row }">
            {{ row.flowPublishTime ? formatToDateTime(row.flowPublishTime) : '' }}
          </template>
        </vxe-column>
        <vxe-column title="实际信息部处理完成时间" field="developmentDate" width="180">
          <template #default="{ row }">
            <el-date-picker
              v-model="row.developmentDate"
              type="date"
              value-format="YYYY-MM-DD"
              v-if="checkPermi(['report:ekp-flow:save-person'])"
              @change="changePerson(row)"
              class="!w-full"
            />
            <span v-else-if="row.developmentDate || row.flowPublishTime">
              <span v-if="row.developmentDate" class="bg-red text-white">
                {{ formatToDate(row.developmentDate) }}
              </span>
              <span v-else>{{ formatToDateTime(row.flowPublishTime) }}</span>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="流程结束时间超期原因" field="reason" width="200">
          <template #default="{row}">
            <span v-if="row.developmentDate" class="bg-red text-white">
              {{ row.reason }}
            </span>
            <span v-else>无异常</span>
          </template>
        </vxe-column>
        <vxe-column title="流程状态" field="flowStatus" width="150" />
        <vxe-column title="及时性评分" field="timeliness" width="100" />
        <vxe-column title="服务态度评分" field="attitude" width="100" />
        <vxe-column title="系统稳定性评分" field="stability" width="120" />
        <vxe-column title="整体评分" field="whole" width="100" />
      </vxe-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import moment from 'moment'
import { getIntDictOptions, getDictLabel } from '@/utils/dict'
import { FlowApi } from '@/api/report/ekp/flow'
import { formatToDate, formatToDateTime } from '@/utils/dateUtil'
import { checkPermi } from '@/utils/permission'
import * as echarts from 'echarts'
import { markRaw } from 'vue'
import { cloneDeep } from 'lodash-es'

const queryParams = reactive({
  date: moment().format('YYYY-MM-DD'),
  dateType: 'month' as any
})
const loading = ref(false)
const dataList = ref<any[]>([])
const message = useMessage()

// 图表实例存储
const chartInstances = ref<Map<string, any>>(new Map())

// 流程状态枚举
enum FlowStatus {
  NORMAL = '正常',
  DELAYED = '延期',
  UNFINISHED = '未结束'
}

const onList = async () => {
  loading.value = true
  try {
    const res = await FlowApi.getFlowList(queryParams)
    dataList.value = res
    // 数据加载完成后渲染图表
    await nextTick()
    renderCharts()
  } catch (error) {
    console.error('获取流程数据失败:', error)
    message.error('获取流程数据失败')
  } finally {
    loading.value = false
  }
}

const toFlowDetails = (flowId: string) => {
  window.open(
    'http://oa.iaa360.cn:8686/km/review/km_review_main/kmReviewMain.do?method=view&fdId=' + flowId,
    '_blank'
  )
}
// 计算流程完成状态
const calculateFlowStatus = (
  flowPlanDate: string | null,
  flowPublishTime: string | null,
  developmentDate: string | null,
  currentMonth: string
): string => {
  const planDate = moment(flowPlanDate)
  const planMonth = cloneDeep(planDate).startOf('month').format('YYYY-MM')

  // 如果当前处理的月份不是 flowPlanDate 所在月份，直接返回 NORMAL
  if (currentMonth !== planMonth) {
    return FlowStatus.NORMAL
  }

  // 否则，执行原有逻辑
  const publishDate = moment(!developmentDate ? flowPublishTime : developmentDate)

  if (!planDate.isValid() || !publishDate.isValid()) {
    console.error(`无效的日期格式: 计划日期=${flowPlanDate}, 完成日期=${flowPublishTime}`)
    return FlowStatus.UNFINISHED
  }

  const daysDiff = publishDate.diff(planDate, 'days')
  return daysDiff <= 2 ? FlowStatus.NORMAL : FlowStatus.DELAYED
}

// 处理数据，按负责人和月份分组统计
const processDataByPersonAndMonth = () => {
  const result: Record<
    string,
    Record<string, { normal: number; delayed: number; unfinished: number }>
  > = {}

  const persons = getIntDictOptions('information_person')

  // 初始化结果结构
  persons.forEach((person) => {
    result[person.value] = {}
  })

  // 处理每条流程数据
  dataList.value.forEach((flow) => {
    if (!flow.flowPerson || !Array.isArray(flow.flowPerson)) return

    const months = getMonthsForFlow(flow.flowCreateTime, flow.flowPlanDate)

    flow.flowPerson.forEach((personId: number) => {
      const personKey = personId.toString()
      if (!result[personKey]) return

      months.forEach((month) => {
        if (!result[personKey][month]) {
          result[personKey][month] = { normal: 0, delayed: 0, unfinished: 0 }
        }
        // 获取流程状态
        const status = calculateFlowStatus(
          flow.flowPlanDate,
          flow.flowPublishTime,
          flow.developmentDate,
          month
        )

        switch (status) {
          case FlowStatus.NORMAL:
            result[personKey][month].normal++
            break
          case FlowStatus.DELAYED:
            result[personKey][month].delayed++
            break
          case FlowStatus.UNFINISHED:
            result[personKey][month].unfinished++
            break
        }
      })
    })
  })

  return result
}

const getMonthsForFlow = (createTime: string, planDate: string): string[] => {
  const start = moment(formatToDate(createTime)).startOf('month')
  const end = moment(formatToDate(planDate)).startOf('month')

  const result: string[] = []
  let current = start.clone()

  while (current <= end) {
    result.push(current.format('YYYY-MM'))
    current.add(1, 'months')
  }

  return result
}

// 渲染所有图表
const renderCharts = () => {
  const processedData = processDataByPersonAndMonth()
  const persons = getIntDictOptions('information_person')

  persons.forEach((person) => {
    renderPersonChart(person.value.toString(), person.label, processedData[person.value] || {})
  })
}

// 渲染单个负责人的图表
const renderPersonChart = (
  personId: string,
  personName: string,
  monthlyData: Record<string, { normal: number; delayed: number; unfinished: number }>
) => {
  const containerId = `person-${personId}-line`
  const container = document.getElementById(containerId)

  if (!container) {
    console.warn(`Container ${containerId} not found`)
    return
  }

  // 销毁已存在的图表实例
  if (chartInstances.value.has(containerId)) {
    chartInstances.value.get(containerId).dispose()
  }

  // 准备图表数据
  const months = Object.keys(monthlyData).sort()
  const normalData = months.map((month) => monthlyData[month]?.normal || 0)
  const delayedData = months.map((month) => monthlyData[month]?.delayed || 0)
  const unfinishedData = months.map((month) => monthlyData[month]?.unfinished || 0)

  // 如果没有数据，显示空状态
  if (months.length === 0) {
    container.innerHTML = `
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">${personName}</div>
          <div class="text-sm mt-2">暂无数据</div>
        </div>
      </div>
    `
    return
  }

  // 创建ECharts实例
  const chart = markRaw(echarts.init(container))

  const option = {
    title: {
      text: personName,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['正常', '延期', '未结束'],
      bottom: 0,
      textStyle: {
        fontSize: 10
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months.map((month) => moment(month).format('MM月')),
      axisLabel: {
        fontSize: 10,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '正常',
        type: 'bar',
        stack: 'total',
        data: normalData,
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '延期',
        type: 'bar',
        stack: 'total',
        data: delayedData,
        itemStyle: {
          color: '#F56C6C'
        }
      },
      {
        name: '未结束',
        type: 'bar',
        stack: 'total',
        data: unfinishedData,
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }

  chart.setOption(option)
  chartInstances.value.set(containerId, chart)

  // 监听窗口大小变化
  const resizeHandler = () => {
    chart.resize()
  }
  window.addEventListener('resize', resizeHandler)

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler)
    chart.dispose()
  })
}

const changePerson = async (row: any) => {
  await FlowApi.saveFlowPerson({
    flowId: row.flowId,
    flowPerson: row.flowPerson,
    developmentDate: row.developmentDate
  })
  message.success('更新成功')
  // 更新负责人后重新渲染图表
  await nextTick()
  renderCharts()
}

// 清理所有图表实例
const disposeAllCharts = () => {
  chartInstances.value.forEach((chart) => {
    chart.dispose()
  })
  chartInstances.value.clear()
}

// 监听窗口大小变化，调整所有图表
const handleResize = () => {
  chartInstances.value.forEach((chart) => {
    chart.resize()
  })
}

onMounted(() => {
  onList()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  disposeAllCharts()
})
</script>

<style scoped>
/* 图表容器样式 */
[id^='person-'][id$='-line'] {
  position: relative;
  overflow: hidden;
}

/* 空状态样式 */
[id^='person-'][id$='-line'] .flex {
  height: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  [id^='person-'][id$='-line'] {
    height: 180px !important;
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  [id^='person-'][id$='-line'] {
    height: 160px !important;
  }
}
</style>
