<template>
  <ContentWrap>
    <x-table
      :data="dataList"
      :total="total"
      :columns="columns"
      :page="queryParams"
      @search="onSerchPageData"
      @pagination="(page) => onSerchPageData(page, true)"
      stripe
      highlight-current
      v-loading="loading"
      height="calc(100vh - 350px)"
    >
      <template #managementCode="{ row }">
        <el-select v-model="row.managementCode" filterable size="small">
          <el-option
            v-for="item in erpSubject"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </template>
      <template #salesCode="{ row }">
        <el-select v-model="row.salesCode" filterable size="small">
          <el-option
            v-for="item in erpSubject"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </template>
      <template #manufactureCode="{ row }">
        <el-select v-model="row.manufactureCode" filterable size="small">
          <el-option
            v-for="item in erpSubject"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </template>
      <template #developmentCode="{ row }">
        <el-select v-model="row.developmentCode" filterable size="small">
          <el-option
            v-for="item in erpSubject"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </template>
      <template #operation="{ row }">
        <el-button type="primary" size="small" link @click="saveData(row)"> 保存 </el-button>
      </template>
    </x-table>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as SubjectApi from '@/api/butt-joint/ekuaibao/subject'

const dataList = ref<any[]>([])
const total = ref(0)
const columns = ref<TableColumn[]>([])
const erpSubject = ref<any[]>([])
const loading = ref(false)
const message= useMessage()
const { t } = useI18n()
const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})
/** 查询表格数据 */
const onSerchPageData = (row: any, hasPage?: boolean) => {
  for (let key in row) {
    if (row[key]) {
      queryParams.value[key] = row[key]
    } else if (queryParams.value.hasOwnProperty(key)) {
      delete queryParams.value[key]
    }
  }
  if (!hasPage) {
    queryParams.value.pageNo = 1
  }
  onPageData()
}
/** 查询数据 */
const onPageData = async () => {
  loading.value = true
  try {
    const res = await SubjectApi.page(queryParams.value)
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 查询列信息及ERP 科目字典 */
const initSubjectData = async () => {
  const resColumns = await SubjectApi.getTableColumn()
  columns.value = resColumns
  const resErpSubject = await SubjectApi.getErpAllSubject()
  erpSubject.value = resErpSubject
}

const saveData = async (row:any) =>{
  loading.value=true;
  try{
    await SubjectApi.save(row)
    message.success(t('common.updateSuccess'))
  }finally{
    loading.value=false;
  }
}

onMounted(() => {
  initSubjectData()
  onPageData()
})
</script>
