<template>
  <div>
    <div class="mb-10px font-600">客户选择</div>
    <el-select
      v-model="selectedCustomers"
      multiple
      filterable
      remote
      :remote-method="fetchCustomers"
      :loading="loading"
      collapse-tags
      collapse-tags-tooltip
      placeholder="请选择客户"
      style="width: 20%"
    >
      <el-option v-for="item in options" :key="item.code" :label="item.name" :value="item.code" />
    </el-select>

    <div class="flex flex-col justify-start text-1.2rem bg-[var(--el-color-primary-light-9)] rounded-5px p-2px mt-5px">
      <div>本币总应收：{{ totalAmount }};</div>
      <div>核币应收:
      <span v-for="(detail, index) in currencyDetails" :key="index" class="ml-10px">
        {{ detail.currency }}：{{ detail.amount }};
      </span >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BusinessReconciliationApi } from '@/api/foreign-trade/businessReconciliation/index'

const props = defineProps<{ modelValue?: string[] }>()
const emit = defineEmits(['update:modelValue'])

const totalAmount = ref(0)
const currencyDetails = ref<Array<{ currency: string; amount: number }>>([])
const loading = ref(false)
const selectedCustomers = computed({
  get: () => props.modelValue || [],
  set: (val: string[]) => emit('update:modelValue', val)
})

// 获取客户列表
const fetchCustomers = async (query: string = '') => {
  loading.value = true
  try {
    const res = await BusinessReconciliationApi.getCustomers(query)
    options.value = res
  } catch (error) {
    console.error('获取客户列表失败:', error)
    options.value = []
  } finally {
    loading.value = false
  }
}

// 监听 selectedCustomers 变化
watch(
  selectedCustomers,
  async (newVal) => {
    try {
      const res = await BusinessReconciliationApi.getTotalAmount({
        orderCodes: newVal
      })
      totalAmount.value = res.totalAmount
      currencyDetails.value = res.currencyDetails || []
    } catch (error) {
      console.error('获取总应收金额失败:', error)
      totalAmount.value = 0
    }
  },
  { immediate: true }
)

const options = ref<any[]>([])

onMounted(async () => {
  await fetchCustomers()
})
</script>
