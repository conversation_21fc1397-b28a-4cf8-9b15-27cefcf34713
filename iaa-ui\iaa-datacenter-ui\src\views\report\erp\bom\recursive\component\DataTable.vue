<template>
  <x-table
    :data="dataList"
    :columns="forwardTableColumns"
    :total="total"
    :page="forwardQueryParams"
    :exclude-query-column="['units', 'buyer', 'seller', 'customer']"
    :right-menu-items="props.rightMenuItems"
    stripe
    @search="onSerchForwardRecursive"
    @pagination="(page) => onSerchForwardRecursive(page, true)"
    @refresh="handleSearch"
    @clrear="clearTextSearch"
    v-loading="loading"
    height="calc(100vh - 400px)"
  >
    <template #x-table-header>
      <el-input
        v-model="allText"
        size="small"
        placeholder="全域查询"
        style="width: 350px"
        @change="allTextSearch"
      />
    </template>
    <template #units-search>
      <el-select
        size="small"
        v-model="forwardQueryParams['units']"
        clearable
        @change="handleSearch"
        filterable
      >
        <el-option v-for="unit in units" :key="unit.key" :label="unit.value" :value="unit.key" />
      </el-select>
    </template>
    <template #buyer-search>
      <el-select
        size="small"
        v-model="forwardQueryParams['buyer']"
        clearable
        @change="handleSearch"
        filterable
      >
        <el-option
          v-for="buyer in buyers"
          :key="buyer.key"
          :label="buyer.value"
          :value="buyer.key"
        />
      </el-select>
    </template>
    <template #seller-search>
      <el-select
        size="small"
        v-model="forwardQueryParams['seller']"
        clearable
        @change="handleSearch"
        filterable
      >
        <el-option
          v-for="seller in sellers"
          :key="seller.key"
          :label="seller.value"
          :value="seller.key"
        />
      </el-select>
    </template>
    <template #customer-search>
      <el-select
        size="small"
        v-model="forwardQueryParams['customer']"
        clearable
        @change="handleSearch"
        filterable
      >
        <el-option
          v-for="customer in customers"
          :key="customer.key"
          :label="customer.value"
          :value="customer.key"
        />
      </el-select>
    </template>
    <template #actualQty="{ row }">
      <el-popover placement="top" trigger="click" width="400">
        <template #reference>
          <el-button type="primary" link size="small">{{ row.actualQty }}</el-button>
        </template>
        <el-form inline label-position="top" class="custom-form" size="small" label-width="80px">
          <el-form-item label="库存"> {{ row.invQty || 0 }} </el-form-item>
          <el-form-item class="operator">+</el-form-item>
          <el-form-item label="收货未审"> {{ row.rcvQty || 0 }} </el-form-item>
          <el-form-item class="operator">+</el-form-item>
          <el-form-item label="杂收未审"> {{ row.miscRcvQty || 0 }} </el-form-item>
          <el-form-item class="operator">+</el-form-item>
          <el-form-item label="调入单未审"> {{ row.ferInQty || 0 }} </el-form-item>
          <el-form-item class="operator">+</el-form-item>
          <el-form-item label="请购未审"> {{ row.poQty || 0 }} </el-form-item>
          <el-form-item class="operator">+</el-form-item>
          <el-form-item label="在途"> {{ row.poQty || 0 }} </el-form-item>
          <el-form-item class="operator">-</el-form-item>
          <el-form-item label="出货未审"> {{ row.shipQty || 0 }} </el-form-item>
          <el-form-item class="operator">-</el-form-item>
          <el-form-item label="杂发未审"> {{ row.miscShipQty || 0 }} </el-form-item>
          <el-form-item class="operator">-</el-form-item>
          <el-form-item label="工单未发"> {{ row.moQty || 0 }} </el-form-item>
          <el-form-item class="operator">-</el-form-item>
          <el-form-item label="未结订单"> {{ row.soQty || 0 }} </el-form-item>
        </el-form>
      </el-popover>
    </template>
  </x-table>
</template>

<script lang="ts" setup>
import * as RecursiveApi from '@/api/report/erp/bom/recursive'
import { propTypes } from '@/utils/propTypes'
import { cloneDeep } from 'lodash-es'

const props = defineProps({
  type: propTypes.oneOf<string>(['forward', 'reverse', 'so']).isRequired,
  rightMenuItems: propTypes.arrayOf<any>(Array<any>).isRequired
})

const forwardQueryParams = ref({
  pageNo: 1,
  pageSize: 30
})

const dataList = ref<any[]>([])
const units = ref<any[]>([])
const buyers = ref<any[]>([])
const sellers = ref<any[]>([])
const customers = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const allText = ref('')

const forwardTableColumns = ref<TableColumn[]>([])

/** 查询表格列 */
const onTableColumn = async () => {
  if (props.type === 'forward') {
    const res = await RecursiveApi.getForwardTableColumn()
    forwardTableColumns.value = res
  } else if (props.type === 'reverse') {
    const res = await RecursiveApi.getReverseTableColumn()
    forwardTableColumns.value = res
  } else if (props.type === 'so') {
    const res = await RecursiveApi.getSoTableColumn()
    forwardTableColumns.value = res
  } else if (props.type === 'material') {
    const res = await RecursiveApi.getItemTableColumn()
    forwardTableColumns.value = res
  }
}

/** 查询表格数据 */
const onSerchForwardRecursive = (row: any, hasPage?: boolean) => {
  for (let key in row) {
    if (row[key]) {
      forwardQueryParams.value[key] = row[key]
    } else if (forwardQueryParams.value.hasOwnProperty(key)) {
      delete forwardQueryParams.value[key]
    }
  }
  if (!hasPage) {
    forwardQueryParams.value.pageNo = 1
  }
  onForwardRecursivePage()
}

const handleSearch = () => {
  forwardQueryParams.value.pageNo = 1
  onForwardRecursivePage()
}

const allTextSearch = () => {
  forwardQueryParams.value['allText'] = allText
  handleSearch()
}

const clearTextSearch = () => {
  forwardQueryParams.value['allText'] = ''
  delete forwardQueryParams.value['allText']
  handleSearch()
}

/** 分页查询表格数据 */
const onForwardRecursivePage = async () => {
  loading.value = true
  try {
    let res: any = {}
    let query = cloneDeep(forwardQueryParams.value)
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    if (props.type === 'forward') {
      res = await RecursiveApi.getForwardRecursivePage(query)
    } else if (props.type === 'reverse') {
      res = await RecursiveApi.getReverseRecursivePage(query)
    } else if (props.type === 'so') {
      res = await RecursiveApi.getSoRecursivePage(query)
    } else if (props.type === 'material') {
      res = await RecursiveApi.getItemRecursivePage(query)
    }
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const onDictList = async () => {
  units.value = await RecursiveApi.getUnits()
  buyers.value = await RecursiveApi.getOperators(0)
  sellers.value = await RecursiveApi.getOperators(1)
  customers.value = await RecursiveApi.getCustomer()
}

const onShowItem = (key: string, value: string) => {
  forwardQueryParams.value[key] = value
  onForwardRecursivePage()
}

defineExpose({
  onShowItem
})

onMounted(() => {
  onTableColumn()
  onForwardRecursivePage()
  onDictList()
})
</script>

<style lang="scss" scoped>
.custom-form {
  .el-form-item {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    padding: 5px;
    background-color: #f5f7f9;

    border: .3px solid #ebeef5;
    border-radius: 5px;
    :deep(.el-form-item__label){
      margin-bottom: 0 !important;
      padding: 0 !important;
    }

    :deep(.el-form-item__content){
      justify-content: center
    }
  }
  .operator{
    display: flex ;
    width: 20px;

    border: none;
    background-color: transparent;
  }
}
</style>
