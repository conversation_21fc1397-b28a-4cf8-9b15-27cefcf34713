# 生产环境：只在打包时使用
NODE_ENV=production

VITE_DEV=false

# 请求路径
VITE_BASE_URL='https://sj.iaa360.cn:13141'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist-prod

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN=''

# 检验是否内容的请求域名
VITE_APP_INTRANET_URL = 'http://192.168.10.23:13141'