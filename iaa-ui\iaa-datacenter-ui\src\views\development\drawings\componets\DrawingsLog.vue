<template>
  <div class="center-table">
    <div class="h-[calc(100vh-380px)]">
      <div class="h-[calc(100%-5px)]">
        <vxe-table
          ref="tableRef"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :row-config="{ isCurrent: true, height: 25 }"
          :filter-config="{remote:true}"
          show-footer
          keep-source
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          @filter-change="handleFilterChange"
          tabindex="0"
          size="mini"
          :cell-class-name="'cursor-pointer'"
        >
          <vxe-column
            field="itemCode"
            title="物料编码"
            width="150"
            :filters="itemCodeOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column field="itemVersion" title="版本" width="80" />
          <vxe-column
            field="wordName"
            title="文档名称"
            min-width="200"
            :filters="wordNameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column field="wordCode" title="文档编码" width="180" />
          <vxe-column 
          field="type" 
          title="操作类型" 
          width="80"            
          :filters="typeOptions"
            :edit-render="{
              name: '$select',
              options: typeOptions,
              props: { value: 'value', label: 'label' }
            }">
            <template #default="{ row }">
              <el-tag v-if="row.type === 0" type="primary">预览</el-tag>
              <el-tag v-else-if="row.type === 1" type="success">下载</el-tag>
              <el-tag v-else type="danger">未知</el-tag>
            </template>
          </vxe-column>
          <vxe-column field="operationName" title="操作人" width="80" />
          <vxe-column
            field="createTime"
            width="200"
            title="操作时间"
            :filters="claimDate"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          >
        <template #default="{ row }">
          {{formatDate(row.createTime)}}
        </template>
        </vxe-column>
          
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        size="small"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { DrawingApi } from '@/api/development/drawings/index'
import * as FilterTemplate from '@/utils/Filter'
import * as FilterValue from '@/utils/Filter'
import { formatDate } from '@/utils/formatTime'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 30
})
const message = useMessage() // 消息弹窗
// 列表的加载中
const loading = ref(true)
// 中间列表的数据
const list = ref<any[]>([])
// 列表的总页数
const total = ref(0)

const itemCodeOptions = ref([{ data: '' }])
const wordNameOptions = ref([{ data: '' }])
const claimDate = ref([{ data: [] }])


const typeOptions = ref([
  { label: '预览', value: 0 },
  { label: '下载', value: 1 },
])
const getList = async () => {
  try {
    loading.value = true
    const res = await DrawingApi.getDrawingLogPage(queryParams)
    list.value = res.list
    total.value = res.total
  } catch (error) {
    console.log('查询失败:', error)
  } finally {
    loading.value = false
  }
}
const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['type']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

onMounted(() => {
  getList()
})

</script>

<style scoped lang="scss">
.center-table {
  flex: 1;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
</style>
