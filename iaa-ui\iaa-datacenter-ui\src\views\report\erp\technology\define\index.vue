<template>
  <ContentWrap>
    <vxe-toolbar size="mini" ref="toolbarRef" custom>
      <template #buttons>
        <el-button
          type="primary"
          size="small"
          @click="defineFormRef.openForm()"
          v-hasPermi="['report:technology:save-define']"
          >新增</el-button
        >
        <el-button type="success" size="small" @click="onList">查询</el-button>
      </template>
    </vxe-toolbar>
    <div class="h-[calc(100vh-180px)]">
      <vxe-table :row-config="{ height: 30}"
        align="center"
        height="100%"
        ref="tableRef"
        :data="defineList"
        :loading="loading"
        show-overflow
        :header-cell-style="{ padding: 0, height: '30px' }"
        :cell-style="{ padding: 0, height: '30px' }"
      >
        <vxe-column title="编码" field="code" />
        <vxe-column title="名称" field="name" />
        <vxe-column title="备注" field="remarks" />
        <vxe-column title="操作" field="opertions" width="120">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="defineFormRef.openForm(row)"
              v-hasPermi="['report:technology:save-define']"
              >编辑</el-button
            >
            <!-- <el-button
              type="danger"
              link
              size="small"
              v-hasPermi="['report:technology:del-define']"
              @click="deleteDefine(row.id)"
              >删除</el-button
            > -->
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <DefineForm ref="defineFormRef" @success="onList" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import DefineForm from './DefineForm.vue'
import { DefineApi } from '@/api/report/technology/define'

const defineFormRef = ref()
const tableRef = ref()
const toolbarRef = ref()
const loading = ref(false)
const message = useMessage()

const defineList = ref<any[]>([])
const onList = async () => {
  loading.value = true
  try {
    const res = await DefineApi.listDefine()
    defineList.value = res
  } finally {
    loading.value = false
  }
}

const deleteDefine = async (id: number) => {
  loading.value = true
  try {
    await message.confirm('确定删除当前工艺嘛？')
    await DefineApi.deleteDefine(id)
    message.success('删除成功')
    onList()
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  unref(tableRef)?.connect(unref(toolbarRef))
  onList()
})
</script>

<style lang="scss" scoped>
:deep(.vxe-buttons--wrapper) {
  & > * + * {
    margin-left: 10px;
  }
}
</style>
