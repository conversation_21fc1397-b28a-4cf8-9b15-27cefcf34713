<template>
  <ContentWrap>
    <el-tabs v-model="currentTab">
      <el-tab-pane label="物料库存" name="material">
        <data-table
          type="material"
          highlight-current
          ref="materialRef"
          :right-menu-items="materialMenuItems"
        />
      </el-tab-pane>
      <el-tab-pane label="BOM正查" name="forward">
        <data-table
          type="forward"
          highlight-current
          ref="forwardTableRef"
          :rightMenuItems="forwardRightMenuItems"
        />
      </el-tab-pane>
      <el-tab-pane label="BOM反查" name="reverse">
        <data-table
          type="reverse"
          highlight-current
          ref="reverseTableRef"
          :rightMenuItems="reverseRightMenuItems"
        />
      </el-tab-pane>
      <el-tab-pane label="销售订单明细" name="so">
        <data-table
          type="so"
          highlight-current
          ref="soTableRef"
          :right-menu-items="soRightMenuItems"
        />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<script lang="ts" setup>
import DataTable from './component/DataTable.vue'

const currentTab = ref('material')
const forwardTableRef = ref()
const reverseTableRef = ref()
const soTableRef = ref()
const materialRef = ref()

const forwardRightMenuItems = [
  {
    text: '反查BOM',
    fn: (row: any, column: any) => {
      currentTab.value = 'reverse'
      reverseTableRef.value.onShowItem('itemCode', row.itemCode)
    }
  },
  {
    text: '查成品订单',
    fn: (row: any, column: any) => {
      currentTab.value = 'so'
      soTableRef.value.onShowItem('itemCode', row.topCode)
    }
  },
  {
    text: '查物料订单',
    fn: (row: any, column: any) => {
      currentTab.value = 'so'
      soTableRef.value.onShowItem('itemCode', row.itemCode)
    }
  },
  {
    text: '查成品库存',
    fn: (row: any, column: any) => {
      currentTab.value = 'material'
      materialRef.value.onShowItem('itemCode', row.topCode)
    }
  },
  {
    text: '查物料库存',
    fn: (row: any, column: any) => {
      currentTab.value = 'material'
      materialRef.value.onShowItem('itemCode', row.itemCode)
    }
  }
]

const reverseRightMenuItems = [
  {
    text: '正查BOM',
    fn: (row: any, column: any) => {
      currentTab.value = 'forward'
      forwardTableRef.value.onShowItem('topCode', row.topCode)
    }
  },
  {
    text: '查成品订单',
    fn: (row: any, column: any) => {
      currentTab.value = 'so'
      soTableRef.value.onShowItem('itemCode', row.topCode)
    }
  },
  {
    text: '查询物料订单',
    fn: (row: any, column: any) => {
      currentTab.value = 'so'
      soTableRef.value.onShowItem('itemCode', row.itemCode)
    }
  },
  {
    text: '查成品库存',
    fn: (row: any, column: any) => {
      currentTab.value = 'material'
      materialRef.value.onShowItem('itemCode', row.topCode)
    }
  },
  {
    text: '查物料库存',
    fn: (row: any, column: any) => {
      currentTab.value = 'material'
      materialRef.value.onShowItem('itemCode', row.itemCode)
    }
  }
]

const soRightMenuItems = [
  {
    text: '正查BOM',
    fn: (row: any, column: any) => {
      currentTab.value = 'forward'
      forwardTableRef.value.onShowItem('topCode', row.itemCode)
    }
  },
  {
    text: '反查BOM',
    fn: (row: any, column: any) => {
      currentTab.value = 'reverse'
      reverseTableRef.value.onShowItem('itemCode', row.itemCode)
    }
  },
  {
    text: '查库存',
    fn: (row: any, column: any) => {
      currentTab.value = 'material'
      materialRef.value.onShowItem('itemCode', row.itemCode)
    }
  }
]

const materialMenuItems = [
{
    text: '正查BOM',
    fn: (row: any, column: any) => {
      currentTab.value = 'forward'
      forwardTableRef.value.onShowItem('topCode', row.itemCode)
    }
  },
  {
    text: '反查BOM',
    fn: (row: any, column: any) => {
      currentTab.value = 'reverse'
      reverseTableRef.value.onShowItem('itemCode', row.itemCode)
    }
  },
  {
    text: '查订单',
    fn: (row: any, column: any) => {
      currentTab.value = 'so'
      soTableRef.value.onShowItem('itemCode', row.itemCode)
    }
  }
]
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0;
}
</style>
