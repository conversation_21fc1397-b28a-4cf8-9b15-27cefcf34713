<template>
  <el-drawer
    v-model="visible"
    title="历史变更记录"
    :size="700"
    direction="rtl"
    :before-close="handleClose"
    class="history-drawer"
  >
    <div class="drawer-content">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="按回车筛选"
          :prefix-icon="Search"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleSearch"
          class="search-input"
        />
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
          class="date-picker"
        />
      </div>

      <!-- 统计信息 -->
      <div class="stats-bar">
        <el-tag type="info" size="small">
          共 {{ total }} 条记录
        </el-tag>
        <!-- <el-tag v-if="filteredTotal !== total" type="warning" size="small">
          筛选后 {{ filteredTotal }} 条
        </el-tag> -->
      </div>

      <!-- 历史记录列表 -->
      <div 
        class="history-list"
        v-infinite-scroll="loadMore"
        :infinite-scroll-distance="20"
        :infinite-scroll-immediate="false"
        :infinite-scroll-disabled="loading || noMore"
      >
        <div
          v-for="(item, index) in displayList"
          :key="item.id || index"
          class="history-item"
          :class="{ 'highlight': item.highlight }"
        >
          <div class="item-header">
            <div class="operator-info">
              <el-avatar :size="32" class="operator-avatar">
                <Icon icon="ep:user" />
              </el-avatar>
              <div class="operator-details">
                <span class="operator-name">{{ item.userName || '系统' }}</span>
                <!-- <span class="operator-role">{{ item.operatorRole || '管理员' }}</span> -->
              </div>
            </div>
            <div class="operation-time">
              <el-tooltip :content="formatToDateTime(item.createTime)" placement="top">
                <span class="time-text">{{ formatToDateTime(item.createTime) }}</span>
              </el-tooltip>
            </div>
          </div>
          
          <div class="item-content">
            <div class="change-record">
              <el-tag 
                :type="getChangeType(item.editType)" 
                size="small" 
                class="change-tag"
              >
                {{ getChangeTypeText(item.editType) }}
              </el-tag>
              <div class="change-description">
                {{ getChangeSummary(item.editContent) }}
              </div>
            </div>

            <!-- 详细变更内容 -->
            <div v-if="item.editContent && hasMoreDetails(item.editContent)" class="change-details">
              <el-collapse>
                <el-collapse-item title="查看详细变更" :name="item.id">
                  <div class="details-content">
                    <div class="change-items">
                      <div
                        v-for="(detail, index) in getChangeDetails(item.editContent)"
                        :key="index"
                        class="change-item"
                      >
                        <div class="change-bullet">•</div>
                        <div class="change-text">{{ detail }}</div>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>


          </div>
          
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 无更多数据提示 -->
        <div v-if="noMore && displayList.length > 0" class="no-more">
          <el-divider>
            <span class="no-more-text">没有更多记录了</span>
          </el-divider>
        </div>

        <!-- 空状态 -->
        <el-empty 
          v-if="!loading && displayList.length === 0" 
          description="暂无历史记录"
          :image-size="120"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { formatToDateTime } from '@/utils/dateUtil'
import { Search } from '@element-plus/icons-vue'
import { PublicityApi } from '@/api/products/publicity';

// 定义组件属性
defineOptions({ name: 'HistoryDrawer' })

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const dateRange = ref<[string, string] | null>(null)
const historyList = ref<any[]>([])
const displayList = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const noMore = ref(false)

// 计算属性
const filteredTotal = computed(() => displayList.value.length)

// 方法定义
const open = () => {
  visible.value = true
  resetData()
  loadHistoryData()
}

const handleClose = () => {
  visible.value = false
  resetData()
}

const resetData = () => {
  historyList.value = []
  displayList.value = []
  currentPage.value = 1
  noMore.value = false
  searchKeyword.value = ''
  dateRange.value = null
}

// 加载历史数据
const loadHistoryData = async (isLoadMore = false) => {
  if (loading.value) return

  loading.value = true
  try {
    // 这里调用实际的API
    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      editContent: searchKeyword.value,
      startTime: dateRange.value?.[0],
      endTime: dateRange.value?.[1]
    }

    const response = await PublicityApi.getHistory(params)

    if (isLoadMore) {
      historyList.value.push(...response.list)
    } else {
      historyList.value = response.list
    }

    total.value = response.total
    displayList.value = [...historyList.value]

    if (historyList.value.length >= total.value) {
      noMore.value = true
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (loading.value || noMore.value) return
  currentPage.value++
  loadHistoryData(true)
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  noMore.value = false
  loadHistoryData()
}

// 日期范围变更处理
const handleDateChange = () => {
  currentPage.value = 1
  noMore.value = false
  loadHistoryData()
}

// 格式化相对时间
const formatRelativeTime = (time: string) => {
  const now = new Date()
  const targetTime = new Date(time)
  const diff = now.getTime() - targetTime.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return formatToDateTime(time)
}

// 获取变更类型样式
const getChangeType = (type: number) => {
  const typeMap = {
    0: 'success',  // 新增
    1: 'warning',  // 修改
    2: 'danger'    // 删除
  }
  return typeMap[type] || 'info'
}

// 获取变更类型文本
const getChangeTypeText = (type: number) => {
  const textMap = {
    0: '新增',
    1: '修改',
    2: '删除'
  }
  return textMap[type] || '操作'
}

// 获取变更摘要（显示第一条变更内容）
const getChangeSummary = (editContent: string) => {
  if (!editContent) return ''

  // 按分号分割，取第一条作为摘要
  const changes = editContent.split(';').filter(item => item.trim())
  if (changes.length === 0) return editContent

  const firstChange = changes[0].trim()
  // 如果只有一条变更，直接返回
  if (changes.length === 1) return firstChange

  // 如果有多条变更，显示第一条并提示还有更多
  return `${firstChange}${changes.length > 1 ? '...' : ''}`
}

// 判断是否有更多详情需要展示
const hasMoreDetails = (editContent: string) => {
  if (!editContent) return false

  const changes = editContent.split(';').filter(item => item.trim())
  return changes.length > 1
}

// 获取所有变更详情（分条显示）
const getChangeDetails = (editContent: string) => {
  if (!editContent) return []

  return editContent.split(';')
    .map(item => item.trim())
    .filter(item => item.length > 0)
}

// 格式化editContent，按分号分行显示
const formatEditContent = (editContent: string) => {
  if (!editContent) return []

  return editContent.split(';')
    .map(line => line.trim())
    .filter(line => line.length > 0)
}

// 格式化变更详情
const formatChangeDetails = (details: any) => {
  if (typeof details === 'string') return details
  return JSON.stringify(details, null, 2)
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.history-drawer {
  .drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .search-bar {
      display: flex;
      gap: 12px;
      margin-bottom: 16px;
      
      .search-input {
        flex: 1;
      }
      
      .date-picker {
        width: 300px;
      }
    }
    
    .stats-bar {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .history-list {
      flex: 1;
      overflow-y: auto;
      
      .history-item {
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        &.highlight {
          border-color: #409eff;
          background: #f0f9ff;
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .operator-info {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .operator-details {
              display: flex;
              flex-direction: column;
              
              .operator-name {
                font-weight: 500;
                color: #303133;
              }
              
              .operator-role {
                font-size: 12px;
                color: #909399;
              }
            }
          }
          
          .operation-time {
            .time-text {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .item-content {
          .change-record {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            margin-bottom: 8px;
            
            .change-tag {
              flex-shrink: 0;
              margin-top: 2px;
            }
            
            .change-description {
              flex: 1;
              color: #606266;
              line-height: 1.5;
            }
          }
          
          .change-details {
            margin-top: 12px;

            .details-content {
              background: #f5f7fa;
              border-radius: 4px;
              padding: 12px;

              .change-items {
                .change-item {
                  display: flex;
                  align-items: flex-start;
                  margin-bottom: 8px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .change-bullet {
                    color: #409eff;
                    margin-right: 8px;
                    font-weight: bold;
                    flex-shrink: 0;
                  }

                  .change-text {
                    flex: 1;
                    color: #606266;
                    line-height: 1.5;
                  }
                }
              }

              pre {
                margin: 0;
                font-size: 12px;
                color: #606266;
                white-space: pre-wrap;
                word-break: break-all;
              }
            }
          }
        }
      }
      
      .loading-container {
        padding: 20px;
      }
      
      .no-more {
        text-align: center;
        margin: 20px 0;
        
        .no-more-text {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
