<template>
  <div class="h-[calc(100vh-110px)] overflow-hidden">
    <!-- <van-nav-bar title="业务对账" fixed placeholder /> -->
    <!-- 顶部：客户选择 + 总应收 -->
    <div>
      <van-cell
        title="客户"
        :value="selectedDisplay || '请选择客户'"
        is-link
        @click="openCustomerPicker"
      />
      <van-cell 
        title="本币总应收" 
        :value="formatMoney(totalAmount)" 
        is-link 
        @click="showCurrencyDetail = true"
      />
      <!-- 标签：订单应收 / 费用应收 -->
    </div>
    <van-tabs v-model:active="activeTab" shrink sticky>
      <van-tab title="订单应收" name="order">
                  <!-- 添加搜索框 -->
          <van-search
            v-model="all"
            placeholder="支持查询订单号"
            show-action
            @search="loadOrders"
            @cancel="loadOrders"
            class="sticky top-0 bg-white z-10"
          />
        <div class="h-[calc(100vh-320px)] overflow-auto mt-10px">

          <van-list
            v-model:loading="orderLoading"
            :finished="orderFinished"
            finished-text="没有更多数据了"
            @load="loadOrders"
          >
            <div v-for="(row, i) in orderList" :key="i" class="mb-10px">
              <van-cell-group inset>
                <van-cell title="客户编码" :value="row.customersCode" />
                <van-cell title="客户名称" :value="row.customersName" />
                <van-cell title="订单日期" :value="row.dateStr" />
                <van-cell title="订单号" :value="row.orderNo" />
                <van-cell title="订单金额" :value="formatMoney(row.salesPrice)" />
                <van-cell title="已出货金额" :value="formatMoney(row.shipPrice)" />
                <van-cell title="收款金额" :value="formatMoney(row.collectionAmount)" />
                <van-cell title="收款金额(本币)" :value="formatMoney(row.collectionAmountLocal)" />
                <van-cell title="应收金额" :value="formatMoney(row.receivableAmount)" />
                <van-cell title="应收金额(本币)" :value="formatMoney(row.receivableAmountLocal)" />
                <van-cell title="汇率" :value="row.rate" />
              </van-cell-group>
            </div>
          </van-list>
        </div>
      </van-tab>
      <van-tab title="费用应收" name="fee">
        <div class="h-[calc(100vh-290px)] overflow-auto mt-10px">
          <van-empty v-if="!expenseLoading && expenseList.length === 0" description="暂无数据" />
          <div v-for="(row, i) in expenseList" :key="i" class="mb-8px">
            <van-cell-group inset>
              <van-cell :title="row.label" :label="row.dateStr ? `费用日期：${row.dateStr}` : ''" />
              <van-cell title="费用" :value="formatMoney(row.cost)" />
              <van-cell title="收款金额" :value="formatMoney(row.collectionAmount)" />
              <van-cell title="收款明细">
                <template #default>
                  <span>{{ formatMoney(row.collectionAmount) }}</span>
                </template>
                <template #label>
                  <div class="mt-1">
                    <div 
                      v-for="(detail, index) in row.expenseDetails" 
                      :key="index" 
                      class="text-12px text-gray-500 leading-4"
                      style="width: 200px;"
                    >
                      {{ detail.currency }}: {{ formatMoney(detail.amount) }}
                      <span v-if="detail.rate">, 汇率: {{ detail.rate }}</span>
                    </div>
                  </div>
                </template>
              </van-cell>
              <van-cell title="应收金额(本币)" :value="formatMoney(row.receivableAmount)" />
            </van-cell-group>
          </div>
        </div>
      </van-tab>
    </van-tabs>

    <!-- 客户选择弹层（多选） -->
    <van-popup v-model:show="customerPickerShow" position="bottom" round :style="{ height: '70%' }">
      <div class="h-full flex flex-col">
        <van-search
          v-model="customerKeyword"
          placeholder="输入客户名称检索"
          show-action
          @search="fetchCustomers"
        >
          <template #action>
            <van-button size="small" type="primary" @click="fetchCustomers">搜索</van-button>
          </template>
        </van-search>
        <div class="flex-1 overflow-auto px-10px">
          <van-checkbox-group v-model="customerChecked" shape="square">
            <van-checkbox v-for="c in customerOptions" :key="c.code" :name="c.code" class="mb-8px">
              {{ c.name }} - {{ c.code }}
            </van-checkbox>
          </van-checkbox-group>
        </div>
        <div class="p-10px flex justify-between">
          <van-button size="small" @click="clearCustomers">清空</van-button>
          <div class="space-x-8px">
            <van-button size="small" type="primary" plain @click="selectAllCustomers"
              >全选</van-button
            >
            <van-button size="small" type="success" @click="applyCustomers">确定</van-button>
          </div>
        </div>
      </div>
    </van-popup>

        <!-- 币种明细弹窗 -->
    <van-popup 
      v-model:show="showCurrencyDetail" 
      position="bottom" 
      round 
      :style="{ height: '60%' }"
    >
      <div class="p-16px">
        <div class="text-center text-16px font-bold mb-16px">各币种明细</div>
        <div class="text-center text-14px mb-16px">本币总应收：{{ formatMoney(totalAmount) }}</div>
        <van-divider />
        <div v-for="(detail, index) in currencyDetails" :key="index" class="mb-12px">
          <div class="flex justify-between">
            <span class="text-gray-600">{{ detail.currency }}</span>
            <span class="font-medium">{{ formatMoney(detail.amount) }}</span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { BusinessReconciliationApi } from '@/api/foreign-trade/businessReconciliation/index'

// tabs
const activeTab = ref<'order' | 'fee'>('order')

// 添加获取收款明细标签的方法
const getCollectionDetailLabel = (expenseDetails: any[]) => {
  if (!expenseDetails || expenseDetails.length === 0) {
    return ''
  }
  
  if (expenseDetails.length === 1) {
    const detail = expenseDetails[0]
    return `${detail.currency}: ${formatMoney(detail.amount)}` + 
           (detail.rate ? `, 汇率: ${detail.rate}` : '')
  }
  
  // 多条明细时显示汇总信息
  // return `${expenseDetails.length}条明细，点击查看详情`
    // 多条明细时用分号分隔显示所有明细
  const detailTexts = expenseDetails.map(detail => {
    return `${detail.currency}: ${formatMoney(detail.amount)}` + 
           (detail.rate ? `, 汇率: ${detail.rate}` : '')
  })
  
  return detailTexts.join('；')
}

// 客户选择
const customerPickerShow = ref(false)
const customerKeyword = ref('')
const customerOptions = ref<Array<{ name: string; code: string }>>([])
const customerChecked = ref<string[]>([]) // 选择中的编码
const selectedCodes = ref<string[]>([]) // 已应用的编码
const selectedDisplay = computed(() => {
  if (selectedCodes.value.length === 0) {
    return ''
  }

  // 获取选中客户的名称
  const selectedNames = selectedCodes.value.map((code) => {
    const customer = customerOptions.value.find((item) => item.code === code)
    return customer ? customer.name : code
  })

  // 如果选中的客户较少，直接显示名称
  if (selectedNames.length <= 2) {
    return selectedNames.join(', ')
  }

  // 如果选中的客户较多，显示前两个名称加省略号
  return `${selectedNames.slice(0, 2).join(', ')} 等${selectedNames.length}个客户`
})
const openCustomerPicker = async () => {
  customerPickerShow.value = true
  await fetchCustomers()
  // 打开时预选已应用的
  customerChecked.value = [...selectedCodes.value]
}
const fetchCustomers = async () => {
  const res = await BusinessReconciliationApi.getCustomers(customerKeyword.value || '')
  customerOptions.value = res || []
}
const clearCustomers = () => {
  customerChecked.value = []
}
const selectAllCustomers = () => {
  customerChecked.value = customerOptions.value.map((c) => c.code)
}
const applyCustomers = async () => {
  selectedCodes.value = [...customerChecked.value]
  customerPickerShow.value = false
  await refreshAll()
}

// 总应收
const totalAmount = ref(0)
// 添加币种明细弹窗控制
const showCurrencyDetail = ref(false)
const currencyDetails = ref<Array<{ currency: string; amount: number }>>([])
const loadTotalAmount = async () => {
  try {
    const res = await BusinessReconciliationApi.getTotalAmount({ orderCodes: selectedCodes.value })
    totalAmount.value = Number(res.totalAmount || 0)
    currencyDetails.value = res?.currencyDetails || []
  } catch {
    totalAmount.value = 0
  }
}

// 订单应收（分页）
const orderList = ref<any[]>([])
const orderLoading = ref(false)
const orderFinished = ref(false)
const orderQuery = reactive({ pageNo: 1, pageSize: 10 })

const all = ref('')
const loadOrders = async () => {
  orderLoading.value = true
  try {
    const data = await BusinessReconciliationApi.getOrderReceivablePage({
      pageNo: orderQuery.pageNo,
      pageSize: orderQuery.pageSize,
      orderCodes: selectedCodes.value,
      orderNo: all.value
    })
    const rows = data?.list || []
    if (orderQuery.pageNo === 1) orderList.value = rows
    else orderList.value.push(...rows)
    if (rows.length < orderQuery.pageSize) orderFinished.value = true
    else orderQuery.pageNo += 1
  } finally {
    orderLoading.value = false
  }
}

// 费用应收（不分页）
const expenseList = ref<any[]>([])
const expenseLoading = ref(false)
const loadExpenses = async () => {
  expenseLoading.value = true
  try {
    const res = await BusinessReconciliationApi.getExpenseReceivablePage({
      orderCodes: selectedCodes.value
    })
    expenseList.value = res || []
  } finally {
    expenseLoading.value = false
  }
}

// 刷新入口
const refreshAll = async () => {
  await loadTotalAmount()
  // 重置订单分页并重新加载
  orderQuery.pageNo = 1
  orderFinished.value = false
  orderList.value = []
  if (activeTab.value === 'order') await loadOrders()
  else await loadExpenses()
}

// tab 切换时按需加载
watch(
  () => activeTab.value,
  async (tab) => {
    if (tab === 'order') {
      // 重置订单分页并重新加载
      orderQuery.pageNo = 1
      orderFinished.value = false
      orderList.value = []
      await loadOrders()
    } else {
      if (expenseList.value.length === 0) await loadExpenses()
    }
  }
)

// 首次进入
onMounted(async () => {
  await fetchCustomers()
  await refreshAll()
})

const formatMoney = (n: any) => {
  const num = Number(n || 0)
  return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}
</script>

<style scoped>
.text-red-500 {
  color: #ee0a24;
}
</style>
