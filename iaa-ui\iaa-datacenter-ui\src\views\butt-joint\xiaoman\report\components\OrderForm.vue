<template>
  <NoModalDrawer v-model="visiable">
    <el-collapse v-model="activeNames" ref="collapseRef">
      <el-collapse-item title="基本信息" name="info">
        <el-form
          class="flex flex-wrap justify-between"
          size="small"
          label-position="left"
          label-width="90px"
        >
          <el-form-item label="客户名称:" class="!w-100%">
            {{ tempRow.customerName }}
          </el-form-item>
          <el-form-item label="业务员:" class="!w-33%">
            {{ tempRow.salesperson?.join(',') }}
          </el-form-item>
          <el-form-item label="所属组别:" class="!w-33%">
            {{ tempRow.departmentName }}
          </el-form-item>
          <el-form-item label="国家/地区:" class="!w-33%">
            {{ tempRow.countryName }}
          </el-form-item>
          <el-form-item label="洲/省:" class="!w-33%">
            {{ tempRow.regionOrProvince }}
          </el-form-item>
          <el-form-item label="城市:" class="!w-33%">
            {{ tempRow.city }}
          </el-form-item>
          <el-form-item label="客户画像:" class="!w-33%">
            {{ tempRow.customerPortraits }}
          </el-form-item>
          <el-form-item label="客户性质:" class="!w-33%">
            {{ tempRow.nature }}
          </el-form-item>
          <el-form-item label="合作阶段:" class="!w-33%">
            {{ tempRow.trailStatus }}
          </el-form-item>
          <el-form-item label="预估年销(万):" class="!w-33%">
            {{ tempRow.estimatedAnnualSales }}
          </el-form-item>
          <el-form-item label="营收规模:" class="!w-33%">
            {{ tempRow.revenueScale }}
          </el-form-item>
          <el-form-item label="香氛规模:" class="!w-33%">
            {{ tempRow.fragranceRevenueScale }}
          </el-form-item>

          <el-form-item label="客户官网:" class="!w-100%">
            {{ tempRow.homepage }}
          </el-form-item>
        </el-form>
      </el-collapse-item>
      <template v-if="tempType === 'order'">
        <el-collapse-item title="近三年销售额" name="order1">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <el-form-item
              :label="`${dayjs().subtract(2, 'years').format('YYYY')}年销售:`"
              class="!w-33%"
            >
              {{
                formatterNumber(
                  tempRow?.yearOrderMap?.[dayjs().subtract(2, 'years').format('YYYY')]
                )
              }}
            </el-form-item>
            <el-form-item
              :label="`${dayjs().subtract(1, 'years').format('YYYY')}年销售:`"
              class="!w-33%"
            >
              {{
                formatterNumber(
                  tempRow?.yearOrderMap?.[dayjs().subtract(1, 'years').format('YYYY')]
                )
              }}
            </el-form-item>
            <el-form-item :label="`${dayjs().format('YYYY')}年销售:`" class="!w-33%">
              {{ formatterNumber(tempRow?.yearOrderMap?.[dayjs().format('YYYY')]) }}
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="竟对情况" name="order2">
          <el-form class="flex flex-wrap justify-between" size="small">
            <el-form-item class="!w-100%">
              <div style="min-height: 50px">
                {{ tempRow.corrival }}
              </div>
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="销售额预测及达标情况" name="order3">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <el-form-item label="2024预测:" class="!w-50%">
              {{ formatterNumber(tempRow.tztfourPredictionSales) }}
            </el-form-item>
            <el-form-item label="2024达标:" class="!w-50%">
              {{ formatterNumber(tempRow.tztfourReachingStandard) }}
            </el-form-item>
            <el-form-item label="2025预测:" class="!w-50%">
              {{ formatterNumber(tempRow.tztfivePredictionSales) }}
            </el-form-item>
            <el-form-item label="2025达标:" class="!w-50%">
              {{ formatterNumber(tempRow.tztfiveReachingStandard) }}
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item
          :title="`${key}销售数据`"
          v-for="(item, key) in tempRow?.orderTotalList"
          :key="key"
          :name="key"
        >
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <el-form-item label="预测销量:" class="!w-33%">
              {{ formatterNumber(item.forecastQty) }}
            </el-form-item>
            <el-form-item label="预测金额:" class="!w-33%">
              {{ formatterNumber(item.forecastAmount) }}
            </el-form-item>
            <el-form-item label="第一周:" class="!w-33%">
              {{ formatterNumber(item.oneWeekAmount) }}
            </el-form-item>
            <el-form-item label="第二周:" class="!w-33%">
              {{ formatterNumber(item.twoWeekAmount) }}
            </el-form-item>
            <el-form-item label="第三周:" class="!w-33%">
              {{ formatterNumber(item.threeWeekAmount) }}
            </el-form-item>
            <el-form-item label="第四周:" class="!w-33%">
              {{ formatterNumber(item.fourWeekAmount) }}
            </el-form-item>
            <el-form-item label="总销量:" class="!w-33%">
              {{ formatterNumber(item.totalQty) }}
            </el-form-item>
            <el-form-item label="总销售额:" class="!w-33%">
              {{ formatterNumber(item.totalAmount) }}
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="回款情况" name="order4">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <el-form-item
              v-for="(item, key) in tempRow.paymentMap"
              :key="key"
              :label="`${key}`"
              class="!w-33%"
            >
              {{ formatterNumber(item) }}
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </template>
      <template v-else-if="tempType === 'opportunity'">
        <el-collapse-item title="竟对机器描述" name="opportunity1">
          <div style="min-height: 20px">
            {{ tempRow.untoMachineRemark }}
          </div>
        </el-collapse-item>
        <el-collapse-item title="竟对精油类数据描述" name="opportunity2">
          <div style="min-height: 20px">
            {{ tempRow.untoEssentialOilRemark }}
          </div>
        </el-collapse-item>
        <el-collapse-item title="相关性产品描述" name="opportunity3">
          <div style="min-height: 20px">
            {{ tempRow.relevantProductRemark }}
          </div>
        </el-collapse-item>
        <el-collapse-item title="合作机器类产品TOP5(人民币元)" name="opportunity4">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <template v-for="(item, index) in [1, 2, 3, 4, 5]" :key="index">
              <el-form-item :label="`产品${item}型号:`" class="!w-50%">
                {{ tempRow[`machineModel${item}`] }}
              </el-form-item>
              <el-form-item :label="`产品${item}金额:`" class="!w-50%">
                {{ formatterNumber(tempRow[`machinePrice${item}`]) }}
              </el-form-item>
            </template>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="合作精油类产品TOP5(人民币元)" name="opportunity5">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <template v-for="(item, index) in [1, 2, 3, 4, 5]" :key="index">
              <el-form-item :label="`产品${item}香型:`" class="!w-50%">
                {{ tempRow[`oilModel${item}`] }}
              </el-form-item>
              <el-form-item :label="`产品${item}金额:`" class="!w-50%">
                {{ formatterNumber(tempRow[`oilModel${item}`]) }}
              </el-form-item>
            </template>
          </el-form>
        </el-collapse-item>
      </template>
      <template v-else-if="tempType === 'customer'">
        <el-collapse-item title="开发信息" name="customer1">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <el-form-item label="开发类型:" class="!w-33%">
              {{ tempRow.developmentType }}
            </el-form-item>
            <el-form-item label="作战产品:" class="!w-33%">
              {{ tempRow.combatProduct }}
            </el-form-item>
            <el-form-item label="开发目标:" class="!w-33%">
              {{ tempRow.developmentTarget }}
            </el-form-item>
            <el-form-item label="预计开发天数:" class="!w-33%">
              {{ tempRow.planDevelopmentDays }}
            </el-form-item>
            <el-form-item label="开始时间:" class="!w-33%">
              {{ tempRow.createTime }}
            </el-form-item>
            <el-form-item label="已开发天数:" class="!w-33%">
              {{ tempRow.developmentDays }}
            </el-form-item>
            <el-form-item label="时间进度:" class="!w-100%">
              <el-progress
                class="!w-100%"
                :percentage="tempRow.progress"
                :color="tempRow.hasOverdue ? 'red' : 'green'"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="结果信息" name="customer2">
          <el-form
            class="flex flex-wrap justify-between"
            size="small"
            label-position="left"
            label-width="90px"
          >
            <el-form-item label="进展:" class="!w-33%">
              {{ tempRow.stageInfo }}
            </el-form-item>
            <el-form-item label="结果:" class="!w-33%">
              <el-tag type="success" v-if="tempRow.result === '赢单'">{{ tempRow.result }}</el-tag>
              <el-tag type="danger" v-if="tempRow.result === '输单'">{{ tempRow.result }}</el-tag>
            </el-form-item>
            <el-form-item label="失败原因:" class="!w-33%">
              {{ tempRow.failTypeName }}
            </el-form-item>
            <el-form-item label="失败描述:" class="!w-100%">
              {{ tempRow.failRemark }}
            </el-form-item>
            <el-form-item label="实际完成日期:" class="!w-33%">
              {{ tempRow.accountDate }}
            </el-form-item>
            <el-form-item label="实际用时(天):" class="!w-33%">
              {{ tempRow.accountDays }}
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </template>
    </el-collapse>
  </NoModalDrawer>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'

const tempRow = ref<any>()
const tempType = ref('')
const visiable = ref(false)

const activeNames = ref<string[]>([
  'info',
  'order1',
  'order2',
  'order3',
  'order4',
  'opportunity1',
  'opportunity2',
  'opportunity3',
  'opportunity4',
  'opportunity5',
  'customer1',
  'customer2'
])

const open = (row: any, type: string) => {
  tempRow.value = row
  visiable.value = true
  tempType.value = type
  switch (type) {
    case 'order':
      for (let key in row.orderTotalList) {
        activeNames.value.push(key)
      }
      break
  }
}

const formatterNumber = (tempValue: number | any) => {
  if (typeof tempValue === 'number') {
    return addThousandSeprator(tempValue.toFixed(0))
  } else if (tempValue && typeof tempValue.cellValue === 'number') {
    return addThousandSeprator(tempValue.cellValue.toFixed(0))
  } else {
    return ''
  }
}

const addThousandSeprator = (strOrNum) => {
  return parseFloat(strOrNum)
    .toString()
    .split('.')
    .map((x, idx) => {
      if (!idx) {
        return x
          .split('')
          .reverse()
          .map((xx, idxx) => (idxx && !(idxx % 3) ? xx + ',' : xx))
          .reverse()
          .join('')
      } else {
        return x
      }
    })
    .join('.')
}
defineExpose({ open })
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  color: var(--el-color-info);
}

:deep(.el-form-item) {
  margin-bottom: 5px;
}

:deep(.el-collapse-item__header) {
  border: none !important;
  border-left: 4px solid var(--el-color-primary) !important;
  padding-left: 4px;
  height: 30px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 3px;
}

:deep(.el-collapse-item) {
  border: none !important;
}
</style>
