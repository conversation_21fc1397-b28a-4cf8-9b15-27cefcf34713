<template>
  <vxe-toolbar ref="toolbarRef" custom size="mini">
    <template #buttons>
      <el-button
        type="success"
        size="small"
        plain
        @click="dialogVisiable = true"
        v-hasPermi="['order-bom:task:save']"
      >
        录入需求
      </el-button>
      <el-button type="primary" size="small" plain @click="handleList">查询</el-button>
      <el-button type="warning" size="small" plain @click="refreshQuery">清空筛选</el-button>
      <el-button
        type="info"
        size="small"
        plain
        @click="onModify"
        v-hasPermi="['order-bom:task:save']"
        >修改</el-button
      >
      <el-button
        type="danger"
        size="small"
        plain
        @click="onDelete"
        v-hasPermi="['order-bom:task:delete']"
        >删除</el-button
      >
      <el-date-picker
        v-model="queryParams.overdue"
        class="ml-10px"
        size="small"
        placeholder="未完成数据"
        @change="handleList"
      />
    </template>
    <template #tools>
      <el-switch
        active-text="隐藏已完成"
        inactive-text="显示所有"
        size="small"
        v-model="queryParams.showAll"
        @change="handleList"
      />
      <el-button size="small" circle @click="handleDownload">
        <Icon icon="ep:download" />
      </el-button>
    </template>
  </vxe-toolbar>
  <div class="h-[calc(100vh-260px)]">
    <vxe-table
      :cell-config="{ height: 34 }"
      :row-config="{ height: 34, isCurrent: true, isHover: true }"
      id="mainTable"
      @cell-click="(params: any) => handleCellClick(params)"
      :custom-config="customConfig"
      :header-cell-style="{ padding: 0, height: '30px' }"
      :cell-style="{ padding: 0, height: '30px' }"
      :checkbox-config="{ labelField: 'check', range: true }"
      :column-config="{ resizable: true, maxFixedSize: 0 }"
      :virtual-y-config="{ enabled: true, gt: 0 }"
      align="center"
      border
      show-overflow
      height="100%"
      :data="dataList"
      ref="tableRef"
      size="small"
      :row-style="rowStyle"
    >
      <vxe-column type="checkbox" width="50" field="check" align="center" />
      <vxe-column field="businessDate" width="200" title="日期">
        <template #header>
          <div>日期</div>
          <el-date-picker
            v-model="queryParams.businessDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="seller" width="100" title="业务员">
        <template #header>
          <div>业务员</div>
          <el-input
            v-model="queryParams.seller"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="customerName" width="120" title="客户">
        <template #header>
          <div>客户</div>
          <el-input
            v-model="queryParams.customerName"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="itemCode" width="120" title="品号">
        <template #header>
          <div>品号</div>
          <el-input
            v-model="queryParams.itemCode"
            clearable
            placeholder="左查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="itemName" width="140" title="品名">
        <template #header>
          <div>品名</div>
          <el-input
            v-model="queryParams.itemName"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="spec" min-width="200" title="规格">
        <template #header>
          <div>规格</div>
          <el-input
            v-model="queryParams.spec"
            clearable
            placeholder="模糊查询"
            size="small"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="lastOrderQty" width="120" title="最后下单数量">
        <template #header>
          <div>最后下单数量</div>
        </template>
      </vxe-column>
      <vxe-column field="lastOrderDate" width="120" title="最后下单日期">
        <template #header>
          <div>最后下单日期</div>
        </template>
      </vxe-column>
      <vxe-column field="packing" width="120" title="定制包材类">
        <template #header>
          <div>定制包材类</div>
          <el-select
            v-model="queryParams.packing"
            multiple
            clearable
            collapse-tags
            size="small"
            @change="handleList"
          >
            <el-option
              v-for="dict in getStrDictOptions('eng_packing_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
            <el-option
              v-for="dict in ColorStatus"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          <el-tag type="success" v-if="!row.packing || row?.packing == '99'">无定制</el-tag>
          <DetailDate :data="row.packingDetail" v-else @success="getList" />
        </template>
      </vxe-column>
      <vxe-column field="logo" width="120" title="定制logo类">
        <template #header>
          <div>定制logo类</div>
          <el-select
            v-model="queryParams.logo"
            multiple
            clearable
            collapse-tags
            size="small"
            @change="handleList"
          >
            <el-option
              v-for="dict in getStrDictOptions('eng_logo_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
            <el-option
              v-for="dict in ColorStatus"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          <el-tag type="success" v-if="!row.logo || row?.logo == '99'">无定制</el-tag>
          <DetailDate :data="row.logoDetail" v-else @success="getList" />
        </template>
      </vxe-column>
      <vxe-column field="instruction" width="120" title="定制说明书类">
        <template #header>
          <div>定制说明书类</div>
          <el-select
            v-model="queryParams.instruction"
            multiple
            clearable
            collapse-tags
            size="small"
            @change="handleList"
          >
            <el-option
              v-for="dict in getStrDictOptions('eng_instruction_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
            <el-option
              v-for="dict in ColorStatus"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          <el-tag type="success" v-if="!row.instruction || row?.instruction == '99'">无定制</el-tag>

          <DetailDate :data="row.instructionDetail" v-else @success="getList" />
        </template>
      </vxe-column>
      <vxe-column field="program" width="120" title="定制程序类">
        <template #header>
          <div>定制程序类</div>
          <el-select
            v-model="queryParams.program"
            multiple
            clearable
            collapse-tags
            size="small"
            @change="handleList"
          >
            <el-option
              v-for="dict in getStrDictOptions('eng_program_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
            <el-option
              v-for="dict in ColorStatus"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          <el-tag type="success" v-if="!row.program || row?.program == '99'">无定制</el-tag>

          <DetailDate :data="row.programDetail" v-else @success="getList" />
        </template>
      </vxe-column>
      <vxe-column field="structure" width="120" title="定制结构类">
        <template #header>
          <div>定制结构类</div>
          <el-select
            v-model="queryParams.structure"
            multiple
            clearable
            collapse-tags
            size="small"
            @change="handleList"
          >
            <el-option
              v-for="dict in getStrDictOptions('eng_structure_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
            <el-option
              v-for="dict in ColorStatus"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
        <template #default="{ row }">
          <el-tag type="success" v-if="!row.structure || row?.structure == '99'">无定制</el-tag>

          <DetailDate :data="row.structureDetail" v-else @success="getList" />
        </template>
      </vxe-column>
      <vxe-column field="remark" width="120" title="备注">
        <template #header>
          <div>备注</div>
          <el-input v-model="queryParams.remark" size="small" clearable @change="handleList" />
        </template>
      </vxe-column>
      <vxe-column field="planBomCompleteDate" width="200" title="BOM计划完成日期">
        <template #header>
          <div>BOM计划完成日期</div>
          <el-date-picker
            v-model="queryParams.planBomCompleteDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="planBomCompleteDateChange"
        width="200"
        title="BOM计划完成变更日期"
        class-name="cursor-pointer"
      >
        <template #header>
          <div>BOM计划完成变更日期</div>
          <el-date-picker
            v-model="queryParams.planBomCompleteDateChange"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="planCompleteDate" width="200" title="计划完成日期">
        <template #header>
          <div>计划完成日期</div>
          <el-date-picker
            v-model="queryParams.planCompleteDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="actualCompleteDate" width="200" title="实际完成日期">
        <template #header>
          <div>实际完成日期</div>
          <el-date-picker
            v-model="queryParams.actualCompleteDate"
            clearable
            type="daterange"
            value-format="YYYY-MM-DD"
            size="small"
            class="!w-100%"
            @change="handleList"
          />
        </template>
      </vxe-column>
      <vxe-column field="taskStatus" width="100" title="任务状态">
        <template #header>
          <div class="w-100%">任务状态</div>
          <el-select
            v-model="queryParams.taskStatus"
            clearable
            collapse-tags
            size="small"
            class="!w-100%"
            @change="getList"
          >
            <el-option label="未到期" value="未到期" />
            <el-option label="超期未完成" value="超期未完成" />
            <el-option label="超期完成" value="超期完成" />
            <el-option label="按期完成" value="按期完成" />
          </el-select>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <!-- 分页 -->
  <Pagination
    v-model:limit="queryParams.pageSize"
    v-model:page="queryParams.pageNo"
    :total="total"
    @pagination="getList"
    size="small"
  />

  <Dialog title="录入订单需求" v-model="dialogVisiable" width="80%" :before-close="formBeforeClose">
    <el-form label-width="105" size="small" :model="formData" :rules="formRules" ref="formRef">
      <el-row>
        <el-col :span="6">
          <el-form-item label="日期">
            <el-date-picker
              class="!w-100%"
              v-model="formData.businessDate"
              type="date"
              placeholder="选择日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="业务员">
            <el-input v-model="formData.seller" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户编码">
            <el-autocomplete
              v-model="formData.customerCode"
              :fetch-suggestions="
                (queryString: any, cb: any) => getCustomerInfo(queryString, cb, 'code')
              "
              clearable
              @select="(item: any) => handleCustomerSelect(item, 'code')"
              @clear="
                () => {
                  formData.customerName = undefined
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户名称">
            <el-autocomplete
              v-model="formData.customerName"
              :fetch-suggestions="
                (queryString: any, cb: any) => getCustomerInfo(queryString, cb, 'name')
              "
              clearable
              @select="(item: any) => handleCustomerSelect(item, 'name')"
              @clear="
                () => {
                  formData.customerCode = undefined
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品号" prop="itemCode">
            <el-autocomplete
              v-model="formData.itemCode"
              :fetch-suggestions="getItemInfo"
              clearable
              @select="handleItemSelect"
              @clear="
                () => {
                  ;(formData.itemName = undefined), (formData.spec = undefined)
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品名">
            <el-input v-model="formData.itemName" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格">
            <el-input v-model="formData.spec" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="定制包材">
            <el-select v-model="formData.packing" clearable>
              <el-option
                v-for="dict in getStrDictOptions('eng_packing_dict')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="18">
          <el-form-item label="定制包材描述">
            <el-input v-model="formData.packingDescription" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label="定制logo">
            <el-select v-model="formData.logo" clearable>
              <el-option
                v-for="dict in getStrDictOptions('eng_logo_dict')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="18">
          <el-form-item label="定制logo描述">
            <el-input v-model="formData.logoDescription" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label="定制说明书">
            <el-select v-model="formData.instruction" clearable>
              <el-option
                v-for="dict in getStrDictOptions('eng_instruction_dict')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="18">
          <el-form-item label="定制说明书描述">
            <el-input v-model="formData.instructionDescription" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label="定制程序">
            <el-select v-model="formData.program" clearable>
              <el-option
                v-for="dict in getStrDictOptions('eng_program_dict')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="18">
          <el-form-item label="定制程序描述">
            <el-input v-model="formData.programDescription" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label="定制结构">
            <el-select v-model="formData.structure" clearable>
              <el-option
                v-for="dict in getStrDictOptions('eng_structure_dict')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="BOM计划完成">
            <el-date-picker
              v-model="formData.planBomCompleteDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="18">
          <el-form-item label="定制结构描述">
            <el-input v-model="formData.structureDescription" clearable />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="flex justify-between p-8px rounded-4px bg-#f5f7fa">
      <el-button type="info" plain size="small" @click="showPasteParser = true">
        批量粘贴解析
      </el-button>
      <el-button type="primary" plain size="small" @click="addList" :loading="formLoading">
        继续添加
      </el-button>
    </div>
    <vxe-table
      :header-cell-style="{ padding: 0, height: '30px' }"
      :cell-style="{ padding: 0 }"
      :cell-config="{ height: 34 }"
      :column-config="{ resizable: true }"
      align="center"
      border
      show-overflow
      height="380px"
      :data="batchAddList"
      stripe
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
    >
      <vxe-column title="日期" field="businessDate" width="120">
        <template #default="{ row }">
          <el-date-picker
            v-model="row.businessDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择日期"
            class="!w-100%"
          />
        </template>
      </vxe-column>
      <vxe-column title="业务员" field="seller" width="120">
        <template #default="{ row }">
          <el-input v-model="row.seller" />
        </template>
      </vxe-column>
      <vxe-column title="客户编码" field="customerCode" width="120">
        <template #default="{ row }">
          <el-autocomplete
            v-model="row.customerCode"
            :fetch-suggestions="
              (queryString: any, cb: any) => getCustomerInfo(queryString, cb, 'code')
            "
            clearable
            @select="
              (item: any) => {
                row.customerName = item.label
              }
            "
            @clear="
              () => {
                row.customerName = undefined
              }
            "
          />
        </template>
      </vxe-column>
      <vxe-column title="客户名称" field="customerName" width="120">
        <template #default="{ row }">
          <el-autocomplete
            v-model="row.customerName"
            :fetch-suggestions="
              (queryString: any, cb: any) => getCustomerInfo(queryString, cb, 'name')
            "
            clearable
            @select="
              (item: any) => {
                row.customerCode = item.label
              }
            "
            @clear="
              () => {
                formData.customerCode = undefined
              }
            "
          />
        </template>
      </vxe-column>
      <vxe-column title="品号" field="itemCode" width="120">
        <template #default="{ row }">
          <el-autocomplete
            v-model="row.itemCode"
            :fetch-suggestions="getItemInfo"
            clearable
            @select="
              (item: any) => {
                row.itemName = item.name
                row.spec = item.spec
              }
            "
            @clear="
              () => {
                ;(row.itemName = undefined), (row.spec = undefined)
              }
            "
          />
        </template>
      </vxe-column>
      <vxe-column title="品名" field="itemName" width="120" />
      <vxe-column title="规格" field="spec" width="120" />
      <vxe-column title="定制包材" field="packing" width="150">
        <template #default="{ row }">
          <el-select v-model="row.packing" clearable>
            <el-option
              v-for="dict in getStrDictOptions('eng_packing_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </vxe-column>
      <!-- <vxe-column title="定制包材描述" field="packingDescription" width="120" /> -->
      <vxe-column title="定制Logo" field="logo" width="150">
        <template #default="{ row }">
          <el-select v-model="row.logo" clearable>
            <el-option
              v-for="dict in getStrDictOptions('eng_logo_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </vxe-column>
      <!-- <vxe-column title="定制Logo描述" field="logoDescription" width="120" /> -->
      <vxe-column title="定制说明书" field="instruction" width="150">
        <template #default="{ row }">
          <el-select v-model="row.instruction" clearable>
            <el-option
              v-for="dict in getStrDictOptions('eng_instruction_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </vxe-column>
      <!-- <vxe-column title="定制说明书描述" field="instructionDescription" width="120" /> -->
      <vxe-column title="定制程序" field="program" width="120">
        <template #default="{ row }">
          <el-select v-model="row.program" clearable>
            <el-option
              v-for="dict in getStrDictOptions('eng_program_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </vxe-column>
      <!-- <vxe-column title="定制程序描述" field="programDescription" width="120">
        <template #default="{ row }">
          <el-input v-model="row.programDescription" />
        </template>
      </vxe-column> -->
      <vxe-column title="定制结构" field="structure" width="120">
        <template #default="{ row }">
          <el-select v-model="row.structure" clearable>
            <el-option
              v-for="dict in getStrDictOptions('eng_structure_dict')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </vxe-column>
      <!-- <vxe-column title="定制结构描述" field="structureDescription" width="120">
        <template #default="{ row }">
          <el-input v-model="row.structureDescription" />
        </template>
      </vxe-column> -->
      <vxe-column title="BOM计划完成" field="planBomCompleteDate" width="140">
        <template #default="{ row }">
          <el-date-picker
            v-model="row.planBomCompleteDate"
            type="date"
            value-format="YYYY-MM-DD"
            class="!w-100%"
            clearable
            :disabled="row.id"
          />
        </template>
      </vxe-column>
      <vxe-column
        title="BOM计划完成变更"
        field="planBomCompleteDateChange"
        width="140"
        v-if="isEdit"
      >
        <template #default="{ row }">
          <el-date-picker
            v-model="row.planBomCompleteDateChange"
            type="date"
            value-format="YYYY-MM-DD"
            class="!w-100%"
            clearable
            :disabled="row.id && !checkPermi(['edit:bom-complete:date'])"
          />
        </template>
      </vxe-column>
      <vxe-column
        title="实际完成日期"
        field="actualCompleteDateMain"
        width="140"
        v-if="hasEmptyCustomizationFields()"
      >
        <template #default="{ row }">
          <el-date-picker
            v-model="row.actualCompleteDateMain"
            type="date"
            value-format="YYYY-MM-DD"
            class="!w-100%"
            clearable
            :disabled="row.id && !hasEmptyCustomizationFieldsRow(row)"
          />
        </template>
      </vxe-column>
      <vxe-column title="备注" field="remark" width="120">
        <template #default="{ row }">
          <el-input v-model="row.remark" />
        </template>
      </vxe-column>
      <vxe-column title="操作" field="opertion" width="80" fixed="right">
        <template #default="{ rowIndex }">
          <el-button type="danger" link @click="removeRow(rowIndex)">移除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" size="small" @click="saveTask" :loading="formLoading">
        保存
      </el-button>
    </template>
  </Dialog>

  <!-- 粘贴解析器弹窗 -->
  <Dialog
    title="数据批量粘贴解析"
    v-model="showPasteParser"
    width="90%"
    :before-close="() => (showPasteParser = false)"
  >
    <PasteParser
      :target-fields="targetFields"
      :mappings="mappings"
      @data-parsed="handleParsedData"
    />
  </Dialog>

  <el-dialog v-model="dialogVisible" title="修改原因" width="400" align-center>
    <div>
      <p>请输入修改原因</p>
      <el-input
        v-model="reason"
        type="textarea"
        style="width: 380px"
        placeholder="请输入修改原因"
      />
      <p>请选择责任主体</p>
      <el-radio-group v-model="category" style="display: block; margin-bottom: 10px">
        <el-radio label="研发">研发</el-radio>
        <el-radio label="工程">工程</el-radio>
        <el-radio label="业务">业务</el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确认</el-button>
      </span>
    </template>
  </el-dialog>
  <DetailForm ref="detailFormRef" width="34%" @success="handleList" />
</template>

<script setup lang="ts">
import { getStrDictOptions } from '@/utils/dict'
import DetailForm from './DetailForm.vue'
import { CustomerApi } from '@/api/report/erp/order-bom'
import { cloneDeep, debounce } from 'lodash-es'
import DetailDate from './DetailDate.vue'
import PasteParser from './PasteParser.vue'
import { VxeTablePropTypes } from 'vxe-table'
import { customConfig } from '@/utils/vxeCustom'
import { checkPermi } from '@/utils/permission'
import download from '@/utils/download'
import { ElMessageBox } from 'element-plus'

const message = useMessage()

const dataList = ref<any[]>([])
const total = ref(0)
const loading = ref(false)

const queryParams = ref({
  pageNo: 1,
  pageSize: 100,
  businessDate: undefined,
  seller: undefined,
  customerName: undefined,
  itemCode: undefined,
  itemName: undefined,
  spec: undefined,
  packing: undefined,
  logo: undefined,
  instruction: undefined,
  program: undefined,
  structure: undefined,
  remark: undefined,
  planBomCompleteDate: undefined,
  planBomCompleteDateChange: undefined,
  planCompleteDate: undefined,
  actualCompleteDate: undefined,
  taskStatus: undefined,
  showAll: true,
  overdue: undefined
})

const toolbarRef = ref()
const tableRef = ref()
const formRef = ref()

const detailFormRef = ref()

const handleCellClick = (params: any) => {
  // 只有点击 "BOM计划完成变更日期" 这一列才弹窗
  if (params.column.field === 'planBomCompleteDateChange') {
    detailFormRef.value?.openForm(getRowWithType(params.row))
  }
}
/** 获取BOM任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await CustomerApi.pageTask(queryParams.value)
    dataList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleList = async () => {
  queryParams.value.pageNo = 1
  getList()
}

const ColorStatus = [
  { label: '待处理', value: '98' },
  { label: '正常', value: '97' },
  { label: '预警', value: '96' },
  { label: '超期', value: '95' },
  { label: '超期完成', value: '94' }
]

const refreshQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 100,
    businessDate: undefined,
    seller: undefined,
    customerName: undefined,
    itemCode: undefined,
    itemName: undefined,
    spec: undefined,
    packing: undefined,
    logo: undefined,
    instruction: undefined,
    program: undefined,
    structure: undefined,
    remark: undefined,
    planBomCompleteDate: undefined,
    planBomCompleteDateChange: undefined,
    planCompleteDate: undefined,
    actualCompleteDate: undefined,
    showAll: true,
    taskStatus: undefined,
    overdue: undefined
  }
  handleList()
}

const dialogVisiable = ref(false)
const formLoading = ref(false)
const showPasteParser = ref(false)
const formData = ref({
  id: undefined,
  businessDate: undefined,
  seller: undefined,
  customerCode: undefined,
  customerName: undefined,
  itemCode: undefined,
  itemName: undefined,
  spec: undefined,
  packing: undefined,
  packingDescription: undefined,
  logo: undefined,
  logoDescription: undefined,
  instruction: undefined,
  instructionDescription: undefined,
  program: undefined,
  programDescription: undefined,
  structure: undefined,
  structureDescription: undefined,
  planBomCompleteDate: undefined,
  remark: undefined
})
const formRules = reactive({
  itemCode: [{ required: true, message: '请输入物料编码', trigger: 'blur' }]
})
const batchAddList = ref<any[]>([])

// 目标字段选项
const targetFields = [
  { label: '品号', value: 'itemCode' },
  { label: '品名', value: 'itemName' },
  { label: '规格', value: 'spec' }
]
// 根据示例数据和位置推断字段类型
const mappings = [
  'itemCode', // 第1列：品号
  'itemName', // 第3列：品名
  'spec' // 规格
]

/** 处理粘贴解析的数据 */
const handleParsedData = (parsedItems: any[]) => {
  // 将解析的数据添加到批量添加列表中
  parsedItems.forEach((item) => {
    const newItem: any = {
      ...formData.value, // 保留表单中的基础信息
      itemCode: item.itemCode,
      itemName: item.itemName,
      spec: item.spec
    }

    batchAddList.value.push(newItem)
  })

  // 关闭粘贴解析器弹窗
  showPasteParser.value = false

  // 提示用户
  message.success(`成功导入 ${parsedItems.length} 条数据到批量添加列表`)
}

const menuConfig = reactive<VxeTablePropTypes.MenuConfig<any>>({
  body: {
    options: [[{ code: 'batch', name: '批量设置' }]]
  }
})

const contextMenuClickEvent = ({ menu, rowIndex, column }) => {
  if (menu.code == 'batch') {
    for (let i = rowIndex; i < batchAddList.value.length; i++) {
      batchAddList.value[i][column.field] = batchAddList.value[rowIndex][column.field]
    }
  }
}

const rowStyle: VxeTablePropTypes.RowStyle<any> = ({ row }) => {
  if (row['actualCompleteDate']) {
    return {
      backgroundColor: 'var(--el-color-success-light-3) !important',
      color: '#ffffff'
    }
  }
}

// 判断是否存在空的定制字段（即所有定制字段都为空或为99）
const hasEmptyCustomizationFields = () => {
  return batchAddList.value.some((row) => {
    const instructionEmpty = !row.instruction || row.instruction === '99'
    const logoEmpty = !row.logo || row.logo === '99'
    const packingEmpty = !row.packing || row.packing === '99'
    const programEmpty = !row.program || row.program === '99'
    const structureEmpty = !row.structure || row.structure === '99'

    return instructionEmpty && logoEmpty && packingEmpty && programEmpty && structureEmpty
  })
}

const hasEmptyCustomizationFieldsRow = (row: any) => {
  const instructionEmpty = !row.instruction || row.instruction === '99'
  const logoEmpty = !row.logo || row.logo === '99'
  const packingEmpty = !row.packing || row.packing === '99'
  const programEmpty = !row.program || row.program === '99'
  const structureEmpty = !row.structure || row.structure === '99'
  return instructionEmpty && logoEmpty && packingEmpty && programEmpty && structureEmpty
}
/** 将表单数据添加到列表中 */
const addList = async () => {
  formLoading.value = true
  try {
    await formRef.value?.validate()
    if (
      !formData.value.packing &&
      !formData.value.program &&
      !formData.value.structure &&
      !formData.value.logo &&
      !formData.value.instruction
    ) {
      message.error('请至少选择一项定制内容！')
      return
    }
    batchAddList.value.push({ ...formData.value })
    refresh()
  } finally {
    formLoading.value = false
  }
}

const getRowWithType = (row: any): any => {
  return {
    ...row,
    type: 'main'
  }
}

const dialogVisible = ref(false)
const reason = ref('')
const category = ref('')

const cancelDialog = () => {
  dialogVisible.value = false
}

const confirmDialog = async () => {
  formLoading.value = true
  try {
    batchAddList.value.forEach((item) => {
      const modifyInfo = editBeforeValue.value.find((el) => el.id == item.id)?.planBomCompleteDate
      if (item.planBomCompleteDateChange != modifyInfo) {
        item.modifyInfo = `BOM计划完成变更时间从${modifyInfo}修改为${item.planBomCompleteDateChange}`
        item.reason = reason
        item.category = category
      }
    })
    await CustomerApi.batchSaveTask(batchAddList.value)
    dialogVisible.value = false
    reason.value = ''
    category.value = ''
    message.success('保存成功')
    formBeforeClose()
    getList()
  } finally {
    formLoading.value = false
  }
}
/** 保存任务 */
const saveTask = async () => {
  formLoading.value = true
  try {
    if (batchAddList.value.length == 0) {
      await addList()
      // message.error('请添加数据！')
      // return
    }
    if (
      batchAddList.value.some((item) => item.id) &&
      editBeforeValue.value.some((item) => {
        const afterValue = batchAddList.value.find(
          (el) => el.id == item.id
        )?.planBomCompleteDateChange
        return afterValue != item.planBomCompleteDateChange
      })
    ) {
      dialogVisible.value = true
    } else {
      await CustomerApi.batchSaveTask(batchAddList.value)
      message.success('保存成功')
      formBeforeClose()
      getList()
    }
  } finally {
    formLoading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    businessDate: undefined,
    seller: undefined,
    customerCode: undefined,
    customerName: undefined,
    itemCode: undefined,
    itemName: undefined,
    spec: undefined,
    packing: undefined,
    packingDescription: undefined,
    logo: undefined,
    logoDescription: undefined,
    instruction: undefined,
    instructionDescription: undefined,
    program: undefined,
    programDescription: undefined,
    structure: undefined,
    structureDescription: undefined,
    planBomCompleteDate: undefined,
    remark: undefined
  }
}

const formBeforeClose = (done?: any) => {
  refresh()
  batchAddList.value = []
  dialogVisiable.value = false
  isEdit.value = false // 回到新增模式
  if (done) {
    done()
  }
}

const removeRow = async (index: number) => {
  await message.confirm('确定要移除该行吗？')
  batchAddList.value.splice(index, 1)
  message.success('移除成功')
}

/** 获取客户信息 */
const getCustomerInfo: any = debounce(async (queryString: string, cb: any, type: string) => {
  let query = {} as any
  query[type] = queryString
  const res = await CustomerApi.getCustomerList(query)
  if (type == 'code') {
    cb(res.map((item) => ({ value: item.code, label: item.name })))
  } else {
    cb(res.map((item) => ({ value: item.name, label: item.code })))
  }
}, 500)
/** 客户信息选择项 */
const handleCustomerSelect = (item: any, type: string) => {
  if (type == 'code') {
    formData.value.customerName = item.label
  } else {
    formData.value.customerCode = item.label
  }
}

/** 获取料品信息 */
const getItemInfo: any = debounce(async (queryString: string, cb: any) => {
  const res = await CustomerApi.getItemList(queryString)
  cb(res.map((item) => ({ value: item.itemCode, name: item.itemName, spec: item.spec })))
}, 500)
/** 获取料品信息 */
const handleItemSelect = (item: any) => {
  formData.value.itemName = item.name
  formData.value.spec = item.spec
}
const editBeforeValue = ref<any[]>([])

const isEdit = ref(false)
const onModify = () => {
  const row = unref(tableRef)?.getCheckboxRecords()
  if (!row || row.length == 0) {
    message.error('请选择要修改的行！')
    return
  }
  batchAddList.value = row
  editBeforeValue.value = cloneDeep(row)
  dialogVisiable.value = true
  isEdit.value = true // 设置为修改模式
}

const onDelete = () => {
  const row = unref(tableRef)?.getCheckboxRecords()
  if (!row || row.length == 0) {
    message.error('请选择要删除的行！')
    return
  }
  message.confirm('确定要删除选择的内容吗？').then(() => {
    console.log(row.map((item) => item.id))
    CustomerApi.batchDelete(row.map((item) => item.id))
    message.success('删除成功')
    getList()
  })
  console.log(row.map((item) => item.id))
}

const handleDownload = async () => {
  const data = await CustomerApi.export(queryParams.value)
  download.excel(data, '订单BOM跟进数据.xlsx')
}

onMounted(() => {
  getList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 10px !important;
}
:deep(.vxe-cell) {
  padding: 1px 1px !important;
}
:deep(.vxe-cell .el-input__wrapper),
:deep(.vxe-cell .el-select__wrapper) {
  box-shadow: none;
  border-radius: 0;
}

:deep(.el-tag--info),
:deep(.el-tag--success),
:deep(.el-tag--warning),
:deep(.el-tag--danger),
:deep(.el-tag--primary) {
  cursor: pointer;
}
</style>
