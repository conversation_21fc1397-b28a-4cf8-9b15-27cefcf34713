// main.js

import { VxeUI } from 'vxe-table'
import TextFilter from '@/components/FilterInput/TextFilter.vue'
import UserFilter from '@/components/FilterInput/UserFilter.vue'
import UserOrDeptFilter from '@/components/FilterInput/UserOrDeptFilter.vue'
import DateRangeFilter from '@/components/FilterInput/DateRangeFilter.vue'
import NumberFilter from '@/components/FilterInput/NumberFilter.vue'
import { useUserStore } from '@/store/modules/user'
import moment from 'moment'

// 创建一个简单的输入框筛选渲染器
VxeUI.renderer.add('TextFilter', {
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <TextFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = ''
    })
  },
  // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  tableFilterRecoverMethod ({ option }) {
    option.data = ''
  },
  // 自定义筛选方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    if (cellValue) {
      return cellValue.indexOf(data) > -1
    }
    return false
  }
})


// 创建一个简单的输入框筛选渲染器
VxeUI.renderer.add('DateRangeFilter', {
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <DateRangeFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = []
    })
  },
  // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  tableFilterRecoverMethod ({ option }) {
    option.data = []
  },
  // 自定义筛选方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    return moment(data[0]).isSameOrBefore(moment(cellValue))&&moment(data[1]).isSameOrAfter(moment(cellValue))
  }
})

// 创建一个简单的输入框筛选渲染器
VxeUI.renderer.add('NumberFilter', {
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <NumberFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = {condition:'12',value:undefined}
    })
  },
  // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  tableFilterRecoverMethod ({ option }) {
    option.data = {condition:'12',value:undefined}
  },
  // 自定义筛选方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    switch(data.condition){
      case '10':
        return cellValue>data.value
      case '11':
        return cellValue<data.value
      case '12':
        return cellValue==data.value
      case '13':
        return cellValue>=data.value
      case '13':
        return cellValue<=data.value
      case '13':
        return cellValue!=data.value
      default:
        return cellValue !=0
    }
  }
})


VxeUI.renderer.add('UserFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <UserFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'my', userList: [] }
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const {getUser}= useUserStore()
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { userList, type } = option.data
    if (cellValue) {
      if (type === 'my') {
        return cellValue.includes(getUser.id)
      }
      // 将过滤值转为 Set，提升查找效率
      const filterSet = new Set(userList);
      
      // 判断 cellValue 是否包含 filterSet 中的任意一个元素
      return cellValue.some(val => filterSet.has(val));
    }
    return false
  }
})

VxeUI.renderer.add('UserOrDeptFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <UserOrDeptFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'dept', deptList: [] }
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { userList,deptList, type } = option.data
    if (cellValue) {
      // 将过滤值转为 Set，提升查找效率
      const filterSet = new Set(type=='user'?userList:deptList);
      
      // 判断 cellValue 是否包含 filterSet 中的任意一个元素
      return cellValue.some(val => filterSet.has(val));
    }
    return false
  }
})