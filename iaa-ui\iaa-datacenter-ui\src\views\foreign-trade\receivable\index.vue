<template>
  <ContentWrap>
    <!-- 添加标签页 -->
    <el-tabs v-model="activeTab" class="mb-20px">
      <el-tab-pane label="应收金额" name="import" />
      <el-tab-pane label="应收报表" name="statements" />
    </el-tabs>
    <div v-if="activeTab === 'import'">
      <vxe-toolbar custom ref="toolbarRef" size="mini">
        <template #buttons>
          <el-button
            plain
            type="success"
            class="mr-10px"
            size="small"
            v-hasPermi="['collection:receivable:create']"
            @click="databaseUploadFormRef?.open()"
            title="上传"
          >
            上传
          </el-button>
        </template>
      </vxe-toolbar>

      <div class="h-[calc(100vh-290px)]">
        <vxe-table
          :row-config="{ height: 25, keyField: 'id' }"
          ref="tableRef"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :filter-config="{}"
          show-footer
          keep-source
          :footer-data="footerData"
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          :mouse-config="{ selected: true }"
          tabindex="0"
          size="mini"
          @filter-change="handleFilterChange"
        >
          <vxe-column type="checkbox" width="40" field="id" fixed="left" />
          <vxe-column
            field="customersCode"
            width="120"
            title="客户编码"
            :filters="codeOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="customersName"
            title="客户名称"
            width="120"
            :filters="nameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="dateStr"
            title="日期"
            width="120"
            :filters="dateStr"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column field="receivableType" title="费用类别" width="120">
            <template #default="{ row }">
              {{
                row.receivableType === 1
                  ? '订单'
                  : row.receivableType === 2
                    ? '费用'
                    : row.receivableType
              }}
            </template>
          </vxe-column>
          <vxe-column field="receivableProject" title="项目" min-width="220" />
          <vxe-column field="amount" title="费用金额" width="200" />
          <vxe-column title="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                v-hasPermi="['collection:receivable:delete']"
                @click="handleDelete(row.id)"
                link
                type="danger"
              >
                删除
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <div v-if="activeTab === 'statements'">
      <vxe-toolbar custom ref="toolbarRef" size="mini">
        <template #buttons>
          <el-button
            plain
            type="info"
            class="mr-10px"
            size="small"
            v-hasPermi="['collection:receivable:export']"
            @click="handleExport()"
            :loading="exportLoading"
            title="导出"
          >
            导出
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100vh-290px)]">
        <vxe-table
          :row-config="{ height: 27, keyField: 'id' }"
          ref="tableStatementsRef"
          :data="listStatements"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loadingStatements"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :filter-config="{}"
          show-footer
          keep-source
          :footer-data="footerDataStatements"
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          :mouse-config="{ selected: true }"
          tabindex="0"
          size="mini"
          @filter-change="handleFilterChange"
        >
          <vxe-column
            field="salesmanName"
            width="100"
            title="业务员"
            :filters="salesmanNameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="customersCode"
            width="100"
            title="客户编码"
            :filters="codeOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="customersName"
            width="100"
            title="客户"
            :filters="nameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="claimDate"
            width="100"
            title="日期"
            :filters="dateStr"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column
            field="type"
            width="100"
            title="类别"
            :filters="typeFilterOptions"
            :edit-render="{
              name: '$select',
              options: productsTypeOptions,
              props: { value: 'value', label: 'label' }
            }"
          >
            <template #default="{ row }">
              {{ row.type === 1 ? '订单' : row.type === 2 ? '费用' : row.type }}
            </template>
          </vxe-column>
          <vxe-column field="orderNo" title="项目（订单号/费用明细）" width="200" />
          <vxe-column field="currency" title="币种" width="120" />
          <vxe-column field="salesPrice" title="订单金额" width="120" />
          <vxe-column field="shipPrice" title="出货金额" width="120" />
          <vxe-column field="collectionAmount" title="收款金额" width="150" />
          <vxe-column field="collectionAmountLocal" title="收款金额（本币）" width="150" />
          <vxe-column field="receivableAmount" title="应收余额" min-width="120" />
          <vxe-column field="receivableAmountLocal" title="应收余额（本币）" min-width="120" />
        </vxe-table>
      </div>

      <!-- 分页 -->
      <Pagination
        :total="totalStatements"
        v-model:page="queryParamsStatements.pageNo"
        v-model:limit="queryParamsStatements.pageSize"
        @pagination="getListStatements"
      />
    </div>

    <DatabaseUploadForm ref="databaseUploadFormRef" @success="getList()" />
  </ContentWrap>
</template>
<script lang="ts" setup>
import DatabaseUploadForm from '@/views/foreign-trade/receivable/components/DatabaseUploadForm.vue'
import { FinancialApi } from '@/api/foreign-trade/receivable/index'
import * as FilterValue from '@/utils/Filter'
import * as FilterTemplate from '@/utils/Filter'
import type { VxeTablePropTypes } from 'vxe-table'
import download from '@/utils/download'

const codeOptions = ref([{ data: '' }])
const nameOptions = ref([{ data: '' }])
const salesmanNameOptions = ref([{ data: '' }])
const dateStr = ref([{ data: [] }])

// 添加固定的产品分类选项
const productsTypeOptions = ref([
  { label: '订单', value: 1 },
  { label: '费用', value: 2 }
])

// 添加筛选器选项
const typeFilterOptions = ref([
  { label: '订单', value: 1 },
  { label: '费用', value: 2 }
])
const databaseUploadFormRef = ref()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const activeTab = ref<'import' | 'statements'>('import')
const tableRef = ref()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 30,
  customersCode: undefined
})

const totalAmount = ref(0)
//应收合计行
const footerData = ref<VxeTablePropTypes.FooterData>([
  { receivableProject: '合计总和', amount: totalAmount }
])

const totalSalesPrice = ref(0)
const totalShipPrice = ref(0)
const totalCollectionAmount = ref(0)
const totalReceivableAmountLocal = ref(0)

//应收报表合计行
const footerDataStatements = ref<VxeTablePropTypes.FooterData>([
  {
    salesmanName: '合计总和',
    salesPrice: totalSalesPrice,
    shipPrice: totalShipPrice,
    collectionAmount: totalCollectionAmount,
    receivableAmountLocal: totalReceivableAmountLocal
  }
])

//报表
const tableStatementsRef = ref()
const loadingStatements = ref(true)
const listStatements = ref<any[]>([])
const totalStatements = ref(0)

const queryParamsStatements = reactive({
  pageNo: 1,
  pageSize: 30
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await FinancialApi.getReceivablePage(queryParams)
    list.value = res.list
    total.value = res.total
    totalAmount.value = list.value.reduce((total, item) => total + item.amount, 0).toFixed(3)
  } finally {
    loading.value = false
  }
}

const exportLoading = ref(false) // 导出的加载中
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await FinancialApi.exportInformation(queryParamsStatements)
    download.excel(data, `财务应收报表.xlsx`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const getListStatements = async () => {
  loadingStatements.value = true
  try {
    const res = await FinancialApi.getReceivableStatementsPage(queryParamsStatements)
    listStatements.value = res.list
    totalStatements.value = res.total
    totalSalesPrice.value = listStatements.value
      .reduce((total, item) => total + item.salesPrice, 0)
      .toFixed(4)
    totalShipPrice.value = listStatements.value
      .reduce((total, item) => total + item.shipPrice, 0)
      .toFixed(4)
    totalCollectionAmount.value = listStatements.value
      .reduce((total, item) => total + item.collectionAmount, 0)
      .toFixed(4)
    totalReceivableAmountLocal.value = listStatements.value
      .reduce((total, item) => total + item.receivableAmountLocal, 0)
      .toFixed(4)
  } finally {
    loadingStatements.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  // 根据默认激活的标签页加载数据
  if (activeTab.value === 'import') {
    getList()
  } else if (activeTab.value === 'statements') {
    getListStatements()
  }
})

const handleFilterChange = (params: any) => {
  // 初始化 filters 对象
  const filters = {}
  // 特定字段列表
  const specialFields = ['type']
  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 调用后端接口获取数据
  if (activeTab.value === 'import') {
    // 清空不在 params.filterList 中的字段
    Object.keys(queryParams).forEach((key) => {
      if (
        !['pageNo', 'pageSize'].includes(key) &&
        !params.filterList.some((item: any) => item.field === key)
      ) {
        queryParams[key] = undefined
      }
    })
    // 更新 queryParams
    Object.assign(queryParams, filters)
    getList()
  } else {
    // 清空不在 params.filterList 中的字段
    Object.keys(queryParamsStatements).forEach((key) => {
      if (
        !['pageNo', 'pageSize'].includes(key) &&
        !params.filterList.some((item: any) => item.field === key)
      ) {
        queryParamsStatements[key] = undefined
      }
    })
    // 更新 queryParams
    Object.assign(queryParamsStatements, filters)
    getListStatements()
  }
}

watch(activeTab, (newVal) => {
  if (newVal === 'import') {
    resetQueryParams(queryParams)
    getList()
  } else if (newVal === 'statements') {
    resetQueryParams(queryParamsStatements)
    getListStatements()
  }
})
/** 删除 */
const handleDelete = async (row: any) => {
  message.confirm('是否确认删除？').then(async () => {
    await FinancialApi.deleteReceivable([row])
    message.success('删除成功')
    getList()
  })
}

// 通用的重置查询参数函数
const resetQueryParams = (params: Record<string, any>) => {
  Object.keys(params).forEach((key) => {
    if (!['pageNo', 'pageSize'].includes(key)) {
      params[key] = undefined
    }
  })
  params.pageNo = 1
}
</script>
