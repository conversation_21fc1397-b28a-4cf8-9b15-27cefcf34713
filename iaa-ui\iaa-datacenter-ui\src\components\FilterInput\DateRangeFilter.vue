<template>
  <div v-if="currOption" class="my-filter-input">
    <el-date-picker
      type="daterange"
      value-format="YYYY-MM-DD"
      v-model="currOption.data"
      placeholder="支持回车筛选"
      :shortcuts="shortcuts"
      @keyup.enter="keyupEvent"
      @change="changeOptionEvent"
    />
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { watch, ref, computed } from 'vue'
import type { VxeTableDefines } from 'vxe-table'

const props = defineProps({
  renderParams: propTypes.any.def({})
})

const currOption = ref<VxeTableDefines.FilterOption>()

const currField = computed(() => {
  const { column } = props.renderParams || {}
  return column ? column.field : ''
})

const shortcuts = [
  {
    text: '前一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '前一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '前三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '本年',
    value: () => {
      const end = new Date()
      const start = new Date(end.getFullYear(), 0, 1)
      return [start, end]
    }
  },
  {
    text: '前一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

const load = () => {
  const { renderParams } = props
  if (renderParams) {
    const { column } = renderParams
    const option = column.filters[0]
    currOption.value = option
  }
}

const changeOptionEvent = () => {
  const { renderParams } = props
  const option = currOption.value
  if (renderParams && option) {
    const { $table } = renderParams
    const checked = !!option.data
    $table.updateFilterOptionStatus(option, checked)
  }
}

const keyupEvent = ($event) => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.confirmFilterEvent($event)
  }
}

watch(currField, () => {
  load()
})

load()
</script>

<style scoped>
.my-filter-input {
  padding: 10px;
}
</style>
