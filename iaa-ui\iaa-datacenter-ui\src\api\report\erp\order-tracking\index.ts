import request from '@/config/axios'

export const OrderTrackingApi = {
  /** 获取订单跟踪设置信息 */
  getOrderTrackingEdit: () => {
    return request.get({ url: '/report/order-tracking/get-order-tracking-edit' })
  },
  /** 保存订单跟踪配置信息 */
  saveOrderTrackingEdit: (data: any) => {
    return request.post({ url: '/report/order-tracking/save-order-tracking-edit', data })
  },
  /** 分页获取订单跟踪配置信息 */
  getOrderTrackingPage: (data: any) => {
    return request.post({ url: '/report/order-tracking/get-order-tracking-page', data })
  },
  /** 根据销售订单号和品号获取工单详情 */
  getMoDetails: (demand: number) => {
    return request.get({
      url: '/report/order-tracking/get-mo-details',
      params: { demand }
    })
  },
  /** 根据销售订单号和品号获取工单备料详情 */
  getMoPickDetails: (demand: number) => {
    return request.get({
      url: '/report/order-tracking/get-mo-pick-details',
      params: { demand }
    })
  },
  /** 根据销售订单号和品号获取报工详情 */
  getProductionDetails: (type: number, docNo: string, itemCode: string) => {
    return request.get({
      url: '/report/order-tracking/get-production-details',
      params: { type, docNo, itemCode }
    })
  },
  /** 根据销售订单号和品号获取成品入库详情 */
  getCompleteDetails: (demand: number) => {
    return request.get({
      url: '/report/order-tracking/get-complete-details',
      params: { demand }
    })
  },
  /** 根据销售订单号和品号获取出货详情 */
  getShipDetails: (demand: number) => {
    return request.get({
      url: '/report/order-tracking/get-ship-details',
      params: { demand }
    })
  },
  getOutDetails: (docNo: string, itemCode: string) => {
    return request.get({
      url: '/report/order-tracking/get-out-details',
      params: { docNo, itemCode }
    })
  }
}
