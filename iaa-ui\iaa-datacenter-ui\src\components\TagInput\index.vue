<template>
  <div class="tag-input-wrapper">
    <div class="input-tag-container">
      <div
        ref="editableDiv"
        contenteditable="true"
        @input="handleInput"
        @keydown="handleKeydown"
        @blur="handleBlur"
        @paste="handlePaste"
        class="editable-content"
        :class="{ 'empty': isEmpty }"
        v-html="displayContent"
      ></div>
      <div v-if="isEmpty" class="placeholder">{{ placeholder }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface ParsedItem {
  type: 'text' | 'tag'
  value: string
}

interface Props {
  modelValue?: string
  placeholder?: string
  tagStyles?: Record<string, string>
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', parsedContent: ParsedItem[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '输入内容，使用 {tag} 表示标签',
  tagStyles: () => ({})
})

const emit = defineEmits<Emits>()

const editableDiv = ref<HTMLElement | null>(null)
const rawContent = ref(props.modelValue)
const parsedContent = ref<ParsedItem[]>([])

// 正则匹配 {tag} 格式
const tagRegex = /{([^{}]+)}/g

// 转换为可显示的 HTML 内容
const displayContent = computed(() => {
  if (!rawContent.value) return ''

  return rawContent.value.replace(
    tagRegex,
    (_, tagValue) => {
      // 检查是否有特殊类型的tag
      let tagClass = 'tag'
      
      // 根据tag内容添加不同的样式类
      if (tagValue.startsWith('user:')) {
        tagClass += ' tag-user'
      } else if (tagValue.startsWith('system:')) {
        tagClass += ' tag-system'
      } else if (tagValue.startsWith('error:')) {
        tagClass += ' tag-error'
      } else if (tagValue.startsWith('ai:')) {
        tagClass += ' tag-ai'
      } else if (tagValue.startsWith('data:')) {
        tagClass += ' tag-data'
      }
      
      return `<span class="${tagClass}">${tagValue}</span>`
    }
  )
})

// 判断是否为空
const isEmpty = computed(() => {
  return !rawContent.value || rawContent.value.trim() === ''
})

// 解析内容为结构化数据
const parseContent = () => {
  const content = rawContent.value
  if (!content) {
    parsedContent.value = []
    return
  }

  const result: ParsedItem[] = []
  let lastIndex = 0
  let match: RegExpExecArray | null

  // 重置正则表达式的 lastIndex
  tagRegex.lastIndex = 0

  // 按 tag 分割内容
  while ((match = tagRegex.exec(content)) !== null) {
    const [fullMatch, tagValue] = match
    
    // 添加前面的文本
    if (match.index > lastIndex) {
      result.push({
        type: 'text',
        value: content.slice(lastIndex, match.index)
      })
    }
    
    // 添加 tag
    result.push({
      type: 'tag',
      value: tagValue
    })
    
    lastIndex = match.index + fullMatch.length
  }

  // 添加最后的文本
  if (lastIndex < content.length) {
    result.push({
      type: 'text',
      value: content.slice(lastIndex)
    })
  }

  parsedContent.value = result
  emit('change', result)
}

// 处理输入事件
const handleInput = () => {
  if (editableDiv.value) {
    rawContent.value = editableDiv.value.innerText
    parseContent()
    emit('update:modelValue', rawContent.value)
  }
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Backspace') {
    handleBackspace(event)
  }
}

// 处理退格键删除tag
const handleBackspace = (event: KeyboardEvent) => {
  if (!editableDiv.value) return

  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)
  
  // 如果有选中内容，让浏览器默认处理
  if (!range.collapsed) return

  const { startContainer, startOffset } = range

  // 情况1: 光标在tag元素内部
  if (startContainer.nodeType === Node.TEXT_NODE && startContainer.parentElement?.classList.contains('tag')) {
    event.preventDefault()
    const tagElement = startContainer.parentElement
    tagElement.remove()
    updateContent()
    return
  }

  // 情况2: 光标在tag元素前面
  if (startContainer.nodeType === Node.ELEMENT_NODE) {
    const elementAtCursor = startContainer.childNodes[startOffset - 1]
    if (elementAtCursor && elementAtCursor.nodeType === Node.ELEMENT_NODE && 
        (elementAtCursor as Element).classList.contains('tag')) {
      event.preventDefault()
      elementAtCursor.remove()
      updateContent()
      return
    }
  }

  // 情况3: 光标在文本节点中，检查前面是否有tag
  if (startContainer.nodeType === Node.TEXT_NODE && startOffset === 0) {
    const previousSibling = startContainer.previousSibling
    if (previousSibling && previousSibling.nodeType === Node.ELEMENT_NODE && 
        (previousSibling as Element).classList.contains('tag')) {
      event.preventDefault()
      previousSibling.remove()
      updateContent()
      return
    }
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  
  // 插入纯文本
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents()
    range.insertNode(document.createTextNode(text))
    range.collapse(false)
    
    updateContent()
  }
}

// 更新内容的辅助函数
const updateContent = () => {
  if (editableDiv.value) {
    rawContent.value = editableDiv.value.innerText
    parseContent()
    emit('update:modelValue', rawContent.value)
    // 重新渲染HTML以确保tag样式正确
    setTimeout(() => {
      if (editableDiv.value) {
        editableDiv.value.innerHTML = displayContent.value
      }
    }, 0)
  }
}

// 处理失去焦点
const handleBlur = () => {
  if (editableDiv.value) {
    rawContent.value = editableDiv.value.innerText
    parseContent()
    emit('update:modelValue', rawContent.value)
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== rawContent.value) {
    rawContent.value = newValue
    parseContent()
    if (editableDiv.value) {
      editableDiv.value.innerHTML = displayContent.value
    }
  }
})

// 初始化
parseContent()
</script>

<style scoped>
.tag-input-wrapper {
  width: 100%;
}

.input-tag-container {
  position: relative;
  min-height: 60px;
  border: 1px solid #e4e4e4;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  background: #fff;
  transition: border-color 0.2s ease;
}

.input-tag-container:focus-within {
  border-color: #00D4FF;
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
}

.editable-content {
  outline: none;
  min-height: 44px;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.5;
}

.placeholder {
  position: absolute;
  top: 10px;
  left: 12px;
  color: #999;
  pointer-events: none;
  user-select: none;
}

.tag {
  display: inline-block;
  background: #00D4FF;
  color: #000;
  padding: 2px 8px;
  margin: 0 2px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.4;
  cursor: default;
  user-select: none;
  vertical-align: middle;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.tag:hover {
  background: #00B8E6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 212, 255, 0.2);
}

/* 不同类型的tag样式 */
.tag.tag-user {
  background: #52c41a;
  color: #fff;
  border-color: rgba(82, 196, 26, 0.3);
}

.tag.tag-system {
  background: #fa8c16;
  color: #fff;
  border-color: rgba(250, 140, 22, 0.3);
}

.tag.tag-error {
  background: #ff4d4f;
  color: #fff;
  border-color: rgba(255, 77, 79, 0.3);
}

.tag.tag-ai {
  background: #722ed1;
  color: #fff;
  border-color: rgba(114, 46, 209, 0.3);
}

.tag.tag-data {
  background: #13c2c2;
  color: #fff;
  border-color: rgba(19, 194, 194, 0.3);
}
</style>
