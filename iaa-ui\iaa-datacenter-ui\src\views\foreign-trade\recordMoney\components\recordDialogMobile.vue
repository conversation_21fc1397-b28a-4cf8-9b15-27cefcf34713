<!-- src/views/foreign-trade/recordMoney/components/recordDialogMobile.vue -->
<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    round
    :style="{ height: '90%', padding: '12px' }"
    :closeable="true"
    @click-overlay="onClickOverlay"
    @click-close-icon="onClickOverlay"
  >
    <div class="h-full flex flex-col">
      <div class="text-center text-5 font-600 mb-8px">
        {{ readOnly ? '录款详情' : '新增录款' }}</div
      >
      <div class="flex-1 overflow-auto pr-4px">
        <van-cell-group inset>
          <VDatePicker
            v-model="form.claimDate"
            label="日期"
            style="margin-left: -10px; margin-bottom: -18px"
            :readonly="readOnly || !isDateDisabled"
          />
          <van-field
            v-model="form.salesmanName"
            label="业务员"
            placeholder="默认当前登录人"
            :readonly="readOnly"
          />
          <van-field
            :model-value="customerDisplay"
            label="客户名称"
            is-link
            readonly
            placeholder="输入选择"
            @click="!readOnly && openCustomerPicker()"
          />
          <van-field v-model="form.customerCode" label="客户编码" readonly />
          <van-field
            :model-value="form.currencyName || form.currency"
            label="币种"
            is-link
            readonly
            placeholder="请选择币种"
            @click="!readOnly && openCurrencyPicker()"
          />
          <van-field v-model="form.totalAmount" label="总金额" readonly />

          <!-- 收款账户 -->
          <van-field
            v-model="form.collectionAccount"
            label="收款账户"
            is-link
            readonly
            placeholder="请选择收款账户"
            @click="!readOnly && openAccountPicker()"
          />
          <van-field
            v-model="form.remark"
            label="备注"
            :readonly="readOnly"
          />
          <!-- 信保账号说明（仅在收款账户为"信保（美金）"时显示） -->
          <van-field
            v-if="form.collectionAccount === '信保（美金）'"
            :model-value="form.creditInsurance"
            label="信保账号说明"
            is-link
            readonly
            placeholder="请选择信保账号说明"
            @click="!readOnly && openCreditInsurancePicker()"
          />

          <!-- 客户水单上传 -->
          <van-field label="客户水单">
            <template #input>
              <van-uploader
                v-model="fileList"
                :max-count="5"
                multiple
                :after-read="afterRead"
                :before-delete="beforeDelete"
              />
            </template>
          </van-field>
        </van-cell-group>

        <van-divider>订单明细</van-divider>
        <div class="px-10px">
          <div
            v-for="(row, idx) in orders"
            :key="idx"
            class="mb-10px border rounded p-10px bg-#fff"
          >
            <van-field
              :model-value="row.orderNo"
              label="订单号"
              is-link
              readonly
              placeholder="选择订单"
              @click="!readOnly && openOrderPicker(idx)"
            />
            <van-field v-model.number="row.orderAmount" label="订单金额" type="number" readonly />
            <!-- <van-field
              v-model.number="row.shipAmount"
              label="已出货金额"
              type="number"
              :readonly="true"
            /> -->
            <van-field
              v-model.number="row.remainingAmount"
              label="剩余认款余额"
              type="number"
              :readonly="true"
            />
            <van-field
              v-model.number="row.claimRatio"
              label="收款比例(%)"
              type="number"
              :readonly="readOnly"
              @input="!readOnly && handleRatioInput(row, $event)"
            />
            <van-field
              v-model.number="row.amount"
              label="认款金额"
              type="number"
              placeholder="≤订单金额"
              :readonly="readOnly"
              @input="!readOnly && handleAmountInput(row, $event)"
            />
            <div class="text-right mt-6px" v-if="!readOnly">
              <van-button size="small" type="danger" @click="orders.splice(idx, 1)"
                >删除</van-button
              >
            </div>
          </div>
          <van-button block type="primary" size="small" plain @click="addOrder" v-if="!readOnly"
            >+ 添加订单</van-button
          >
        </div>

        <van-divider>费用类别</van-divider>
        <div class="px-10px">
          <div
            v-for="(row, idx) in expenses"
            :key="idx"
            class="mb-10px border rounded p-10px bg-#fff"
          >
            <van-field
              :model-value="getExpenseTypeLabel(row.expenseType)"
              label="费用类别"
              placeholder="请选择费用类别"
              readonly
              is-link
              @click="!readOnly && openExpenseTypePicker(idx)"
            />
            <van-field
              v-model.number="row.amount"
              label="金额"
              type="number"
              :readonly="readOnly"
            />
            <van-field v-model="row.expenseRemark" label="备注" :readonly="readOnly" />
            <div class="text-right mt-6px" v-if="!readOnly">
              <van-button size="small" type="danger" @click="expenses.splice(idx, 1)"
                >删除</van-button
              >
            </div>
          </div>
          <van-button block type="primary" size="small" plain @click="addExpense" v-if="!readOnly"
            >+ 添加费用</van-button
          >
        </div>

        <van-divider>订单未下</van-divider>
        <div class="px-10px">
          <div
            v-for="(row, idx) in ordersUnsettled"
            :key="idx"
            class="mb-10px border rounded p-10px bg-#fff"
          >
            <van-field
              v-model.number="row.amount"
              label="金额"
              type="number"
              :readonly="readOnly"
            />
          </div>
        </div>
      </div>

      <!-- 客户选择 Picker -->
      <van-popup
        v-model:show="customerPickerShow"
        position="bottom"
        round
        :style="{ height: '60%' }"
      >
        <van-picker
          :columns="customerColumns"
          :loading="customerLoading"
          @confirm="onCustomerConfirm"
          @cancel="customerPickerShow = false"
        >
          <template #columns-top>
            <div class="p-10px">
              <van-field
                v-model="customerKeyword"
                placeholder="输入客户名称检索"
                clearable
                @update:model-value="fetchCustomerColumns"
              />
            </div>
          </template>
        </van-picker>
      </van-popup>

      <!-- 订单选择 Picker -->
      <van-popup v-model:show="orderPickerShow" position="bottom" round :style="{ height: '60%' }">
        <van-picker
          :columns="orderColumns"
          :loading="orderLoading"
          @confirm="onOrderConfirm"
          @cancel="orderPickerShow = false"
        >
          <template #columns-top>
            <div class="p-10px">
              <van-field
                v-model="orderKeyword"
                placeholder="输入订单号检索"
                clearable
                @update:model-value="fetchOrderColumns"
              />
            </div>
          </template>
        </van-picker>
      </van-popup>

      <!-- 币种选择 Picker -->
      <van-popup
        v-model:show="currencyPickerShow"
        round
        position="bottom"
        :style="{ height: '60%' }"
      >
        <van-picker
          :columns="currencyColumns"
          @cancel="currencyPickerShow = false"
          @confirm="onCurrencyConfirm"
        />
      </van-popup>

      <!-- 信保账号说明选择器 -->
      <van-popup
        v-model:show="creditInsurancePickerShow"
        round
        position="bottom"
        :style="{ height: '60%' }"
      >
        <van-picker
          :columns="creditInsuranceColumns"
          @cancel="creditInsurancePickerShow = false"
          @confirm="onCreditInsuranceConfirm"
        />
      </van-popup>

      <!-- 收款账户选择 Picker -->
      <van-popup
        v-model:show="accountPickerShow"
        round
        position="bottom"
        :style="{ height: '60%' }"
      >
        <van-picker
          :columns="accountColumns"
          @cancel="accountPickerShow = false"
          @confirm="onAccountConfirm"
        />
      </van-popup>

      <!-- 费用类别选择器弹窗 -->
      <van-popup
        v-model:show="expenseTypePickerShow"
        position="bottom"
        round
        :style="{ height: '60%' }"
        v-if="!readOnly"
      >
        <van-picker
          :columns="expenseTypeColumns"
          @confirm="onExpenseTypeConfirm"
          @cancel="expenseTypePickerShow = false"
        />
      </van-popup>

      <div class="pt-10px" v-if="!readOnly && !props.isEdit">
        <van-row :gutter="20">
          <van-col span="24"
            ><span class="mr-15px text-xs">已填写总金额：{{ totalClaimed }}</span></van-col
          >
          <van-col span="12">
            <van-button
              type="warning"
              block
              size="small"
              :loading="tempSaving"
              @click="handleTempSave"
              >暂存</van-button
            >
          </van-col>
          <van-col span="12">
            <van-button
              type="primary"
              block
              size="small"
              :loading="submitting"
              @click="handleSubmit"
              >确认提交</van-button
            >
          </van-col>
        </van-row>
      </div>

      <div class="pt-10px" v-else-if="!readOnly && props.isEdit">
        <van-row :gutter="20">
          <van-col span="24"
            ><span class="mr-15px text-xs">已填写总金额：{{ totalClaimed }}</span></van-col
          >
          <van-col span="24">
            <van-button
              type="primary"
              block
              size="small"
              :loading="submitting"
              @click="handleSubmit"
              >确认提交</van-button
            >
          </van-col>
        </van-row>
      </div>

      <!-- 只读模式下显示关闭按钮 -->
      <div class="pt-10px" v-else>
        <van-button type="primary" block size="small" @click="show = false">关闭</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts" setup>
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import { CollectionDetailApi } from '@/api/foreign-trade/collectionDetails'
import { updateFile } from '@/api/infra/file/index'
import { getAccount } from '@/api/foreign-trade/account/index'
import { useUserStore } from '@/store/modules/user'
import VDatePicker from '@/components/Mobile/VDatePicker.vue'
import { showSuccessToast, showFailToast } from 'vant'
import type { UploaderFileListItem } from 'vant'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import dayjs from 'dayjs'

const userStore = useUserStore()

const props = defineProps<{
  show: boolean
  id?: number
  readOnly?: boolean
  isAdd?: boolean
  isEdit?: boolean
}>()

const emits = defineEmits(['update:show', 'success'])
const show = computed({ get: () => props.show, set: (v) => emits('update:show', v) })

// 添加币种映射
const currencyMap = ref<Record<string, string>>({})
// 表单数据
const form = reactive<any>({
  claimDate: dayjs().format('YYYY-MM-DD'),
  type: 2,
  status: 0,
  salesmanName: userStore.user?.nickname || '',
  customerName: '',
  customerCode: '',
  totalAmount: 0.0,
  currency: '', // 币种
  currencyCode: '', // 币种Code
  currencyName: '', // 币种Name（用于显示）
  customerList: [], // 客户水单，用于提交给后端
  collectionAccount: '',
  creditInsurance: '',
  collectionId: '',
  remark: ''
})

// 图片上传相关 - 专门用于uploader组件显示的数组
const fileList = ref<UploaderFileListItem[]>([])

// 处理图片上传
const afterRead = async (file: UploaderFileListItem) => {
  if (!file.file) return

  const formData = new FormData()
  formData.append('file', file.file)

  try {
    const response = await updateFile(formData)

    if (response.code === 0 && response.data) {
      // 上传成功，保存URL到form.customerList（数组类型）
      const url = response.data.url
      form.customerList.push(url) // 添加到数组中

      // 更新文件列表项的状态，用于UI显示
      if (file && typeof file === 'object') {
        file.url = url
        file.status = 'done'
      }
    } else {
      showFailToast('图片上传失败')
      if (file && typeof file === 'object') {
        file.status = 'failed'
      }
    }
  } catch (err) {
    console.error('图片上传失败:', err)
    showFailToast('图片上传失败')
    if (file && typeof file === 'object') {
      file.status = 'failed'
    }
  }
}

// 处理图片删除
const beforeDelete = (file: UploaderFileListItem, detail: { index: number }) => {
  // 删除图片时从form.customerList中移除对应URL
  const index = detail.index
  if (index >= 0 && index < form.customerList.length) {
    form.customerList.splice(index, 1)
  }
  return true
}

// 订单和费用数据
const orders = ref<any[]>([])
const expenses = ref<any[]>([])
const ordersUnsettled = ref<any[]>([{ type: 3, amount: 0 }])

// 添加订单
const addOrder = () => {
  if (props.readOnly) return
  orders.value.push({
    type: 1,
    orderNo: '',
    orderAmount: 0,
    amount: 0,
    remainingAmount: 0,
    currency: form.currency || '美元',
    claimRatio: 100
  })
}

// 添加费用
const addExpense = () => {
  if (props.readOnly) return
  expenses.value.push({
    type: 2,
    expenseType: '',
    expenseRemark: '',
    amount: 0,
    currency: form.currency || '美元'
  })
}

// 计算总金额
const claimTotalAmount = computed(() => {
  const orderAmountTotal = orders.value.reduce((sum, order) => {
    return sum + Number(order.amount || 0)
  }, 0)

  const expenseAmountTotal = expenses.value.reduce((sum, expense) => {
    return sum + Number(expense.amount || 0)
  }, 0)

  const ordersUnsettledAmount = ordersUnsettled.value.reduce((sum, item) => {
    return sum + Number(item.amount || 0)
  }, 0)

  // 返回总和
  return orderAmountTotal + expenseAmountTotal + ordersUnsettledAmount
})

const totalClaimed = ref(0)
// 监听订单和费用变化，更新总金额
watch(
  [orders, expenses, ordersUnsettled],
  () => {
    form.totalAmount = claimTotalAmount.value
    totalClaimed.value = claimTotalAmount.value
  },
  { deep: true }
)

// 监听主表币种变化，同步更新费用项币种
watch(
  () => form.currency,
  (newCurrency) => {
    if (newCurrency) {
      expenses.value.forEach((expense) => {
        expense.currency = newCurrency
      })
    }
  }
)

// 客户相关
const customerPickerShow = ref(false)
const customerKeyword = ref('')
const customerOptions = ref<any[]>([])
const customerColumns = ref<any[]>([])
const customerLoading = ref(false)
const customerDisplay = computed(() =>
  form.customerName && form.customerCode ? `${form.customerName}` : ''
)

const openCustomerPicker = async () => {
  if (props.readOnly) return
  customerPickerShow.value = true
  await fetchCustomerColumns()
}

const fetchCustomerColumns = async () => {
  customerLoading.value = true
  try {
    const res: any = await ClaimApi.getCustomer(customerKeyword.value || '')
    customerOptions.value = res || []
    customerColumns.value = customerOptions.value.map((it: any) => ({
      text: `${it.name}-${it.code}`,
      value: it
    }))
  } finally {
    customerLoading.value = false
  }
}

const isDateDisabled = ref(false)
const onCustomerConfirm = ({ selectedValues }) => {
  const item = selectedValues?.[0]
  if (!item) return
  form.customerName = item.name
  form.customerCode = item.code
  customerPickerShow.value = false
  orders.value = []
}

// 订单相关
const orderPickerShow = ref(false)
const orderKeyword = ref('')
const orderOptions = ref<any[]>([])
const orderColumns = ref<any[]>([])
const orderLoading = ref(false)
let currentOrderIndex = -1

const openOrderPicker = (index: number) => {
  if (props.readOnly) return
  if (!form.customerCode) {
    showFailToast('请先选择客户')
    return
  }
  orderKeyword.value = ''
  currentOrderIndex = index
  orderPickerShow.value = true
  fetchOrderColumns()
}

const fetchOrderColumns = async () => {
  orderLoading.value = true
  try {
    const res: any = await ClaimApi.getOrders({
      code: form.customerCode,
      DocNo: orderKeyword.value
    })
    orderOptions.value = res || []
    orderColumns.value = orderOptions.value.map((it: any) => ({
      text: `${it.DocNo}(${it.currency}-${it.salesPrice})`,
      value: it
    }))
  } finally {
    orderLoading.value = false
  }
}

const onOrderConfirm = ({ selectedValues }) => {
  const item = selectedValues?.[0]
  if (!item || currentOrderIndex < 0) return

  // 检查币种是否一致
  if (form.currency && item.currency) {
    // 使用form.currency(币种名称)进行比较
    if (form.currency !== item.currency) {
      showFailToast(
        `所选订单币种为 ${item.currency}，与当前选择的币种 ${form.currency} 不一致，无法选择`
      )
      return
    }
  }

  const row = orders.value[currentOrderIndex]
  row.orderNo = item.DocNo
  row.orderAmount = item.salesPrice
  row.shipAmount = item.shipPrice || 0
  row.remainingAmount = item.remainingAmount
  row.amount = item.remainingAmount
  row.currency = item.currency
  row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
  orderPickerShow.value = false
}

// 处理收款比例输入
const handleRatioInput = (row: any, event: Event) => {
  const input = event.target as HTMLInputElement
  const ratio = parseFloat(input.value) || 0

  // 根据收款比例计算认款金额，使用四舍五入
  const calculatedAmount = Math.round((row.orderAmount * ratio) / 100)
  row.amount = calculatedAmount

  // 确保认款金额不超过剩余可认款金额
  if (row.amount > row.remainingAmount) {
    row.amount = row.remainingAmount
    // 重新计算收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
    showFailToast('认款金额不能超过剩余可认款金额，收款比例已自动调整')
  }
}

// 处理认款金额输入
const handleAmountInput = (row: any, event: Event) => {
  const input = event.target as HTMLInputElement
  const value = parseFloat(input.value) || 0

  if (value > row.remainingAmount) {
    row.amount = row.remainingAmount
    // 同步更新收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
    showFailToast('认款金额不能超过剩余可认款金额，收款比例已自动调整')
  } else {
    // 同步更新收款比例，使用四舍五入
    if (row.orderAmount > 0) {
      row.claimRatio = Math.round((row.amount / row.orderAmount) * 100)
    }
  }
}

// 信保账号说明选择
const creditInsurancePickerShow = ref(false)
const creditInsuranceColumns = [
  { text: 'ScentaChina', value: 'ScentaChina' },
  { text: 'ScentMachine', value: 'ScentMachine' },
  { text: 'ScentMarketing', value: 'ScentMarketing' }
]

const openCreditInsurancePicker = () => {
  if (props.readOnly) return
  creditInsurancePickerShow.value = true
}

const onCreditInsuranceConfirm = ({ selectedValues }: { selectedValues: string[] }) => {
  const value = selectedValues?.[0]
  if (!value) return
  form.creditInsurance = value
  creditInsurancePickerShow.value = false
}

// 币种选择
const currencyPickerShow = ref(false)
const currencyColumns = ref<any[]>([])

// 在组件加载时预加载币种数据
const fetchCurrencyList = async () => {
  try {
    const res = await ClaimApi.getCurrency()
    const currencyData = res || []

    // 构建映射关系
    const map: Record<string, string> = {}
    currencyColumns.value = currencyData.map((item: any) => {
      map[item.Code] = item.Name
      return {
        text: item.Name,
        value: item.Code
      }
    })
    currencyMap.value = map
  } catch (err) {
    console.error('获取币种列表失败:', err)
    showFailToast('获取币种列表失败')
  }
}
const openCurrencyPicker = async () => {
  if (props.readOnly) return
  currencyPickerShow.value = true
}

const onCurrencyConfirm = ({ selectedValues }) => {
  const code = selectedValues?.[0]
  if (!code) return

  // 根据编码查找对应的币种对象
  const selectedCurrency = currencyColumns.value.find((item) => item.value === code)

  if (selectedCurrency) {
    form.currency = selectedCurrency.text // 币种名称
    form.currencyCode = code // 币种编码
    form.currencyName = selectedCurrency.text // 显示名称
  }
  currencyPickerShow.value = false
}

// 收款账户选择
const accountPickerShow = ref(false)
const accountColumns = ref<any[]>([])
const accountList = ref<any[]>([])

// 预加载账户数据
const fetchAccountList = async () => {
  try {
    const res: any = await getAccount()
    accountList.value = res || []
    accountColumns.value = accountList.value.map((item) => ({
      text: item.accountName,
      value: item
    }))
  } catch (err) {
    console.error('获取账户列表失败:', err)
    showFailToast('获取账户列表失败')
  }
}
const openAccountPicker = async () => {
  if (props.readOnly) return
  accountPickerShow.value = true
  // 不再在这里调用接口
}

const onAccountConfirm = ({ selectedValues }) => {
  const item = selectedValues?.[0]
  if (!item) return
  form.collectionAccount = item.accountName
  form.collectionId = item.accountId
  accountPickerShow.value = false
}

// 费用类别选择
const expenseTypePickerShow = ref(false)

const expenseTypeColumns = computed(() => {
  const dictOptions = getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)
  return dictOptions.map((dict) => ({
    text: dict.label,
    value: dict.value.toString() // 确保值是字符串类型
  }))
})

let currentExpenseTypeIndex = -1

// 添加获取费用类型标签的方法
const getExpenseTypeLabel = (value: string | number) => {
  if (value === undefined || value === null || value === '') return ''
  const dictOptions = getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)
  // 使用 == 比较以处理数字和字符串的比较
  const dict = dictOptions.find((option) => option.value == value)
  return dict ? dict.label : typeof value === 'number' ? value.toString() : value
}
const openExpenseTypePicker = (index: number) => {
  if (props.readOnly) return
  currentExpenseTypeIndex = index
  expenseTypePickerShow.value = true
}

const onExpenseTypeConfirm = ({ selectedValues }: { selectedValues: string[] }) => {
  const value = selectedValues?.[0]
  if (value === undefined || value === null || currentExpenseTypeIndex < 0) return

  const row = expenses.value[currentExpenseTypeIndex]
  row.expenseType = value
  expenseTypePickerShow.value = false
}

// 表单状态
const claimIdRef = ref<number | null>(null)
const submitting = ref(false)
const tempSaving = ref(false)

// 构建明细列表
const buildDetailList = () => [
  ...orders.value.map((o) => ({
    type: 1,
    orderNo: o.orderNo,
    orderAmount: o.orderAmount,
    amount: Number(o.amount || 0),
    remainingAmount: Number(o.remainingAmount || 0),
    shipAmount: Number(o.shipAmount || 0)
  })),
  ...expenses.value.map((e) => ({
    type: 2,
    expenseType: e.expenseType,
    expenseRemark: e.expenseRemark,
    amount: Number(e.amount || 0)
  })),
  // 订单未下金额直接提交，不管是否为0（但通常只有大于0才会提交）
  ...ordersUnsettled.value
    .map((o) => ({
      type: 3,
      amount: Number(o.amount || 0)
    }))
    .filter((item) => item.amount > 0) // 只提交大于0的记录
]

// 表单验证
const validateForm = (): boolean => {
  if (!form.claimDate) {
    showFailToast('日期不能为空')
    return false
  }

  if (!form.salesmanName) {
    showFailToast('业务员不能为空')
    return false
  }

  if (!form.customerName || !form.customerCode) {
    showFailToast('客户名称和客户编码不能为空')
    return false
  }

  if (!form.currency) {
    showFailToast('币种不能为空')
    return false
  }

  if (!form.collectionAccount) {
    showFailToast('收款账户不能为空')
    return false
  }

  if (form.collectionAccount === '信保（美金）') {
    if (!form.creditInsurance) {
      showFailToast('请选择信保账号说明')
      return false
    }
  }
  if (form.totalAmount <= 0) {
    showFailToast('总金额必须大于0')
    return false
  }

  for (let i = 0; i < orders.value.length; i++) {
    const order = orders.value[i]
    if (!order.orderNo) {
      showFailToast(`第${i + 1}条订单明细的订单号不能为空`)
      return false
    }
    if (order.amount <= 0) {
      showFailToast(`第${i + 1}条订单明细的认款金额必须大于0`)
      return false
    }
  }

  for (let i = 0; i < expenses.value.length; i++) {
    const expense = expenses.value[i]
    if (
      expense.expenseType === undefined ||
      expense.expenseType === null ||
      expense.expenseType === ''
    ) {
      showFailToast(`第${i + 1}条费用明细的费用类别不能为空`)
      return false
    }
    if (expense.amount <= 0) {
      showFailToast(`第${i + 1}条费用明细的金额必须大于0`)
      return false
    }
  }

  return true
}

// 暂存
const handleTempSave = async () => {
  if (tempSaving.value || props.readOnly) return

  if (!validateForm()) return
  const payload: any = {
    id: claimIdRef.value || undefined,
    claimDate: form.claimDate,
    type: form.type,
    status: 2, // 暂存状态
    salesmanName: form.salesmanName,
    customerName: form.customerName,
    customerCode: form.customerCode,
    currency: form.currency,
    currencyCode: form.currencyCode,
    customerList: form.customerList, // 传递字符串类型的URL
    totalAmount: form.totalAmount,
    collectionAccount: form.collectionAccount,
    collectionId: form.collectionId,
    creditInsurance: form.creditInsurance,
    remark: form.remark,
    detailList: buildDetailList()
  }

  try {
    tempSaving.value = true
    await ClaimApi.createClaim(payload)
    showSuccessToast('暂存成功')
    claimIdRef.value = null
    emits('success')
    emits('update:show', false)
  } catch (err) {
    console.error('暂存失败:', err)
    showFailToast('暂存失败')
  } finally {
    tempSaving.value = false
  }
}

// 提交
const handleSubmit = async () => {
  if (submitting.value || props.readOnly) return

  if (!validateForm()) return

  let status=3
  if (props.isEdit) {
    status=1
  }
  const payload: any = {
    id: claimIdRef.value || undefined,
    claimDate: form.claimDate,
    type: form.type,
    status: status, // 业务员确认后无法修改
    salesmanName: form.salesmanName,
    customerName: form.customerName,
    customerCode: form.customerCode,
    currency: form.currency,
    currencyCode: form.currencyCode,
    customerList: form.customerList, // 传递字符串类型的URL
    totalAmount: form.totalAmount,
    collectionAccount: form.collectionAccount,
    creditInsurance: form.creditInsurance,
    collectionId: form.collectionId,
    remark: form.remark,
    detailList: buildDetailList()
  }

  try {
    submitting.value = true
    await ClaimApi.createClaim(payload)
    showSuccessToast('录款成功')
    claimIdRef.value = null
    emits('success')
    emits('update:show', false)
  } catch (err) {
    console.error('录款失败:', err)
    showFailToast('录款失败')
  } finally {
    submitting.value = false
  }
}

// 获取录款详情
const getClaimDetail = async (id: number) => {
  try {
    const res: any = await ClaimApi.getClaimDetail(id)
    if (res) {
      claimIdRef.value = res.id
      form.claimDate = res.claimDate
      form.type = res.type
      form.status = res.status
      form.salesmanName = res.salesmanName
      form.customerName = res.customerName
      form.customerCode = res.customerCode
      form.totalAmount = res.totalAmount
      form.currency = res.currency // 币种名称
      form.currencyCode = res.currencyCode // 币种编码
      form.currencyName = res.currency // 显示名称
      form.customerList = res.customerList || []
      form.collectionAccount = res.collectionAccount
      form.creditInsurance = res.creditInsurance
      form.collectionId = res.collectionId
      form.remark = res.remark

      // 更新文件列表显示
      if (form.customerList) {
        // 更新文件列表显示
        fileList.value = form.customerList.map((url: string) => ({
          url: url,
          status: 'done'
        }))
      } else {
        fileList.value = []
      }

      if (res.detailList && Array.isArray(res.detailList)) {
        orders.value = res.detailList
          .filter((item: any) => item.type === 1)
          .map((item: any) => {
            // 计算收款比例，使用四舍五入
            let claimRatio = 100
            if (item.orderAmount > 0) {
              claimRatio = Math.round((Number(item.amount || 0) / item.orderAmount) * 100)
            }

            return {
              type: 1,
              orderNo: item.orderNo,
              orderAmount: item.orderAmount,
              amount: item.amount,
              currency: item.currency,
              remainingAmount: item.remainingAmount,
              shipAmount: item.shipAmount,
              claimRatio: claimRatio // 添加收款比例字段
            }
          })

        expenses.value = res.detailList
          .filter((item: any) => item.type === 2)
          .map((item: any) => ({
            type: 2,
            expenseType: item.expenseType,
            expenseRemark: item.expenseRemark || '',
            amount: item.amount,
            currency: item.currency
          }))

        ordersUnsettled.value = res.detailList
          .filter((item: any) => item.type === 3)
          .map((item: any) => ({
            type: 3,
            amount: item.amount
          }))
      }
    }
  } catch (err) {
    console.error('获取录款详情失败:', err)
    showFailToast('获取录款详情失败')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    claimDate: dayjs().format('YYYY-MM-DD'),
    type: 2,
    status: 0,
    salesmanName: userStore.user?.nickname || '',
    customerName: '',
    customerCode: '',
    totalAmount: 0.0,
    currency: '',
    currencyName: '', // 重置币种名称
    customerList: [],
    collectionAccount: '',
    collectionId: ''
  })

  orders.value = []
  expenses.value = []
  ordersUnsettled.value = [{ type: 3, amount: 0 }] // 重置订单未下数组
  fileList.value = []
  claimIdRef.value = null
}

// 监听props变化
watch(
  [() => props.show, () => props.id, () => props.isAdd],
  async ([newShow, newId, newIsAdd]) => {
    if (newShow) {
      // 预加载币种和账户数据
      await Promise.all([fetchCurrencyList(), fetchAccountList()])

      if (newId) {
        await getClaimDetail(newId)
      } else if (newIsAdd) {
        resetForm()
      }
    }
    isDateDisabled.value=await CollectionDetailApi.getDate()
  },
  { immediate: true }
)

const onClickOverlay = () => {
  claimIdRef.value = null
}

const readOnly = computed(() => props.readOnly)
</script>

<style scoped>
.border {
  border: 1px solid #f0f0f0;
}
.rounded {
  border-radius: 6px;
}
</style>
