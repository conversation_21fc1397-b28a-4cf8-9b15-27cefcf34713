import request from '@/config/axios'
import axios from 'axios'
const checkIntranetUrl = import.meta.env.VITE_APP_INTRANET_URL

export interface PermissionAssignUserRoleReqVO {
  userId: number
  roleIds: number[]
}

export interface PermissionAssignRoleMenuReqVO {
  roleId: number
  menuIds: number[]
}

export interface PermissionAssignRoleDataScopeReqVO {
  roleId: number
  dataScope: number
  dataScopeDeptIds: number[]
}

// 查询角色拥有的菜单权限
export const getRoleMenuList = async (roleId: number) => {
  return await request.get({ url: '/system/permission/list-role-menus?roleId=' + roleId })
}

// 赋予角色菜单权限
export const assignRoleMenu = async (data: PermissionAssignRoleMenuReqVO) => {
  return await request.post({ url: '/system/permission/assign-role-menu', data })
}

// 赋予角色数据权限
export const assignRoleDataScope = async (data: PermissionAssignRoleDataScopeReqVO) => {
  return await request.post({ url: '/system/permission/assign-role-data-scope', data })
}

// 查询用户拥有的角色数组
export const getUserRoleList = async (userId: number) => {
  return await request.get({ url: '/system/permission/list-user-roles?userId=' + userId })
}

// 赋予用户角色
export const assignUserRole = async (data: PermissionAssignUserRoleReqVO) => {
  return await request.post({ url: '/system/permission/assign-user-role', data })
}

//#region 请求内网方法，确认当前是否属于内网环境
// 创建 axios 实例
const instance = axios.create({
  baseURL: checkIntranetUrl, // 你的基础 URL
  timeout: 1000, // 请求超时时间
})
// 添加响应拦截器
instance.interceptors.response.use(
  (response) => response, // 对响应数据做点什么
  (error) => {
    // 对响应错误做点什么
    // 这里可以添加自定义的错误处理逻辑，但不打印错误信息到控制台
    return Promise.reject(error)
  }
)
export const checkIntranet = async () => {
  try {
    return (await instance.get('/admin-api/system/permission/check-intranet'))?.data
  } catch (e) {
    return { data: false }
  }
}
//#endregion
