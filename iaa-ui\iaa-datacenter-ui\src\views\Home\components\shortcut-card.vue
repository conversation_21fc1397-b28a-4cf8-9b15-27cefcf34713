<template>
  <card-title :title="props.name" />
  <div class="flex items-top justify-start flex-wrap gap-10px">
    <div
      class="shortcut"
      v-for="(router, index) in props.data"
      :key="index"
      @click="push({ name: router.name })"
    >
      <div class="shortcut-icon">
        <Icon :icon="router.meta?.icon" :size="30" color="#fff" />
      </div>
      <div class="ml-2px">
        {{ router.meta?.title }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'

const { push } = useRouter()

const props = defineProps({
  data: propTypes.oneOf<any[]>([]).isRequired,
  name: propTypes.string.def('')
})
</script>

<style lang="scss" scoped>
.shortcut {
  gap: 10px;
  width: 6rem;
  border-radius: 5px;
  text-align: center;
  color: var(--el-color-info);
  font-size: 16px;
  // 添加清爽的阴影效果
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // transition:
  //   box-shadow 0.3s ease,
  //   transform 0.3s ease; // 添加过渡效果

  cursor: pointer;

  padding: 5px;
  .shortcut-icon {
    border-radius: 10px;
    display: inline-block;
    width: 80%;
    height: 60px;
    line-height: 60px;
    padding-top: 10px;
    background-color: var(--el-color-primary);
  }
}

.shortcut:hover {
  background-color: #fafafa;
  border-radius: 10px;
}
</style>
