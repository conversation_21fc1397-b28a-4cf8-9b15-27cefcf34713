<template>
  <ContentWrap style="height: 100%;">
    <!-- 参数栏 -->
    <el-form :inline="true">
      <el-form-item label="日期选择">
        <el-date-picker
style="width: 140px" @change="handleList();" v-model="queryParams.dateStr"
          value-format="YYYY-MM-DD" placeholder="日期" :clearable="false" />
      </el-form-item>
      <el-form-item label="料号">
        <el-input v-model="queryParams.itemCode" class="input-width" clearable />
      </el-form-item>
      <el-form-item label="品名">
        <el-input v-model="queryParams.itemName" class="input-width-1" clearable />
      </el-form-item>
      <el-form-item label="规格">
        <el-input v-model="queryParams.spec" class="input-width-1" clearable />
      </el-form-item>
      <el-form-item label="型号">
        <el-input v-model="queryParams.model" class="input-width" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleList()">查询</el-button>
        <el-button type="warning" :loading="exportLoading" @click="exportExcel()">
          导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
height="calc(100vh - 260px)" :header-cell-style="{ padding: '0px', color: '#555' }" :data="dataList"
      v-loading="loading" border>
      <el-table-column label="料号" prop="itemCode" align="center" width="100px" />
      <el-table-column label="旧料号" prop="beforeCode" align="center" min-width="140px" />
      <el-table-column label="品名" prop="itemName" align="center" width="100px" show-overflow-tooltip />
      <el-table-column label="规格" prop="spec" align="center" width="100px" show-overflow-tooltip />
      <el-table-column label="型号" prop="model" align="center" width="100px" show-overflow-tooltip />
      <!-- <el-table-column
          label="旧储位"
          prop="beforeWh"
          align="center"
          width="80px"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="储位"
          prop="wh"
          align="center"
          width="80px"
          show-overflow-tooltip
        ></el-table-column> -->
      <el-table-column label="包装规格" prop="packing" align="center" width="100px" show-overflow-tooltip />
      <el-table-column label="安全库存" prop="safetyQty" align="center" min-width="100px" show-overflow-tooltip />
      <el-table-column label="期初库存" prop="monthStart" align="center" min-width="100px" show-overflow-tooltip />
      <el-table-column v-for="(item, index) in daysInMonth" :label="`${item}号`" align="center" :key="index">
        <el-table-column label="入库" align="center" width="80px">
          <template #default="{ row }">
            {{ row.into[index] }}
          </template>
        </el-table-column>
        <el-table-column label="出库" align="center" width="80px">
          <template #default="{ row }">
            {{ row.out[index] }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="合计" align="center">
        <el-table-column label="入库" align="center" width="80px">
          <template #default="{ row }">
            {{ row.into[row.into.length - 1] }}
          </template>
        </el-table-column>
        <el-table-column label="出库" align="center" width="80px">
          <template #default="{ row }">
            {{ row.out[row.out.length - 1] }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column
label="结余库存" prop="monthEnd" align="center" min-width="100px" show-overflow-tooltip
        fixed="right" />
    </el-table>
    <!-- 分页 -->
    <Pagination
:total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { InventoryApi } from '@/api/report/erp/bom/inventory'
import download from '@/utils/download'
import { formatToDate } from '@/utils/dateUtil'

const message = useMessage() // 消息弹窗

// 获取当前日期
const today = new Date();
const formattedToday = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  // 支持查询的字段
  dateStr: formattedToday,
  itemCode: undefined,
  itemName: undefined,
  spec: undefined,
  model: undefined
})

const loading = ref(false);

const daysInMonth = ref<number[]>([]);

// 根据月份更新天数
const updateDaysInMonth = () => {
  const year = parseInt(queryParams.dateStr.slice(0, 4));
  const month = parseInt(queryParams.dateStr.slice(5, 7));
  const days = new Date(year, month, 0).getDate();
  daysInMonth.value = Array.from({ length: days }, (_, i) => i + 1);
};

// 初始加载
updateDaysInMonth();

// 监听月份变化并更新天数
watch(() => queryParams.dateStr, updateDaysInMonth);

const dataList = ref<any[]>([])
const total = ref(0) // 列表的总页数
// 筛选处理
const handleList = () => {
  queryParams.pageNo = 1
  // 筛选时保持滚动位置
  getList()
}
const exportLoading = ref(false) // 导出的加载中
const exportExcel = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InventoryApi.exportInventory(queryParams)
    download.excel(data, `库存及出入库明细_${queryParams.dateStr}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await InventoryApi.getInventory(queryParams);
    dataList.value = res.list;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  handleList();
});
</script>

<style scoped>
.input-width {
  width: 100px !important;
}

.input-width-1 {
  width: 140px !important;
}
</style>