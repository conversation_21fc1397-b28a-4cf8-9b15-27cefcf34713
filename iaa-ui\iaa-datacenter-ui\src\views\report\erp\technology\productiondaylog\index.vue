<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="操作内容" prop="operationContent">
        <el-input
          v-model="queryParams.operationContent"
          placeholder="请输入操作内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="操控类型" prop="operationType">
        <el-select
          v-model="queryParams.operationType"
          placeholder="请选择操控类型"
          clearable
          class="!w-240px"
        >
          <el-option label="新增生产日报" value="新增生产日报" />
          <el-option label="更新生产日报" value="更新生产日报" />
          <el-option label="删除生产日报" value="删除生产日报" />
          <el-option label="删除生产日报(异常工时页面)" value="删除生产日报(异常工时页面)" />
          <el-option label="新增异常工时" value="新增异常工时" />
          <el-option label="删除异常工时" value="删除异常工时" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="操作内容" align="left" min-width="500px" prop="operationContent" />
      <el-table-column label="操作类型" align="center" min-width="120px" prop="operationType" />
      <el-table-column label="操作人" align="center" min-width="80px" prop="operationName" />
      <el-table-column label="操作时间" align="center" min-width="180px" prop="createTime">
            <template #default="{ row }">
              {{ row.createTime && formatToDateTime(row.createTime) }}
            </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm(scope.row.id)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DayLogForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { DayLogApi } from '@/api/report/technology/productionlog/index'
import DayLogForm from './DayLogForm.vue'
import { formatToDateTime, formatToDate } from '@/utils/dateUtil'

/** 生产日报表日志 列表 */
defineOptions({ name: 'DayLog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  operationContent: undefined,
  operationName: undefined,
  operationType: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DayLogApi.getDayLogPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (id?: number) => {
  formRef.value.open(id)
}


/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DayLogApi.exportDayLog(queryParams)
    download.excel(data, '生产日报表日志.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>