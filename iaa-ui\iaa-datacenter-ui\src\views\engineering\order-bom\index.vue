<template>
  <ContentWrap>
    <el-tabs v-model="currentTab">
      <el-tab-pane label="主表" name="main" />
      <el-tab-pane label="程序" name="program" />
      <el-tab-pane label="结构" name="structure" />
      <el-tab-pane label="包材" name="packing" />
      <el-tab-pane label="说明书" name="instruction" />
      <el-tab-pane label="面板" name="logo" />
      <el-tab-pane label="交付率" name="interactionRate" />
    </el-tabs>
    <Main v-if="currentTab === 'main'" />
    <Program v-else-if="currentTab === 'program'" />
    <Structure v-else-if="currentTab === 'structure'" />
    <Packing v-else-if="currentTab === 'packing'" />
    <Instruction v-else-if="currentTab === 'instruction'" />
    <Logo v-else-if="currentTab === 'logo'" />
    <InteractionRate v-else-if="currentTab === 'interactionRate'" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import Main from './componets/main.vue'
import Program from './componets/program.vue'
import Structure from './componets/structure.vue'
import Packing from './componets/packing.vue'
import Instruction from './componets/instruction.vue'
import Logo from './componets/logo.vue'
import InteractionRate from './componets/interactionRate.vue'

const currentTab = ref('main')
</script>

<style lang="scss" scoped>
:deep(.vxe-header--column .vxe-cell) {
  padding: 1px !important;
}

:deep(.el-tabs__header) {
  margin-bottom: 0 !important;
}

:deep(.vxe-cell--title .el-input__wrapper),
:deep(.vxe-cell--title .el-select__wrapper) {
  box-shadow: none;
  border-radius: 0;
}
</style>
