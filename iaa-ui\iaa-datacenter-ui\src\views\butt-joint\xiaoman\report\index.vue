<template>
  <ContentWrap class="!mb-0px">
    <div class="content">
      <opportunity
        v-if="currentTab === 'opportunity'"
        :user-list="xiaomanUserFilter"
        :department-list="xiaomanDepartmentFilter"
        :customer-portrait="customerPortrait"
        :customer-stage="customerStage"
        :customer-revenue="customerRevenue"
        :customer-oil-revenue="customerOilRevenue"
      />
      <CustomerDevelopment
        v-else-if="currentTab === 'customerDevelopment'"
        :user-list="xiaomanUserFilter"
        :department-list="xiaomanDepartmentFilter"
        :customer-portrait="customerPortrait"
        :customer-stage="customerStage"
        :customer-revenue="customerRevenue"
        :customer-oil-revenue="customerOilRevenue"
      />
      <order
        v-else-if="currentTab === 'order'"
        :user-list="xiaomanUserFilter"
        :department-list="xiaomanDepartmentFilter"
        :customer-portrait="customerPortrait"
        :customer-stage="customerStage"
        :customer-revenue="customerRevenue"
        :customer-oil-revenue="customerOilRevenue"
      />
    </div>
    <el-tabs v-model="currentTab" type="border-card" tab-position="bottom" class="w-full">
      <el-tab-pane
        label="机会点明细"
        name="opportunity"
        v-hasPermi="['xiaoman:report:page-opportunity']"
      />
      <el-tab-pane
        label="客户开发作战表"
        name="customerDevelopment"
        v-hasPermi="['xiaoman:report:page-customer-development']"
      />
      <el-tab-pane label="滚动订单" name="order" v-hasPermi="['xiaoman:report:order']" />
    </el-tabs>
  </ContentWrap>
</template>

<script lang="ts" setup>
import opportunity from './components/opportunity.vue'
import CustomerDevelopment from './components/CustomerDevelopment.vue'
import order from './components/order.vue'
import * as PermissionsApi from '@/api/butt-joint/xiaoman/permissions'
import { getStrDictOptions } from '@/utils/dict'

const currentTab = ref('opportunity')

const userList = ref<any[]>([])

const onViewUserList = async () => {
  const res = await PermissionsApi.getXiaomanViewUserDO()
  userList.value = res
}

// 小满用户列表
const xiaomanUserFilter = computed(() => {
  return userList.value?.map((item) => {
    return { label: item.chinese_name, value: item.user_id }
  })
})
// 小满组别列表
const xiaomanDepartmentFilter = computed(() => {
  const uniqueDepartments = new Set()
  return userList.value
    ?.filter((item) => {
      if (!uniqueDepartments.has(item.department_id)) {
        uniqueDepartments.add(item.department_id)
        return true
      }
      if (item.department_id == 0) return false
      return false
    })
    .map((item) => {
      return { label: item.department_name, value: item.department_id }
    })
})
/** 小满客户画像字典 */
const customerPortrait = computed(() => {
  return getStrDictOptions('xiaoman_customer_portrait').map((item) => {
    return { label: item.label, value: item.value }
  })
})
/** 小满客户阶段字典 */
const customerStage = computed(() => {
  return getStrDictOptions('xiaoman_customer_stage').map((item) => {
    return { label: item.label, value: item.value }
  })
})
/** 小满客户营收字典 */
const customerRevenue = computed(() => {
  return getStrDictOptions('xiaoman_customer_revenue').map((item) => {
    return { label: item.label, value: item.value }
  })
})
/** 小满香氛营收字典 */
const customerOilRevenue = computed(() => {
  return getStrDictOptions('xiaoman_customer_oil_revenue').map((item) => {
    return { label: item.label, value: item.value }
  })
})

onMounted(() => {
  onViewUserList()
})
</script>

<style lang="scss" scoped>
.content {
  height: calc(100vh - 150px);
  padding: 5px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin-top: 0 !important;
}

:deep(.el-card__body) {
  padding: 0px !important;
}
</style>
