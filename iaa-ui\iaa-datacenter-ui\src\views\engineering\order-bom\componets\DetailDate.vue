<template>
  <el-popover placement="bottom" width="500" :visible="visiable">
    <template #reference>
      <el-tag :type="getDetailType(props.data)" @click="openForm">
        {{ getDictLabel(TYPE_DICT[props.data?.type], props.data?.custom) }}
      </el-tag>
    </template>
    <el-form label-width="120" size="small">
      <el-row>
        <el-col :span="12">
          <el-form-item label="计划资料接收日期">
            <span v-if="!edit">{{ props.data?.planReceiptDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.planReceiptDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  props.data?.planReceiptDate!,
                  formData.planReceiptDate!,
                  'planReceiptDate',
                  '计划资料接收日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际资料接收日期">
            <span v-if="!edit">{{ props.data?.actualReceiptDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.actualReceiptDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划设计日期">
            <span v-if="!edit">{{ props.data?.planDesignDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.planDesignDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  props.data?.planDesignDate!,
                  formData.planDesignDate!,
                  'planDesignDate',
                  '计划设计日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际设计日期">
            <span v-if="!edit">{{ props.data?.actualDesignDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.actualDesignDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="props.data?.type == 'packing'">
          <el-form-item label="计划设计稿确认">
            <span v-if="!edit">{{ props.data.planQuotationDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.planQuotationDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  props.data.planQuotationDate!,
                  formData.planQuotationDate!,
                  'planQuotationDate',
                  '计划设计稿确认'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="props.data?.type == 'packing'">
          <el-form-item label="实际设计稿确认">
            <span v-if="!edit">{{ props.data.actualQuotationDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.actualQuotationDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="['program'].includes(props.data?.type!) ? '计划测试日期' : '计划打样日期'"
          >
            <span v-if="!edit">{{ props.data?.planTestingDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.planTestingDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  props.data?.planTestingDate!,
                  formData.planTestingDate!,
                  'planTestingDate',
                  '计划打样日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="['program'].includes(props.data?.type!) ? '实际测试日期' : '实际打样日期'"
          >
            <spen v-if="!edit">{{ props.data?.actualTestingDate }}</spen>
            <el-date-picker
              v-else
              v-model="formData.actualTestingDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!['program'].includes(props.data?.type!)">
          <el-form-item
            :label="
              ['packing', 'instruction'].includes(props.data?.type!)
                ? '计划确认日期'
                : '计划承认日期'
            "
          >
            <span v-if="!edit">{{ props.data?.planAdmitDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.planAdmitDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  props.data?.planAdmitDate!,
                  formData.planAdmitDate!,
                  'planAdmitDate',
                  '计划承认日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!['program'].includes(props.data?.type!)">
          <el-form-item
            :label="
              ['packing', 'instruction'].includes(props.data?.type!)
                ? '实际确认日期'
                : '实际承认日期'
            "
          >
            <span v-if="!edit">{{ props.data?.actualAdmitDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.actualAdmitDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划完成日期">
            <span v-if="!edit">{{ props.data?.planCompleteDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.planCompleteDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  props.data?.planCompleteDate!,
                  formData.planCompleteDate!,
                  'planCompleteDate',
                  '计划完成日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际完成日期">
            <span v-if="!edit">{{ props.data?.actualCompleteDate }}</span>
            <el-date-picker
              v-else
              v-model="formData.actualCompleteDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit || !checkPermi(['allow:complete:date'])"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <span v-if="!edit">{{ props.data?.remark }}</span>
            <el-input v-else v-model="formData.remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="text-align: right; margin: 0">
      <el-button
        size="small"
        @click="
          () => {
            visiable = false
            edit = false
          }
        "
        >关闭</el-button
      >
      <template v-if="checkPermi([`order-bom:task:${props.data?.type}-edit`])">
        <el-button size="small" type="warning" @click="edit = true" v-if="!edit"> 修改 </el-button>
        <el-button size="small" type="primary" @click="onSaveDetail" v-else> 保存 </el-button>
      </template>
    </div>
  </el-popover>

    <el-dialog v-model="dialogVisible" title="修改原因" width="400" align-center append-to-body>
      <div>
        <p>请输入修改原因</p>
        <el-input v-model="reason" type="textarea" style="width: 380px" placeholder="请输入修改原因"   />
        <p>请选择责任主体</p>
        <el-radio-group v-model="category" style="display: block; margin-bottom: 10px">
          <el-radio label="研发">研发</el-radio>
          <el-radio label="工程">工程</el-radio>
          <el-radio label="业务">业务</el-radio>
        </el-radio-group>
      </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { getDictLabel } from '@/utils/dict'
import moment from 'moment'
import { checkPermi } from '@/utils/permission'
import { cloneDeep } from 'lodash'
import { ElMessageBox } from 'element-plus'
import { CustomerApi } from '@/api/report/erp/order-bom'

const message = useMessage()

const props = defineProps({
  data: propTypes.any.isRequired
})
const visiable = ref(false)

const getDetailType = (row: any) => {
  if (!row?.planCompleteDate) {
    return 'info'
  }

  const planDate = moment(row.planCompleteDate)
  let actualDate = row.actualCompleteDate ? moment(row.actualCompleteDate) : moment()

  const planStart = planDate.startOf('day')
  const actualStart = actualDate.startOf('day')

  const daysDiff = planStart.diff(actualStart, 'days')

  if (!row.actualCompleteDate) {
    //实际完成日期为空
    // 当前日期与计划完成日期比较
    if (daysDiff >= 2) {
      return 'primary'
    } else if (daysDiff >= 0) {
      return 'warning'
    } else {
      return 'danger'
    }
  } else {
    // 实际完成日期与计划完成日期比较
    
    if (daysDiff >= 0) {
      return 'success' // 实际完成日期 <= 计划完成日期
    } else {
      return 'danger' // 实际完成日期 > 计划完成日期
    }
  }
}

const edit = ref(false)
const formData = ref<any>()

const TYPE_DICT = {
  structure: 'eng_structure_dict',
  packing: 'eng_packing_dict',
  instruction: 'eng_instruction_dict',
  program: 'eng_program_dict',
  logo: 'eng_logo_dict'
}

const changeData = ref<any>({})
const emits = defineEmits(['success'])

const onDateChange = (
  beforeValue: string,
  afterValue: string,
  field: string,
  fieldName: string
) => {
  if (field === 'planCompleteDate' && formData.value.planBomCompleteDate) {
    if(formData.value.planBomCompleteDateChange) {
      if (moment(formData.value.planBomCompleteDateChange).isBefore(moment(formData.value[field]))) {
      message.error('计划完成时间不能早于变更BOM完成时间')
      formData.value[field] = undefined
      return
     }
    }else if (moment(formData.value.planBomCompleteDate).isBefore(moment(formData.value[field]))) {
      message.error('计划完成时间不能早于计划BOM完成时间')
      formData.value[field] = undefined
      return
    }
  }
  // 如果没有选择日期，则不执行后续操作
  if (!beforeValue && afterValue) return
  if ((!beforeValue && !afterValue) || beforeValue === afterValue) {
    delete changeData.value[field]
    return
  }
  changeData.value[field] = fieldName + '从' + beforeValue + '修改为' + afterValue
}


const dialogVisible = ref(false)
const reason = ref('')
const category = ref('')

const cancelDialog = () => {
  dialogVisible.value = false
}

const confirmDialog = async () => {
  try {
    if (!reason) {
      ElMessage.error('请填写修改原因')
      return
    }
    if (!category) {
      ElMessage.error('请选择责任主体')
      return
    }

    const modifyInfo = Object.values(changeData.value).join('\n')
    await CustomerApi.saveDetail({
      ...formData.value,
      modifyInfo,
      reason: reason.value,
      category: category.value
    })
    message.success('保存成功')
    reason.value = ''
    category.value = ''
    dialogVisible.value = false
    emits('success')
  } finally {
  }
}
const onSaveDetail = async () => {
  try {
    const modifyInfo = Object.values(changeData.value).join('\n')
    console.log('modifyInfo:'+modifyInfo)
    if (modifyInfo) {
      dialogVisible.value = true
    } else {
      await CustomerApi.saveDetail({
        ...formData.value,
        modifyInfo
      })
      message.success('保存成功')
      emits('success')
    }
  } finally {
  }
}

const openForm = () => {
  formData.value = cloneDeep(props.data)
  visiable.value = true
}

const closeForm = () => {
  visiable.value = false
  edit.value = false
}

const disabledDate = (data: Date) => {
  return moment(data).isSameOrBefore(moment().add(-1, 'days'))
}

defineExpose({
  closeForm
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 10px !important;
}
:deep(.el-form-item__content) {
  width: 100%;
  background-color: #f5f7f9;
  padding: 1px 5px;
}
.el-tag--test{
  background-color: #c24ef7 !important;
  --el-tag-bg-color: #f5f7f9 !important;
  color: #fff;
}
</style>
