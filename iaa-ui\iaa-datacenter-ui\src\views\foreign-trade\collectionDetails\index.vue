<template>
  <ContentWrap>
    <vxe-toolbar custom ref="toolbarRef" size="mini">
      <template #buttons>
        <div v-hasPermi="['collection:details:financial']">
          <span class="mr-10px">请选择业务员：</span>
          <el-select
            v-model="queryParams.salesmanNameList"
            multiple
            filterable
            size="small"
            style="width: 500px"
            @change="getList"
          >
            <el-option
              v-for="(item, index) in sellerList"
              :key="index"
              :value="item"
              :label="item"
            />
          </el-select>
        </div>
        <el-button
          type="info"
          plain
          class="ml-10px"
          size="small"
          @click="handleExport()"
          title="导出"
          :loading="exportLoading"
        >
          导出
        </el-button>
        <!-- 添加人民币总额显示 -->
        <div class="ml-20px inline-flex items-center" v-if="totalCNYAmount > 0">
          <span class="text-14px font-bold">人民币总金额：{{ totalCNYAmount }} 元</span>
        </div>
        <span v-hasPermi="['collection:details:updateDate']">
          <el-switch
            v-model="showDate"
            active-text="可修改日期"
            inactive-text="不可修改日期"
            class="ml-20px"
            @change="updateDate()"
          />
        </span>
      </template>
    </vxe-toolbar>

    <div class="h-[calc(100vh-180px)]">
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          :row-config="{ height: 25 }"
          ref="tableRef"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :checkbox-config="{ reserve: true, highlight: false, range: true }"
          :filter-config="{}"
          show-footer
          keep-source
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          :mouse-config="{ selected: true }"
          @filter-change="handleFilterChange"
          @cell-click="handleCellClick"
          tabindex="0"
          size="mini"
          :cell-class-name="cellClassName"
        >
          <vxe-column
            field="claimDate"
            title="收款日期"
            min-width="60"
            :filters="claimDateOptions"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column
            field="salesmanName"
            title="业务员"
            width="120"
            :filters="salesmanNameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="customerCode"
            title="客户编码"
            min-width="100"
            :filters="customerCodeOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="customerName"
            title="客户名称"
            min-width="100"
            :filters="customerNameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="collectionAccount"
            title="收款账号"
            min-width="150"
            :filters="collectionAccountOptions"
            :edit-render="{
              name: '$select',
              options: collectionAccountOptions,
              props: { value: 'value', label: 'label' }
            }"
          />
          <vxe-column
            field="currencyCode"
            title="币种"
            width="120"
            :filters="currencyTypeOptions"
            :edit-render="{
              name: '$select',
              options: currencyTypeOptions,
              props: { value: 'value', label: 'label' }
            }"
          >
            <template #default="{ row }">
              {{row.currency}}
            </template>
          </vxe-column>
          <vxe-column
            field="collectionType"
            title="收款类别"
            width="120"
            :filters="collectionTypeOptions"
            :edit-render="{
              name: '$select',
              options: collectionTypeOptions,
              props: { value: 'value', label: 'label' }
            }"
          >
            <template #default="{ row }">
              <span v-if="row.collectionType === 1">订单收款</span>
              <span v-else-if="row.collectionType === 2">费用收款</span>
              <span v-else-if="row.collectionType === 3">订单未下</span>
              <span v-else-if="row.collectionType === 0">收款信息</span>
              <span v-else>{{ row.collectionType }}</span>
            </template>
          </vxe-column>
          <vxe-column
            field="orderType"
            title="订单号/费用类别"
            width="120"
            :filters="orderTypeOptions"
            :filter-render="FilterValue.textFilterRender"
          >
            <template #default="{ row }">
              <!-- 费用收款时，显示字典值 -->
              <span v-if="row.collectionType === 2">
                {{ getExpenseTypeName(row.orderType) }}
              </span>
              <!-- 订单收款时，显示原值 -->
              <span v-else-if="row.collectionType === 1">
                {{ row.orderType }}
              </span>
              <!-- 其他情况显示原值 -->
              <span v-else>
                {{ row.orderType }}
              </span>
            </template>
          </vxe-column>
          <vxe-column field="rate" title="汇率" width="50" />
          <vxe-column
            field="itemAmount"
            title="单项金额"
            width="120"
            :filters="itemAmountOptions"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column
            field="totalAmount"
            title="总金额"
            width="120"
            :filters="totalAmountOptions"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column
            field="remark"
            title="备注"
            width="120"
            :filters="remarkOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="creditInsurance"
            title="账号说明"
            width="120"
            :filters="creditInsuranceOptions"
            :edit-render="{
              name: '$select',
              options: creditInsuranceOptions,
              props: { value: 'value', label: 'label' }
            }"
          />
          <!-- 添加状态列，仅在"我的"标签页显示 -->
          <vxe-column field="status" title="状态" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.status === 2" class="status-danger">暂存</el-tag>
              <!-- 已提交并且是订单未下时，显示业务待确认 -->
              <el-tag v-else-if="row.collectionType === 3" class="status-danger">业务待确认</el-tag>
              <el-tag
                v-else-if="row.status === 1 && (row.type === '1' || row.type === '2')"
                class="status-gray"
                >财务已确认</el-tag
              >

              <el-tag
                v-else-if="row.status === 0 && (row.type === '1' || row.type === '2')"
                class="status-gray"
                >业务已确认</el-tag
              >
              <el-tag
                v-else-if="row.status === 3 && (row.type === '1' || row.type === '2')"
                class="status-gray"
                >财务待确认</el-tag
              >
              <el-tag v-else-if="row.status === 0 && row.type === '3'" class="status-gray"
                >待认领</el-tag
              >
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        size="small"
      />
    </div>
    <replacementDialogPC
      v-model:show="dialogVisible"
      :maxAmount="maxAmount"
      :id="selectedId"
      :customerCode="customerCode"
      :claimDetail="claimDetail"
      @success="getList"
    />
    <ClaimDialog
      v-model:show="claimDialogVisible"
      :ids="selectedIds"
      :prefill="prefillData"
      :read-only="readOnlyMode"
      @success="getList"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { CollectionDetailApi } from '@/api/foreign-trade/collectionDetails'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FilterValue from '@/utils/Filter'
import * as FilterTemplate from '@/utils/Filter'
import { InformationApi } from '@/api/foreign-trade/collectionInformation/index'
import { RecPaymentApi } from '@/api/sales/receiving-payment'
import replacementDialogPC from './replacementDialogPC.vue'
import ClaimDialog from '@/views/foreign-trade/collectionInformation/components/ClaimDialogPC.vue'
import download from '@/utils/download'
import * as AccountApi from '@/api/foreign-trade/account'
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'

//筛选相关
const salesmanNameOptions = ref([{ data: '' }])
const customerCodeOptions = ref([{ data: '' }])
const customerNameOptions = ref([{ data: '' }])
const claimDateOptions = ref([{ data: [] }])
const totalAmountOptions = ref([{ data: { condition: '10', value: undefined } }])
const itemAmountOptions = ref([{ data: { condition: '10', value: undefined } }])
const remarkOptions = ref([{ data: '' }])
const orderTypeOptions = ref([{ data: '' }])
//收款账号筛选器
const collectionAccountOptions = ref([])
//币种筛选器
const currencyTypeOptions = ref([])

//账号说明筛选器
const creditInsuranceOptions = ref([
  { label: 'ScentaChina', value: 'ScentaChina' },
  { label: 'ScentMachine', value: 'ScentMachine' },
  { label: 'ScentMarketing', value: 'ScentMarketing' }
])

const collectionTypeOptions = ref([
  { label: '收款信息', value: 0 },
  { label: '订单收款', value: 1 },
  { label: '费用收款', value: 2 },
  { label: '订单未下', value: 3 }
])
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
//所有业务员
const sellerList = ref<string[]>([])

// 导出的加载中
const exportLoading = ref(false)

const totalCNYAmount = ref(0)

const showDate = ref(false)

const updateDate = async () => {
  try {
    // 根据当前 showDate 的值来决定传递的参数
    await CollectionDetailApi.updateDate(showDate.value ? 0 : 1)
    getShowDate()
    //刷新数据
    message.success('操作成功')
  } catch (error) {
    // 如果API调用失败，恢复开关状态
    showDate.value = !showDate.value
    message.error('操作失败')
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CollectionDetailApi.exportCollectionDetail(queryParams)
    download.excel(data, `收款明细列表.xlsx`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const onList = async () => {
  sellerList.value = await RecPaymentApi.getAllSellerList()
}
const queryParams = reactive({
  salesmanNameList: [],
  pageNo: 1,
  pageSize: 30
})

const tableRef = ref()
const toolbarRef = ref()

const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['currencyCode', 'collectionAccount','collectionType']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

// 添加 cellClassName 方法来设置鼠标样式
const cellClassName = ({ row }) => {
  // 只有满足条件的行才显示手型光标
  if (row.collectionType === 3) {
    return 'cursor-pointer'
  }
  return ''
}
// 弹窗相关
const dialogVisible = ref(false)
const maxAmount = ref(0)
const selectedId = ref()
const customerCode = ref()
const claimDetail = ref<any>(null)

//暂存认领
const claimDialogVisible = ref(false)
const selectedIds = ref<number[]>([])
const prefillData = ref<any>(null)
const readOnlyMode = ref(false)

const handleCellClick = async ({ row }) => {
  try {
    //只有当收款类别为 订单未下 时才允许点击打开弹窗，补充订单和费用类别
    if (row.status !== 2 && row.collectionType === 3) {
      // 获取第一条记录的详细信息用于继续认领
      const detail = await InformationApi.getSuspended(row.id)
      const data = detail.detailList.filter((item: any) => item.type == '3')
      if (data.length > 0) {
        // 获取可继续认领的最大金额
        maxAmount.value = data[0].amount || 0
        selectedId.value = row.id
        customerCode.value = row.customerCode
        claimDetail.value = detail
        // 打开弹窗
        dialogVisible.value = true
      }
    } else if (row.status === 2 && row.collectionType === 3) {
      let ids: number[] = []
      ids = [row.id]
      // 获取第一条记录的详细信息用于继续认领
      const detail = await InformationApi.getSuspended(ids[0])
      selectedIds.value = [ids[0]]
      prefillData.value = detail || null
      readOnlyMode.value = false
      claimDialogVisible.value = true
    }
  } catch (e) {
    console.error(e)
    getList()
  }
}

const getExpenseTypeName = (value: any) => {
  const dictOptions = getIntDictOptions(DICT_TYPE.FINANCIAL_COSTS_TYPE)
  // 使用宽松比较以处理数字和字符串的匹配问题
  const dict = dictOptions.find((item: any) => item.value == value)
  return dict ? dict.label : value
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CollectionDetailApi.getCollectionDetailsPage(queryParams)
    list.value = data.list
    total.value = data.total

    const totalData = await CollectionDetailApi.getRateAndTotal(queryParams)
    totalCNYAmount.value = totalData
  } finally {
    loading.value = false
  }
}

const getShowDate = async () => {
  const data = await CollectionDetailApi.getDate()
  showDate.value = data
}

onMounted(async () => {
  getList()
  onList()
  getShowDate()
  const accountRes = await AccountApi.getAccount()
  if (accountRes) {
    const convertedData = accountRes.map((item: any) => ({
      value: item.accountName,
      label: item.accountName
    }))
    collectionAccountOptions.value = convertedData
  }
  const res = await ClaimApi.getCurrency()
  if (res) {
    const convertedData = res.map((item: any) => ({
      value: item.Code,
      label: item.Name
    }))
    currencyTypeOptions.value = convertedData
  }
})
</script>

<style lang="css" scoped>
/* 添加鼠标手型样式 */
.cursor-pointer {
  cursor: pointer;
}

/* 自定义红色标签样式 */
.status-danger {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
}

/* 自定义灰色标签样式 */
.status-gray {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: white !important;
}

/* :deep(.el-tag){
  height: 25px;
} */
</style>
