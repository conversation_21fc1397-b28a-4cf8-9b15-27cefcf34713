import request from '@/config/axios'

export const FinancialApi = {
    //获取下载模版
  exportTemplate: () => {
    return request.download({ url: `/collection/receivable/export-template` })
  },

  deleteReceivable: (ids: any) => {
    return request.post({ url: `/collection/receivable/deletes`, data: ids})
  },

  getReceivablePage: (params: any) => {
    return request.post({ url: `/collection/receivable/page`, data:params })
  },

  getReceivableStatementsPage: (params: any) => {
    return request.post({ url: `/collection/receivable/statementsPage`, data: params })
  },

  // 导出财务应收报表 Excel
  exportInformation: async (params) => {
    return await request.download({ url: `/collection/receivable/export-excel`, params })
  },
}