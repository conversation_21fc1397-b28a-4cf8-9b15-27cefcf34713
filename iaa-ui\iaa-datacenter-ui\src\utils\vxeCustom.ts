import { VxeUI } from 'vxe-pc-ui'

// 模拟查询接口
const findCustomSetting = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(id)
      try {
        if (localStorage.getItem(id)) {
          resolve(JSON.parse(localStorage.getItem(id) || ''))
        } else {
          resolve({})
        }
      } catch (e) {
        resolve({})
      }
    }, 300)
  })
}

// 模拟保存接口
const saveCustomSetting = (id, storeData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(storeData)
      localStorage.setItem(id, JSON.stringify(storeData))
      VxeUI.modal.message({
        status: 'success',
        content: '保存个性化列设置成功'
      })
      resolve({})
    }, 200)
  })
}

export const customConfig: any = reactive({
  storage: true, // 启用自定义列状态保存功能
  restoreStore({ id }) {
    // 从服务端调用接口获取当前用户表格自定义列数据，支持异步，返回 Promise
    return findCustomSetting(id)
  },
  updateStore({ id, storeData }) {
    // 当 storage 启用后，默认会自动保存在浏览器本地 localStorage 里面，可以通过自定义改方法，使用服务端保存
    // 将用户自定义的列数据保存到服务端，支持异步，返回 Promise
    return saveCustomSetting(id, storeData)
  },
  checkMethod({ column }) {
    // 禁用动态列
    return !column.field?.startsWith('dynamic_')
  }
})

/** 单元格点击高亮行列 */
export const tableCellClick = ({ row, column }: any, tableRef: any) => {
  const $table = tableRef
  if ($table) {
    $table.setCurrentRow(row)
    $table.setCurrentColumn(column)
  }
}
