
import request from '@/config/axios'

export const CustApi = {
  // 获取报工信息分页数据
  getCustDeclarationPage: async (data: any) => {
    return await request.post({ url: '/report/cust-declaration/page', data })
  },

  // 更新报工信息
  update: async (data: any[]) => {
    return await request.post({ url: '/report/cust-declaration/update', data })
  },
  // 删除报工信息
  delete: async (id: number) => {
    return await request.get({ url: '/report/cust-declaration/delete/' + id })
  }
}
