<template>
  <ContentWrap>
    <vxe-toolbar custom ref="toolbarRef" size="mini">
      <template #buttons>
        <el-button
          type="primary"
          plain
          class="mr-10px"
          size="small"
          v-hasPermi="['record:money:create']"
          @click="handleAddDetail()"
          title="新增"
        >
          新增
        </el-button>
        <el-button
          type="success"
          plain
          class="mr-10px"
          size="small"
          @click="handleBatchConfirm()"
          title="批量确认"
        >
          批量确认
        </el-button>
      </template>
      <template #tools>
        <el-button
          type="primary"
          link
          class="mr-10px"
          @click="permissionFormRef?.openForm()"
          v-hasPermi="['receiving:payment:permission']"
          size="small"
        >
          权限管理
        </el-button>
      </template>
    </vxe-toolbar>

    <div class="h-[calc(100vh-180px)]">
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          :row-config="{ height: 25, keyField: 'id' }"
          ref="tableRef"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          border
          stripe
          align="center"
          height="100%"
          max-height="100%"
          show-overflow="title"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :filter-config="{}"
          show-footer
          keep-source
          :footer-cell-style="{
            padding: 0,
            background: '#dcefdc',
            border: '1px solid #ebeef5'
          }"
          :mouse-config="{ selected: true }"
          @filter-change="handleFilterChange"
          tabindex="0"
          size="mini"
        >
          <vxe-column type="checkbox" width="40" field="id" fixed="left" />
          <vxe-column
            field="customerList"
            title="客户水单"
            width="60"
            :cell-render="imgUrlCellRender"
            fixed="left"
          />
          <vxe-column
            field="customerCode"
            title="客户编码"
            min-width="100"
            :filters="customerCodeOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="customerName"
            title="客户名称"
            min-width="100"
            :filters="customerNameOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="claimDate"
            width="200"
            title="日期"
            :filters="claimDate"
            :filter-render="FilterTemplate.dateRangeFilterRender"
          />
          <vxe-column
            field="collectionAccount"
            title="收款账号"
            min-width="200"
            :filters="collectionAccountOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="totalAmount"
            title="收款金额"
            width="120"
            :filters="totalAmountOptions"
            :filter-render="FilterTemplate.numberFilterRender"
          />
          <vxe-column
            field="currencyCode"
            title="币种"
            width="120"
            :filters="currencyTypeOptions"
            :edit-render="{
              name: '$select',
              options: currencyTypeOptions,
              props: { value: 'value', label: 'label' }
            }"
          >
            <template #default="{ row }">
              {{row.currency}}
            </template>
          </vxe-column>
          <vxe-column
            field="remark"
            title="备注"
            width="120"
            :filters="remarkOptions"
            :filter-render="FilterValue.textFilterRender"
          />
          <vxe-column
            field="creditInsurance"
            title="账号说明"
            width="120"
            :filters="creditInsuranceOptions"
            :edit-render="{
              name: '$select',
              options: creditInsuranceOptions,
              props: { value: 'value', label: 'label' }
            }"
          />
          <!-- 添加状态列，仅在"我的"标签页显示 -->
          <vxe-column field="status" title="状态" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.status === 2" type="warning">暂存</el-tag>
              <el-tag v-else-if="row.status === 1" type="success">财务已确认</el-tag>
              <el-tag v-else-if="row.status === 3" type="primary">业务员已确认</el-tag>
            </template>
          </vxe-column>

          <vxe-column
            field="salesmanName"
            title="业务员"
            width="80"
            :filters="salesmanNameOptions"
            :filter-render="FilterValue.textFilterRender"
          />

          <vxe-column title="操作" width="180" fixed="right">
            <template #default="{ row }">
              <!-- 暂存状态 (status=2) - 显示继续录入 -->
              <el-button
                v-if="row.status === 2"
                @click="handleContinueEdit(row)"
                v-hasPermi="['record:money:create']"
                link
                type="primary"
              >
                继续录入
              </el-button>
              <!-- 财务确认收款按钮 (仅在status=3时显示) -->
              <el-button
                v-if="row.status === 3"
                v-hasPermi="['record:money:status']"
                @click="handleClaim([row.id])"
                link
                type="success"
              >
                确认收款
              </el-button>
              <el-button @click="handleViewDetail(row)" link type="info"> 查看 </el-button>
              <el-button
                v-if="row.status !== 1"
                v-hasPermi="['record:money:delete']"
                @click="handleDelete([row.id])"
                link
                type="danger"
              >
                删除
              </el-button>
              <span v-if="row.status == 1" class="ml-3">
                <el-button
                  @click="handleEdit(row)"
                  v-hasPermi="['record:money:admin']"
                  link
                  type="primary"
                >
                  修改
                </el-button>
                <el-button
                  v-hasPermi="['record:money:admin']"
                  @click="handleDelete([row.id])"
                  link
                  type="danger"
                >
                  删除
                </el-button>
              </span>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        size="small"
      />
    </div>
    <PermissionForm ref="permissionFormRef" />
    <!-- 添加弹窗组件 -->
    <ClaimDialog
      v-model:show="addDialogVisible"
      :id="selectId"
      :read-only="readOnlyMode"
      :isAdd="isAdd"
      :isEdit="isEdit"
      @success="handleClaimSuccess"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import * as FilterValue from '@/utils/Filter'
import ClaimDialog from './recordDialogPC.vue'
import PermissionForm from '@/views/sales/receiving-payment/components/PermissionForm.vue'
import type { VxeColumnPropTypes } from 'vxe-table'
import * as FilterTemplate from '@/utils/Filter'

const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数

const permissionFormRef = ref()

const collectionAccountOptions = ref([{ data: '' }])
const totalAmountOptions = ref([{ data: { condition: '10', value: undefined } }])
const customerCodeOptions = ref([{ data: '' }])
const customerNameOptions = ref([{ data: '' }])
const claimDate = ref([{ data: [] }])
const remarkOptions = ref([{ data: '' }])
const salesmanNameOptions = ref([{ data: '' }])

//币种筛选器
const currencyTypeOptions = ref([])

//账号说明筛选器
const creditInsuranceOptions = ref([
  { label: 'ScentaChina', value: 'ScentaChina' },
  { label: 'ScentMachine', value: 'ScentMachine' },
  { label: 'ScentMarketing', value: 'ScentMarketing' }
])
const imgUrlCellRender = reactive<VxeColumnPropTypes.CellRender>({
  name: 'VxeImage',
  props: {
    width: 36,
    height: 36
  }
})
const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['currencyCode', 'creditInsurance']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize', 'type'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

const tableRef = ref()
const toolbarRef = ref()

// 新增相关
const addDialogVisible = ref(false)
const selectId = ref<any>(null)
const prefillData = ref<any>(null)
const readOnlyMode = ref(false)
const isAdd = ref(false)

const isEdit = ref(false)
// 处理新增成功
const handleClaimSuccess = () => {
  addDialogVisible.value = false
  isAdd.value = false
  selectId.value = null
  prefillData.value = null
  getList() // 重新加载列表数据
}

// 处理继续录入
const handleContinueEdit = (row: any) => {
  selectId.value = row.id
  readOnlyMode.value = false // 可编辑模式
  addDialogVisible.value = true
}

//修改
const handleEdit = (row: any) => {
  selectId.value = row.id
  readOnlyMode.value = false // 可编辑模式
  isEdit.value = true
  addDialogVisible.value = true
}
const handleAddDetail = () => {
  readOnlyMode.value = false // 只读模式
  isAdd.value = true
  addDialogVisible.value = true
}

const handleBatchConfirm = async () => {
  // 获取选中的行
  const $table = tableRef.value
  if (!$table) {
    message.error('表格实例未找到')
    return
  }

  const selectedRows = $table.getCheckboxRecords()
  if (!selectedRows || selectedRows.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  // 过滤出 status=3 的数据
  const validRows = selectedRows.filter((row: any) => row.status === 3)
  if (validRows.length === 0) {
    message.warning('请选择状态为"业务员已确认"的数据进行确认收款')
    return
  }

  // 获取选中行的ID
  const ids = validRows.map((row: any) => row.id)

  await message.confirm(`是否确认收款选中的 ${validRows.length} 条数据？(已过滤掉无效数据)`)
  await ClaimApi.updateClaim(ids)
  message.success('确认收款成功')
  getList() // 重新加载列表数据
}
// 处理查看详情（只读模式）
const handleViewDetail = async (row: any) => {
  selectId.value = row.id
  readOnlyMode.value = true // 只读模式
  addDialogVisible.value = true
  await nextTick() // 确保DOM更新完成
}
const handleClaim = async (rowId?: any) => {
  await message.confirm('是否确认收款？')
  await ClaimApi.updateClaim(rowId)
  message.success('确认收款成功')
  getList()
}

const handleDelete = async (rowId?: any) => {
  await message.confirm('是否确认删除？')
  await ClaimApi.deleteClaim(rowId)
  message.success('删除成功')
  getList()
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClaimApi.getClaimPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const queryParams = reactive({
  pageNo: 1,
  pageSize: 30,
  type: 2,
  collectionAccount: undefined, //收款账号
  dateStr: undefined, //录款日期
  status: undefined // 1:财务已确认, 2:暂存
})

onMounted(async () => {
  getList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })

  const res = await ClaimApi.getCurrency()
  if (res) {
    const convertedData = res.map((item: any) => ({
      value: item.Code,
      label: item.Name
    }))
    currencyTypeOptions.value = convertedData
  }
})
</script>
