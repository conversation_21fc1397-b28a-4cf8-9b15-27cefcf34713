<template>
  <div>
    <el-form ref="formRef" label-width="120px">
      <el-card class="mb-4" shadow="never">
        <template #header v-if="!mobile">
          <div style="display: flex; justify-content: space-between; align-items: center">
            <el-button type="primary" @click="addRow" size="small" plain>
              <Icon icon="ep:plus" style="margin-right: 4px" /> 新增行
            </el-button>
          </div>
        </template>
        <!-- 移动端 -->
        <div v-if="mobile">
          <div v-for="(row, index) in localData" :key="index" class="card-item">
            <div class="card-title"
              >第 {{ index + 1 }} 条 / 共 {{ localData.length }} 条

              <el-button style="padding: 0; color: #fff" link @click="removeRow(index)">
                <icon icon="ep:delete" />
              </el-button>
            </div>
            <el-form class="mobile-body-form">
              <el-row>
                <el-col :span="24">
                  <el-form-item label="时间段">
                    <div class="time-range-row">
                      <el-time-select
                        v-model="row.timeStart"
                        placeholder="起始时间"
                        start="08:30"
                        end="23:30"
                        step="00:30"
                        placement="top-start"
                      />
                      <span class="time-range-separator">-</span>
                      <el-time-select
                        v-model="row.timeEnd"
                        placeholder="结束时间"
                        start="08:30"
                        end="23:30"
                        step="00:30"
                        :min-time="row.timeStart"
                        placement="top-start"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="人数">
                    <el-input
                      v-model="row.number"
                      type="number"
                      placeholder="人数"
                      min="0"
                      @input="(val) => (row.number = Number(val))"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工时">
                    <el-input v-model="row.workingHours" readonly disabled placeholder="工时" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="type === 1">
                <el-col :span="12" v-if="type === 1">
                  <el-form-item label="类型">
                    <el-select v-model="row.type" placeholder="请选择类型" style="width: 100%">
                      <el-option
                        v-for="dict in getIntDictOptions(
                          DICT_TYPE.PRODUCTION_REPORT_OFFICIALLY_TYPE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注">
                    <el-input v-model="row.remark" placeholder="备注" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-else-if="type === 4">
                <el-col :span="12">
                  <el-form-item label="借出产线">
                    <el-select v-model="row.deptCode" style="width: 100%">
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_DEPT)"
                        clearable
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注">
                    <el-input v-model="row.remark" placeholder="备注" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-else-if="type === 5">
                <el-col :span="12">
                  <el-form-item label="借入产线">
                    <el-select v-model="row.deptCode" style="width: 100%">
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_DEPT)"
                        clearable
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注">
                    <el-input v-model="row.remark" placeholder="备注" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-else>
                <el-col :span="24">
                  <el-form-item label="备注">
                    <el-input v-model="row.remark" placeholder="备注" />
                  </el-form-item>
                </el-col>
              </el-row>
              <div
                v-if="index === localData.length - 1"
                style="text-align: center; margin-top: 10px"
              >
                <el-row :gutter="10">
                  <el-col :span="24">
                    <el-button type="primary" plain @click="addRow" class="w-full"
                      >新增行</el-button
                    >
                  </el-col>
                </el-row>
              </div>
            </el-form>
          </div>
        </div>

        <!-- PC 端 -->
        <el-table v-else :data="localData" border>
          <el-table-column label="序号" type="index" min-width="30" align="center" />
          <el-table-column label="时间段" min-width="180">
            <template #default="{ row }">
              <div class="time-range-row">
                <el-time-select
                  v-model="row.timeStart"
                  placeholder="起始时间"
                  start="08:30"
                  end="23:30"
                  step="00:30"
                  placement="top-start"
                />
                <span class="time-range-separator">-</span>
                <el-time-select
                  v-model="row.timeEnd"
                  placeholder="结束时间"
                  start="08:30"
                  end="23:30"
                  step="00:30"
                  :min-time="row.timeStart"
                  placement="top-start"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="人数" min-width="50">
            <template #default="{ row }">
              <el-input
                v-model="row.number"
                type="number"
                placeholder="人数"
                min="0"
                @input="(val) => (row.number = Number(val))"
              />
            </template>
          </el-table-column>
          <el-table-column label="工时" min-width="50">
            <template #default="{ row }">
              <el-input v-model="row.workingHours" readonly placeholder="工时" />
            </template>
          </el-table-column>
          <el-table-column label="类型" min-width="90" prop="type" v-if="type === 1">
            <template #default="{ row }">
              <el-select v-model="row.type" placeholder="请选择类型" style="width: 100%">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_OFFICIALLY_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            label="来源"
            min-width="90"
            prop="deptCode"
            v-if="type === 5 || type === 4"
          >
            <template #default="{ row }">
              <el-select v-model="row.deptCode" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCTION_DEPT)"
                  clearable
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template #default="{ row }">
              <el-input v-model="row.remark" placeholder="备注" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60" fixed="right">
            <template #default="{ $index }">
              <el-button type="danger" link @click="removeRow($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
const message = useMessage() // 消息弹窗
const props = defineProps<{
  modelValue: any[]
  type: number
}>()

const emit = defineEmits(['update:modelValue'])

const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

// 计算有效分钟数并更新工时
const parseTime = (timeStr: string): Date => {
  const now = new Date()
  const [hours, minutes] = timeStr.split(':').map(Number)
  return new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes)
}

const isRestPeriod = (time: Date): boolean => {
  const restPeriods = [
    { start: '12:00', end: '13:00' },
    { start: '17:30', end: '18:00' }
  ]
  for (const period of restPeriods) {
    const restStart = parseTime(period.start)
    const restEnd = parseTime(period.end)
    if (time >= restStart && time < restEnd) return true
  }
  return false
}

const calculateEffectiveMinutes = (start: string, end: string): number => {
  const startDate = parseTime(start)
  const endDate = parseTime(end)
  if (endDate <= startDate) return 0
  let totalMinutes = 0
  let current = new Date(startDate)
  while (current < endDate) {
    const nextMinute = new Date(current.getTime() + 60000)
    const minuteEndTime = nextMinute > endDate ? endDate : nextMinute
    if (!isRestPeriod(current)) {
      totalMinutes += (minuteEndTime.getTime() - current.getTime()) / 60000
    }
    current = minuteEndTime
  }
  return totalMinutes
}
// 初始化本地数据，默认添加一行空数据
const initializeLocalData = () => {
  // console.log(props.modelValue)
  if (props.modelValue && props.modelValue.length > 0) {
    return props.modelValue.map((item) => ({
      number: item.number,
      workingHours: item.workingHours || 0,
      timeStart: item.timeStart || '',
      timeEnd: item.timeEnd || '',
      timeRange: item.timeRange || '',
      remark: item.remark || '',
      type: item.type,
      lendingProductionLine: item.lendingProductionLine || '',
      deptCode: item.deptCode || '',
      borrowProductionLine: item.borrowProductionLine || ''
    }))
  } else {
    if (props.type === 1) {
      return getIntDictOptions(DICT_TYPE.PRODUCTION_REPORT_OFFICIALLY_TYPE).map((item) => ({
        number: '',
        workingHours: 0,
        timeStart: '08:30',
        timeEnd: '21:00',
        timeRange: '',
        remark: '',
        type: item.value
      }))
    } else {
      //清空原有的数据
      return [
        {
          timeStart: '08:30',
          timeEnd: '21:00',
          number: '',
          remark: '',
          workingHours: 0,
          timeRange: ''
        }
      ]
    }
  }
}

const localData = ref<any[]>(initializeLocalData())

const processedRows = computed(() => {
  return localData.value.map((row) => {
    const minutes = calculateEffectiveMinutes(row.timeStart, row.timeEnd)
    return {
      ...row,
      workingHours: parseFloat(((minutes / 60) * (row.number || 0)).toFixed(3)),
      timeRange: `${row.timeStart}-${row.timeEnd}`
    }
  })
})

// 同步外部传入的数据
watch(
  () => processedRows.value,
  (newRows) => {
    emit('update:modelValue', newRows)
  },
  { deep: true }
)

// 新增一行
const addRow = () => {
  localData.value.push({
    timeStart: '08:30',
    timeEnd: '21:00',
    number: '',
    remark: '',
    type: 0,
    workingHours: 0,
    timeRange: '' // 初始化空值
  })
  emit('update:modelValue', [...localData.value])
  console.log(localData)
  // alert(props.type)
}

// 删除一行
const removeRow = (index: number) => {
  //确认是否删除
  message.confirm('确定要第' + (index + 1) + '行删除吗？').then(() => {
    localData.value.splice(index, 1)
    emit('update:modelValue', [...localData.value])
    console.log(localData)
  })
}

// 工时监听
watch(
  localData,
  (rows) => {
    rows.forEach((row) => {
      const minutes = calculateEffectiveMinutes(row.timeStart, row.timeEnd)
      row.workingHours = parseFloat(((minutes / 60) * (row.number || 0)).toFixed(3))
      row.timeRange = `${row.timeStart}-${row.timeEnd}` // 设置时间范围
    })
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.time-range-row {
  display: flex;
  align-items: center;
  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}
.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}
</style>
