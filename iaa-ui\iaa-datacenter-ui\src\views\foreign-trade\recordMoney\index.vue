<!-- src/views/foreign-trade/collectionInformation/index.vue -->
<template>
  <div v-if="!mobile">
    <PC ref="pcRef" />
  </div>

  <div v-else>
    <Mobile />
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store/modules/app'
import Mobile from '@/views/foreign-trade/recordMoney/components/recordMobile.vue'
import PC from '@/views/foreign-trade/recordMoney/components/recordPC.vue'

const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)
const pcRef = ref()
</script>