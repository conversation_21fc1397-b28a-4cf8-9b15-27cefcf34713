<template>
  <div class="h-[100%]">
    <van-nav-bar title="收款信息登记" fixed />

    <div class="fixed top-44px left-0 right-0 z-10 bg-white">
      <van-tabs v-model:active="activeTab" @change="onTabChange">
        <van-tab title="待认领" name="pending" />
        <van-tab title="我的" name="mine" />
      </van-tabs>
      <van-search
        v-model="selectGlobal"
        @search="handleSearch"
        @cancel="handleSearch"
        placeholder="请输入搜索关键词"
      />
    </div>

    <div ref="scrollEl" class="h-[calc(100vh-230px)] overflow-auto px-10px mt-100px">
      <div v-if="activeTab === 'pending'">
        <van-pull-refresh v-model="loading" @refresh="resetAndLoad">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            :immediate-check="true"
            finished-text="没有更多数据了"
            @load="load"
          >
            <div v-for="item in list" :key="item.id" class="mb-10px">
              <van-card
                :title="item.collectionAccount"
                currency=""
                class="rounded-lg shadow-sm"
              >
                <!-- <template #tags>
                  <van-tag plain type="danger">{{ item.currency }}</van-tag>
                </template> -->
            <template #title>
              <div class="h-[14vh] w-[30vh] p-1">
                <div class="text-sm truncate">付款人：{{ item.payer }}</div>
                <div class="text-sm truncate">收款账户：{{ item.collectionAccount }}</div>
                <div class="text-sm truncate">收款币种：{{ item.currency }}</div>
                <div class="text-sm truncate">收款日期：{{ item.dateStr }}</div>
                <div class="text-sm truncate font-bold">收款金额：{{ item.collectionAmount }}</div>
              </div>
            </template>
                <template #footer>
                  <div class="w-full flex items-center justify-between">
                    <van-checkbox
                      v-if="!query.isMe"
                      v-model="checkedMap[item.id]"
                      @click.stop
                      icon-size="18px"
                    >
                      <span class="text-sm">选择</span>
                    </van-checkbox>
                    <div class="flex items-center gap-2">
                      <van-tag v-if="query.isMe && item.status === 1" type="primary"
                        >认领中</van-tag
                      >
                      <van-tag v-if="item.status === 2" type="success">已认领</van-tag>
                      <van-button
                        v-if="!query.isMe"
                        size="small"
                        round
                        type="primary"
                        v-hasPermi="['collection:information:claim']"
                        @click.stop="oneClickOpenClaim(item)"
                        class="min-w-60px"
                        >认领</van-button
                      >
                      <!-- 添加查看详情按钮 -->
                      <van-button
                        v-else-if="item.status === 2"
                        size="small"
                        round
                        type="default"
                        @click.stop="viewClaimDetail(item)"
                        class="min-w-60px"
                        >详情</van-button
                      >
                      <van-button
                        v-else
                        size="small"
                        round
                        type="default"
                        disabled
                        class="min-w-60px"
                        >不可认领</van-button
                      >
                    </div>
                  </div>
                </template>
              </van-card>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div v-else>
        <van-pull-refresh v-model="loading" @refresh="resetAndLoad">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            :immediate-check="true"
            finished-text="没有更多数据了"
            @load="load"
          >
            <div v-for="item in list" :key="item.id" class="mb-10px">
              <van-card
                :title="getCollectionTitle(item)"
                :desc="`日期：${item.claimDate}`"
                :price="String(item.totalAmount)"
                currency=""
                class="rounded-lg shadow-sm"
              >
                <template #tags>
                  <van-tag plain type="danger">{{ item.currency }}</van-tag>
                </template>

                <template #footer>
                  <div class="w-full flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <van-tag v-if="item.status === 2" type="primary">认领中</van-tag>
                      <van-tag v-if="item.status === 0" type="success">已认领</van-tag>
                    </div>
                    <div class="flex items-center gap-2">
                      <span v-if="item.status === 2" class="flex gap-2">
                        <van-button
                          size="small"
                          round
                          type="primary"
                          @click.stop="continueClaim(item)"
                          v-hasPermi="['collection:information:claim']"
                          class="min-w-60px"
                          >继续</van-button
                        >
                        <van-button
                          size="small"
                          round
                          type="danger"
                          v-hasPermi="['collection:information:cancel']"
                          @click.stop="handleDelete(item.id)"
                          class="min-w-60px"
                          >取消</van-button
                        >
                      </span>
                      <!-- 添加查看详情按钮 -->
                      <div v-if="item.status === 0">
                        <van-button
                          size="small"
                          round
                          type="primary"
                          @click.stop="editClaim(item)"
                          v-hasPermi="['record:money:admin']"
                          class="min-w-60px"
                          >修改</van-button
                        >
                        <van-button
                          size="small"
                          round
                          type="default"
                          @click.stop="viewClaimDetail(item)"
                          class="min-w-60px"
                          >详情</van-button
                        >
                        <van-button
                          size="small"
                          round
                          type="danger"
                          v-hasPermi="['collection:information:cancel']"
                          @click.stop="handleDelete(item.id)"
                          class="min-w-60px"
                          >取消</van-button
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </van-card>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <div
      class="fixed bottom-10 left-0 right-0 bg-white border-t p-10px flex justify-between items-center"
    >
      <div>共 {{ total }} 条</div>
      <div v-if="!isMine">已选 {{ selectedIds.length }} 条</div>
      <van-button
        type="success"
        size="small"
        :disabled="selectedIds.length === 0"
        @click="goClaim(selectedIds)"
        v-hasPermi="['collection:information:claim']"
        v-if="!isMine"
        >批量认领</van-button
      >
    </div>

    <ClaimMobile
      v-model:show="claimPopupShow"
      :ids="claimIds"
      :prefill="prefillData"
      :isEdit="isEdit"
      @success="resetAndLoad"
    />
  </div>
</template>

<script lang="ts" setup>
import { InformationApi } from '@/api/foreign-trade/collectionInformation/index'
import { ClaimApi } from '@/api/foreign-trade/collectionInformation/claim'
import ClaimMobile from './ClaimMobile.vue'
import { showFailToast, showConfirmDialog, showSuccessToast } from 'vant'

const activeTab = ref<'pending' | 'mine'>('pending')
const isMine = computed(() => activeTab.value === 'mine')
const prefillData = ref<any>(null)
const selectGlobal = ref('')
const isEdit = ref(false)

const query = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  collectionAccount: '',
  status: [0],
  isMe: false,
  global: '' // 添加全局搜索字段
})

const list = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const finished = ref(false)
const checkedMap = reactive<Record<number, boolean>>({})

const selectedIds = computed(() =>
  Object.keys(checkedMap)
    .filter((k) => checkedMap[k as any])
    .map((k) => Number(k))
)

// 统一的搜索处理方法
const handleSearch = () => {
  query.global = selectGlobal.value
  resetAndLoad()
}

const scrollEl = ref<HTMLElement | null>(null)

// 添加币种检查相关方法（放在其他方法定义区域）
// 检查所选收款记录的币种是否一致
const checkCurrencyConsistency = (selectedItems: any[]) => {
  if (selectedItems.length === 0) {
    console.log('没有选中的项目')
    return true
  }

  const firstCurrency = selectedItems[0].currency
  console.log('第一个币种:', firstCurrency)

  const isConsistent = selectedItems.every((item) => {
    console.log(`项目ID: ${item.id}, 币种: ${item.currency}`)
    return item.currency === firstCurrency
  })

  console.log('币种是否一致:', isConsistent)
  return isConsistent
}

const resetAndLoad = () => {
  list.value = []
  total.value = 0
  query.pageNo = 1
  finished.value = false
  loading.value = false
  clearSelected()
  // 重置滚动条
  nextTick(() => scrollEl.value?.scrollTo({ top: 0 }))
  load()
}

// 专门用于清空选中项的方法
const clearSelected = () => {
  for (const k in checkedMap) {
    delete checkedMap[k as any]
  }
}
const onTabChange = () => {
  // 待认领：status=[0], isMe=false；我的：status=[1,2], isMe=true
  if (activeTab.value === 'mine') {
    query.status = [1, 2]
    query.isMe = true
  } else {
    query.status = [0]
    query.isMe = false
    // 清空已选，避免在“我的”里勾选状态被带回
    for (const k in checkedMap) delete checkedMap[k as any]
  }
  // 切换标签时清空搜索关键词
  selectGlobal.value = ''
  query.global = ''
  resetAndLoad()
}

watch(
  () => query.dateStr,
  () => resetAndLoad()
)
// 加载函数：增加 loading 防抖，避免切换标签或初始化时的双触发
const load = async () => {
  if (finished.value) return
  loading.value = true
  try {
    let data: any

    if (activeTab.value === 'mine') {
      data = await ClaimApi.getClaimPage({
        pageSize: query.pageSize,
        pageNo: query.pageNo,
        global: query.global,
        type: 1
      })
    } else {
      data = await InformationApi.getInformationPage({
        ...query,
        pageNo: query.pageNo // 确保传递当前页码
      })
    }
    total.value = data?.total || 0
    const rows = data?.list || []
    // 去重，避免切换标签或翻页时重复
    const existed = new Set(list.value.map((x: any) => x.id))
    const newRows = rows.filter((x: any) => !existed.has(x.id))
    // 第一页时替换数据，后续页追加数据
    if (query.pageNo === 1) {
      list.value = rows
    } else {
      list.value.push(...rows)
    }

    // 初始化勾选map（仅对可继续认领的项）
    newRows.forEach((row: any) => {
      if (checkedMap[row.id] === undefined) checkedMap[row.id] = false
    })

    // 加载状态结束
    loading.value = false

    // 判断是否还有更多数据
    // 修改判断逻辑：当前列表总数 >= 总数时，表示没有更多数据
    if (list.value.length >= total.value) {
      finished.value = true
    } else {
      query.pageNo++
    }
  } catch (error) {
    // 发生错误时也结束加载状态
    loading.value = false
    finished.value = true
    console.error('加载数据出错:', error)
  }
}

const claimPopupShow = ref(false)
const claimIds = ref<number[]>([])
const openClaim = (ids: number[]) => {
  claimIds.value = ids
  claimPopupShow.value = true
}
onMounted(() => {
  // 获取列表数据
  load()
})
const oneClickOpenClaim = (item: any) => {
  // 对于单个认领，币种肯定是一致的，可以直接认领
  prefillData.value = null
  openClaim([item.id])
}

// 添加计算收款信息标题的方法
const getCollectionTitle = (item: any) => {
  let title = '' // 默认为 customerName

  if (item.collectionList && item.collectionList.length > 0) {
    const collectionInfo = item.collectionList
      .map((collection) => `${collection.collectionAccount}-${collection.collectionAmount}`)
      .join(', ')

    // 如果 collectionInfo 不为空，则将其添加到 title 中
    if (collectionInfo) {
      title += ` ${collectionInfo}`
    }
  }

  return title
}

const continueClaim = async (item: any) => {
  try {
    const detail = await InformationApi.getSuspended(item.id)
    // 将预填数据存入一个全局临时对象，通过弹层 props 传入
    prefillData.value = detail || null
    openClaim([item.id])
  } catch (e) {
    console.error(e)
  }
}

const editClaim = async (item: any) => {
  try {
    isEdit.value = true
    const detail = await InformationApi.getSuspended(item.id)
    // 将预填数据存入一个全局临时对象，通过弹层 props 传入
    prefillData.value = detail || null
    openClaim([item.id])
  } catch (e) {
    console.error(e)
  }
}

const handleDelete = async (id?: number) => {
  showConfirmDialog({
    title: '取消认领',
    message: '是否确认取消认领该笔收款？'
  })
    .then(async () => {
      // on confirm
      loading.value = true
      await ClaimApi.deleteClaim([id])
      loading.value = false
      showSuccessToast('取消成功')
      resetAndLoad()
    })
    .catch(() => {
      // on cancel
    })
}

// 添加查看详情的方法
const viewClaimDetail = async (item: any) => {
  try {
    const detail = await InformationApi.getSuspended(item.id)
    // 将详情数据作为预填数据传入，但设置为只读模式
    prefillData.value = {
      ...detail,
      readOnly: true // 添加只读标识
    }
    openClaim([item.id])
  } catch (e) {
    console.error(e)
  }
}

const goClaim = async (ids: number[]) => {
  console.log('selectIds:', selectedIds.value)

  if (ids.length === 0) {
    showFailToast('请选择收款记录')
    return
  }
  // 直接从当前列表中获取选中的收款记录
  const selectedItems = list.value.filter((item) => ids.includes(item.id))

  console.log('selectedItems:', selectedItems)

  // 检查币种是否一致
  if (!checkCurrencyConsistency(selectedItems)) {
    showFailToast('所选收款记录币种不一致，无法批量认领')
    return
  }
  prefillData.value = null
  openClaim(ids)
}
</script>

<style scoped>
.border-t {
  border-top: 1px solid #f5f5f5;
}

:deep(.van-card) {
  background-color: #ffffff !important;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
}

:deep(.van-card__header) {
  padding: 12px 16px 8px !important;
}

:deep(.van-card__content) {
  padding: 0 16px !important;
}

:deep(.van-card__footer) {
  padding: 12px 16px !important;
  border-top: 1px solid #f2f3f5 !important;
}

.min-w-60px {
  min-width: 60px;
}
</style>
