import request from '@/config/axios'

/** 获取ERP项目列 */
export const getProrjectTableColumn = async () => {
  return await request.get({ url: '/butt-joint/erp/project/get-columns' })
}
/** 获取ERP项目分页 */
export const getErpProjectPage = async (params: any) => {
  return await request.get({ url: '/butt-joint/erp/project/page', params })
}

export const getJstToken = async () => {
  return await request.get({ url: '/butt-joint/erp/project/get-jst-token' })
}

export const updateJstToken = async (token: string) => {
  return await request.post({ url: '/butt-joint/erp/project/update-jst-token?token=' + token })
}
