<template>
  <ContentWrap>
    <div class="flex justify-between items-center">
      <div class="text-15px font-bold text-#3c6382 flex">
        <div class="bg-#74b9ff text-#fff p-3px rounded-3px">版本时间：{{ versionTime }} </div>
        <div class="ml-30px bg-#00b894 text-#fff p-3px rounded-3px max-w-200px">
          <el-tooltip placement="top">
            <template #content>
              <div class="tooltip-content">
                <div v-for="(item, index) in changeLogItems" :key="index">{{ item }}</div>
              </div>
            </template>
            <span class="change-log">{{ changeLog }}</span>
          </el-tooltip>
        </div>
      </div>
      <el-radio-group v-model="showGroup">
        <el-radio-button label="所有数据" value="all" />
        <el-radio-button label="生命周期属性" value="life-cycle" />
        <el-radio-button label="规格属性" value="spec" />
        <el-radio-button label="包装属性" value="packing" />
        <el-radio-button label="推广属性" value="promotion" />
        <el-radio-button label="认证报告" value="certification" />
      </el-radio-group>
      <div class="button-group">
        <el-button circle class="mr-10px" @click="handleHistory()" title="查看历史更新记录">
          <Icon icon="ep:document" />
        </el-button>
        <el-button
          circle
          class="mr-10px"
          @click="databaseUploadFormRef?.open()"
          title="上传"
          v-hasPermi="['report:products:import']"
        >
          <Icon icon="ep:upload" />
        </el-button>
        <el-button
          circle
          class="mr-10px"
          @click="handleExport()"
          title="导出"
          v-hasPermi="['report:products:export']"
        >
          <Icon icon="ep:download" />
        </el-button>
      </div>
    </div>
    <div class="mt-10px">
      <div class="h-[calc(100vh-250px)]">
        <vxe-table
          ref="tableRef"
          show-footer
          border
          stripe
          align="center"
          show-overflow
          height="100%"
          size="mini"
          :data="list"
          :row-class-name="getRowClassName"
          :footer-cell-config="{ height: 30 }"
          :cell-config="{ height: 30 }"
          :row-config="{ keyField: 'id' }"
          :row-style="{cursor:'pointer'}"
          :footer-cell-style="{ padding: 0, background: '#dcefdc', border: '1px solid #ebeef5' }"
          :filter-config="{}"
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          :loading="loading"
          :menu-config="menuConfig"
          :checkbox-config="{ reserve: true, highlight: true, range: true }"
          :edit-config="{
            trigger: 'manual',
            mode: 'cell',
            autoClear: true,
            enabled:false
            // enabled: checkPermi(['report:prducts:update'])
          }"
          @cell-click="handleCellDbClick"
          @edit-closed="saveEdit"
          @edit-actived="onEditActived"
          @edit-disabled="onEditDisabled"
          @menu-click="menuClickEvent"
          @filter-change="handleFilterChange"
        >
          <vxe-column type="checkbox" width="40" field="id" fixed="left" />
          <vxe-colgroup title="产品基本属性" id="col_1" field="base" key="0">
            <vxe-column
              field="imageUrl"
              title="缩略图"
              width="60"
              :cell-render="imgUrlCellRender"
              fixed="left"
            />
            <vxe-column
              title="型号"
              field="model"
              width="150"
              align="left"
              :filters="filterValue.model"
              :filter-render="FilterValue.textFilterRender"
              fixed="left"
            />
            <vxe-column
              title="操控方式"
              field="operationMode"
              width="100"
              :filters="filterValue.operationMode"
              :filter-render="FilterValue.textFilterRender"
            />

            <vxe-column
              title="机箱颜色"
              field="chassisColor"
              width="100"
              :filters="filterValue.chassisColor"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="主型号归集"
              field="mainModel"
              width="150"
              align="left"
              :filters="filterValue.mainModel"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="产品分类"
              field="productsType"
              width="120"
              align="center"
              :filters="FilterValue.productsTypeOptions"
              :edit-render="{
                name: '$select',
                options: getIntDictOptions(DICT_TYPE.PRODUCTS_TYPE),
                props: { value: 'value', label: 'label' }
              }"
            >
              <template #default="{ row }">
                <DictTag :type="'products_type'" :value="row.productsType" />
              </template>
            </vxe-column>

            <vxe-column
              title="场景分类"
              field="sceneType"
              width="120"
              align="center"
              :filters="FilterValue.sceneTypeOptions"
              :edit-render="{
                name: '$select',
                options: getIntDictOptions(DICT_TYPE.SCENE_TYPE),
                props: { value: 'value', label: 'label' }
              }"
            >
              <template #default="{ row }">
                <DictTag :type="'scene_type'" :value="row.sceneType" />
              </template>
            </vxe-column>

            <vxe-column
              title="技术分类"
              field="technologyType"
              width="120"
              align="center"
              :filters="FilterValue.technologyTypeOptions"
              :edit-render="{
                name: '$select',
                options: getIntDictOptions(DICT_TYPE.TECHNOLOGY_TYPE),
                props: { value: 'value', label: 'label' }
              }"
            >
              <template #default="{ row }">
                <DictTag :type="'technology_type'" :value="row.technologyType" />
              </template>
            </vxe-column>
          </vxe-colgroup>

          <vxe-colgroup
            title="生命周期属性"
            id="col_2"
            field="cycle"
            v-if="['all', 'life-cycle'].includes(showGroup)"
            key="1"
          >
            <vxe-column
              title="生命周期"
              field="lifeCycle"
              width="120"
              align="center"
              :filters="FilterValue.lifeCycleOptions"
              :edit-render="{
                name: '$select',
                options: getIntDictOptions(DICT_TYPE.LIFE_CYCLE),
                props: { value: 'value', label: 'label' }
              }"
            >
              <template #default="{ row }">
                <DictTag :type="'life_cycle'" :value="row.lifeCycle" />
              </template>
            </vxe-column>

            <vxe-column
              title="定制/中性"
              field="customizedNeutral"
              width="120"
              align="center"
              :filters="FilterValue.customizedNeutralOptions"
              :edit-render="{
                name: '$select',
                options: getIntDictOptions(DICT_TYPE.CUSTOMIZED_NEUTRAL),
                props: { value: 'value', label: 'label' }
              }"
            >
              <template #default="{ row }">
                <DictTag :type="'customized_neutral'" :value="row.customizedNeutral" />
              </template>
            </vxe-column>

            <vxe-column
              title="产品分组"
              field="productsGroup"
              width="150"
              align="left"
              :filters="filterValue.productsGroup"
              :filter-render="FilterValue.textFilterRender"
            />

            <vxe-column
              field="listedTime"
              min-width="100"
              title="上市时间"
              :filters="filterValue.listed"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              field="delistingTime"
              min-width="100"
              title="退市时间"
              :filters="filterValue.delisting"
              :filter-render="FilterValue.textFilterRender"
            />

            <vxe-column
              title="退市替代方案"
              field="delistingProgramme"
              width="100"
              align="center"
              :filters="filterValue.programme"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="起订量"
              min-width="70"
              field="moq"
              :filters="filterValue.moq"
              :filter-render="FilterValue.numberFilterRender"
            />
            <vxe-column
              title="开机费(￥)"
              min-width="90"
              field="startupFee"
              :filters="filterValue.startupFee"
              :filter-render="FilterValue.numberFilterRender"
            />
            <vxe-column
              title="独家情况"
              min-width="90"
              field="remark"
              :filters="filterValue.remark"
              :filter-render="FilterValue.textFilterRender"
            />
          </vxe-colgroup>

          <vxe-colgroup
            title="规格属性"
            id="col_3"
            field="specification"
            v-if="['all', 'spec'].includes(showGroup)"
            key="2"
          >
            <vxe-column
              title="产品尺寸"
              width="90"
              field="productSize"
              :filters="filterValue.productSize"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="材料"
              width="70"
              field="material"
              :filters="filterValue.material"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="覆盖范围"
              width="90"
              field="coverageArea"
              :filters="filterValue.coverageArea"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="瓶子容量"
              width="90"
              field="bottleCapacity"
              :filters="filterValue.bottleCapacity"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="雾化量"
              width="70"
              field="atomizationVolume"
              :filters="filterValue.atomizationVolume"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="噪音（30cm）"
              width="120"
              field="noiseThirty"
              :filters="filterValue.noiseThirty"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="噪音（1m）"
              width="110"
              field="noiseMeter"
              :filters="filterValue.noiseMeter"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="供电方式"
              width="90"
              field="supplyMode"
              :filters="filterValue.supplyMode"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="适用适配器"
              width="100"
              field="applicableAdapter"
              :filters="filterValue.applicableAdapter"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="额定功率"
              width="90"
              field="ratedPower"
              :filters="filterValue.ratedPower"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="电池类型"
              width="90"
              field="batteryType"
              :filters="filterValue.batteryType"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="电池容量"
              width="90"
              field="batteryCapacity"
              :filters="filterValue.batteryCapacity"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="电池续航"
              width="90"
              field="batteryLife"
              :filters="filterValue.batteryLife"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="气泵寿命"
              width="90"
              field="airPumpLife"
              :filters="filterValue.airPumpLife"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="雾化芯/片选型"
              width="120"
              field="atomizationCore"
              :filters="filterValue.atomizationCore"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="风扇转速"
              width="90"
              field="fanSpeed"
              :filters="filterValue.fanSpeed"
              :filter-render="FilterValue.textFilterRender"
            />
          </vxe-colgroup>
          <vxe-colgroup
            title="包装属性"
            id="col_4"
            field="packaging"
            v-if="['all', 'packing'].includes(showGroup)"
            key="3"
          >
            <vxe-column
              title="单个净重"
              width="90"
              field="singleWeight"
              :filters="filterValue.singleWeight"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="单个毛重"
              width="90"
              field="singleMz"
              :filters="filterValue.singleMz"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="整箱台数"
              width="90"
              field="boxTotal"
              :filters="filterValue.boxTotal"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="整箱尺寸"
              width="90"
              field="boxSize"
              :filters="filterValue.boxSize"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="整箱毛重"
              width="90"
              field="boxMz"
              :filters="filterValue.boxMz"
              :filter-render="FilterValue.textFilterRender"
            />
          </vxe-colgroup>
          <vxe-colgroup
            title="推广属性"
            id="col_5"
            field="promotion"
            v-if="['all', 'promotion'].includes(showGroup)"
            key="4"
          >
            <vxe-column
              title="基本卖点"
              width="90"
              field="sellingPoints"
              :filters="filterValue.sellingPoints"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="海报(链接)"
              width="100"
              field="posterUrl"
              :filters="filterValue.posterUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.posterUrl) }}</span>
                  <el-button
                    v-if="row.posterUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.posterUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="详情页(链接)"
              width="120"
              field="detailsUrl"
              :filters="filterValue.detailsUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.detailsUrl) }}</span>
                  <el-button
                    v-if="row.detailsUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.detailsUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="视频(链接)"
              width="100"
              field="videoUrl"
              :filters="filterValue.videoUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.videoUrl) }}</span>
                  <el-button
                    v-if="row.videoUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.videoUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="包材(链接)"
              width="100"
              field="packagingUrl"
              :filters="filterValue.packagingUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.packagingUrl) }}</span>
                  <el-button
                    v-if="row.packagingUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.packagingUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="说明书(链接)"
              width="120"
              field="instructionUrl"
              :filters="filterValue.instructionUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.instructionUrl) }}</span>
                  <el-button
                    v-if="row.instructionUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.instructionUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup
            title="认证报告"
            id="col_6"
            field="certification"
            v-if="['all', 'certification'].includes(showGroup)"
            key="5"
          >
            <vxe-column
              title="CE(链接)"
              width="100"
              field="ceUrl"
              :filters="filterValue.ceUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.ceUrl) }}</span>
                  <el-button
                    v-if="row.ceUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.ceUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="RoHS(链接)"
              width="100"
              field="rohsUrl"
              :filters="filterValue.rohsUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.rohsUrl) }}</span>
                  <el-button
                    v-if="row.rohsUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.rohsUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="FCC(链接)"
              width="100"
              field="fccUrl"
              :filters="filterValue.fccUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.fccUrl) }}</span>
                  <el-button
                    v-if="row.fccUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.fccUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              title="ISED(链接)"
              width="100"
              field="isedUrl"
              :filters="filterValue.isedUrl"
              :filter-render="FilterValue.textFilterRender"
            >
              <template #default="{ row }">
                <div class="path-with-copy">
                  <span class="path-text">{{ formatPathDisplay(row.isedUrl) }}</span>
                  <el-button
                    v-if="row.isedUrl"
                    class="copy-btn"
                    size="small"
                    type="primary"
                    link
                    @click="copyPathToClipboard(row.isedUrl)"
                    title="复制路径"
                  >
                    <Icon icon="ep:document-copy" />
                  </el-button>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="创建时间" min-width="160" field="createTime">
              <template #default="{ row }">
                {{ row.createTime && formatToDateTime(row.createTime) }}
              </template>
            </vxe-column>
            <vxe-column title="更新时间" min-width="160" field="updateTime">
              <template #default="{ row }">
                {{ row.updateTime && formatToDateTime(row.updateTime) }}
              </template>
            </vxe-column>
          </vxe-colgroup>
          <!-- <vxe-column
            title="操作"
            width="100"
            fixed="right"
            v-hasPermi="['report:prducts:update', 'report:products:delete']"
          >
            <template #default="{ row }">
              <el-button
                @click="handleEdit(row)"
                link
                type="primary"
                v-hasPermi="['report:prducts:update']"
              >
                编辑
              </el-button>
              <el-button
                @click="handleDelete(row.id)"
                link
                type="danger"
                v-hasPermi="['report:products:delete']"
              >
                删除
              </el-button>
            </template>
          </vxe-column> -->
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="handleList"
      />
    </div>
  </ContentWrap>
  <DatabaseUploadForm ref="databaseUploadFormRef" @success="handleList()" />
  <!-- 批量删除确认对话框 -->
  <Dialog title="批量删除确认" v-model="batchDeleteVisible">
    <div>当前选中：{{ selectionData.length }} 条数据，确认要删除吗？</div>
    <template #footer>
      <el-button type="danger" @click="confirmBatchDelete()">确认删除</el-button>
      <el-button @click="batchDeleteVisible = false">取消</el-button>
    </template>
  </Dialog>

  <!-- 历史更新记录抽屉 -->
  <HistoryDrawer ref="historyDrawerRef" />

  <!-- 编辑抽屉 -->
  <EditDrawer ref="editDrawerRef" @success="handleList" />
</template>
<script lang="ts" setup>
import DatabaseUploadForm from '@/views/report/products/publicity/components/DatabaseUploadForm.vue'
import HistoryDrawer from '@/views/report/products/publicity/components/HistoryDrawer.vue'
import EditDrawer from '@/views/report/products/publicity/components/EditDrawer.vue'
import { formatToDateTime, formatToDate } from '@/utils/dateUtil'
import * as FilterValue from '@/utils/Filter'
import { PublicityApi } from '@/api/products/publicity'
import download from '@/utils/download'
import { checkPermission } from '@/store/modules/permission'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import type { VxeColumnPropTypes } from 'vxe-table'
import { checkPermi } from '@/utils/permission'
import { ElMessageBox } from 'element-plus'

const filterValue = ref({
  model: [{ data: '' }],
  operationMode: [{ data: '' }],
  chassisColor: [{ data: '' }],
  mainModel: [{ data: '' }],
  productsGroup: [{ data: '' }],
  listed: [{ data: '' }],
  delisting: [{ data: '' }],
  programme: [{ data: '' }],
  moq: [{ data: { condition: '10', value: undefined } }],
  startupFee: [{ data: { condition: '10', value: undefined } }],
  remark: [{ data: '' }],
  productSize: [{ data: '' }],
  material: [{ data: '' }],
  coverageArea: [{ data: '' }],
  bottleCapacity: [{ data: '' }],
  atomizationVolume: [{ data: '' }],
  noiseThirty: [{ data: '' }],
  noiseMeter: [{ data: '' }],
  supplyMode: [{ data: '' }],
  applicableAdapter: [{ data: '' }],
  ratedPower: [{ data: '' }],
  batteryType: [{ data: '' }],
  batteryCapacity: [{ data: '' }],
  batteryLife: [{ data: '' }],
  airPumpLife: [{ data: '' }],
  atomizationCore: [{ data: '' }],
  fanSpeed: [{ data: '' }],
  singleWeight: [{ data: '' }],
  singleMz: [{ data: '' }],
  boxTotal: [{ data: '' }],
  boxSize: [{ data: '' }],
  boxMz: [{ data: '' }],
  sellingPoints: [{ data: '' }],
  posterUrl: [{ data: '' }],
  detailsUrl: [{ data: '' }],
  videoUrl: [{ data: '' }],
  packagingUrl: [{ data: '' }],
  instructionUrl: [{ data: '' }],
  ceUrl: [{ data: '' }],
  rohsUrl: [{ data: '' }],
  fccUrl: [{ data: '' }],
  isedUrl: [{ data: '' }]
})

const delistingTime = ref([{ data: [] }])
const message = useMessage() // 消息弹窗
// 版本时间和变更记录
const versionTime = ref()
const changeLog = ref()
const showGroup = ref('all')

const databaseUploadFormRef = ref()
const historyDrawerRef = ref()
const editDrawerRef = ref()
const loading = ref(false) // 列表的加载中

const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 50,
  // 支持查询的字段
  model: undefined,
  productsType: undefined,
  operationMode: undefined,
  chassisColor: undefined,
  mainModel: undefined,
  sceneType: undefined,
  technologyType: undefined,
  lifeCycle: undefined,
  customizedNeutral: undefined,
  productsGroup: undefined,
  listedTime: undefined,
  delistingTime: undefined
})

const imgUrlCellRender = reactive<VxeColumnPropTypes.CellRender>({
  name: 'VxeImage',
  props: {
    width: 36,
    height: 36
  }
})
// 批量删除相关
const batchDeleteVisible = ref(false)
const selectionData = ref<any[]>([])
const tableRef = ref()
const menuConfig = reactive<any>({
  body: {
    options: [[{ code: 'batchDelete', name: '批量删除' }]]
  }
})
// 右键菜单点击事件
const menuClickEvent = ({ menu }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'batchDelete':
      const rows = $table.getCheckboxRecords()
      if (rows.length === 0) {
        message.alertError('请选择要删除的数据')
        selectionData.value = []
        return
      }
      selectionData.value = rows
      batchDeleteVisible.value = true
      break
  }
}
// 行样式控制
const getRowClassName = ({ row }) => {
  // 如果生命周期字段为“已退市”，返回自定义样式类名

  if (row.lifeCycle === '9') {
    return 'row-retired'
  }
  return ''
}
// 批量删除确认
const confirmBatchDelete = async () => {
  loading.value = true
  try {
    let isAudit = true
    //判断是否有删除的权限
    if (!checkPermission(['report:products:delete'])) {
      isAudit = false
      message.error('您没有删除权限，请联系管理员！')
      return
    } else {
      const ids = selectionData.value.map((item) => item.id)
      await PublicityApi.batchDelete(ids)
      message.success('批量删除成功')
      batchDeleteVisible.value = false
      selectionData.value = []
    }
    // 删除后查询最新的数据
    handleList()
  } catch {
    message.error('批量删除失败')
  } finally {
    loading.value = false
  }
}

const handleList = async () => {
  loading.value = true
  const res = await PublicityApi.getList(queryParams)
  list.value = res.list
  total.value = res.total
  getLastChangeLog()
  loading.value = false
}
//获取最新的一条变更记录日志
const getLastChangeLog = async () => {
  try {
    const res = await PublicityApi.getLastChangeLog()
    if (res && res.editContent) {
      changeLog.value = res.editContent
    }
    if (res && res.versionTime && Array.isArray(res.versionTime) && res.versionTime.length >= 3) {
      const date = new Date(res.versionTime[0], res.versionTime[1] - 1, res.versionTime[2])
      versionTime.value = formatToDate(date) // 输出：2025/7/17
    } else {
      versionTime.value = formatToDate(new Date()) // 使用当前日期作为默认值
    }
  } catch (error) {
    console.error('获取变更记录失败:', error)
    changeLog.value = '暂无变更记录'
    versionTime.value = formatToDate(new Date())
  }
}

// 将变更记录拆分成数组
const changeLogItems = computed(() => {
  if (!changeLog.value) return []
  return changeLog.value
    .split(';')
    .map((item: string) => item.trim())
    .filter((item) => item.length > 0)
})

// 处理路径显示格式 - 只显示目录部分
const formatPathDisplay = (path: string) => {
  if (!path) return ''
  return path // 显示完整路径
}

// 处理复制路径 - 根据路径类型决定复制内容
const getDirectoryPath = (fullPath: string) => {
  if (!fullPath) return ''

  // 如果是网络路径或普通链接，直接返回
  if (fullPath.startsWith('http') || fullPath.startsWith('https')) {
    return fullPath
  }

  // 常见的文件扩展名正则表达式
  const fileExtensionRegex =
    /\.(exe|dll|doc|docx|pdf|jpg|png|gif|bmp|txt|xls|xlsx|ppt|pptx|zip|rar|7z|mp3|mp4|avi|mov|wmv|mkv|flv|swf|iso|msi|apk|ipa|dmg|pkg|deb|rpm|jar|war|ear|html|htm|css|js|json|xml|csv|md|log|ini|cfg|config|bak|tmp|temp)$/i

  if (fileExtensionRegex.test(fullPath)) {
    // 如果路径以文件扩展名结尾，则去除文件名部分
    const lastSlashIndex = Math.max(fullPath.lastIndexOf('\\'), fullPath.lastIndexOf('/'))
    if (lastSlashIndex > 0) {
      return fullPath.substring(0, lastSlashIndex)
    }
    return fullPath
  } else {
    // 如果不是以文件扩展名结尾，直接返回完整路径
    return fullPath
  }
}

// 复制路径到剪贴板
const copyPathToClipboard = (fullPath: string) => {
  if (!fullPath) {
    ElMessage.warning('路径为空')
    return
  }

  // 获取目录路径
  const directoryPath = getDirectoryPath(fullPath)

  // 复制到剪贴板
  copyToClipboard(directoryPath)
  ElMessage.success('路径已复制到剪贴板: ' + directoryPath)
}

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).catch((err) => {
      console.error('复制失败:', err)
      fallbackCopyTextToClipboard(text)
    })
  } else {
    fallbackCopyTextToClipboard(text)
  }
}

// 降级复制方法
const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-9999px'
  textArea.style.top = '-9999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    if (!successful) {
      ElMessage.error('复制失败')
    }
  } catch (err) {
    ElMessage.error('复制失败')
    console.error('复制操作不被支持', err)
  }

  document.body.removeChild(textArea)
}

const handleFilterChange = (params: any) => {
  console.log(params)

  // 特定字段列表
  const specialFields = [
    'productsType',
    'sceneType',
    'technologyType',
    'lifeCycle',
    'customizedNeutral'
  ]

  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item

    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  handleList()
}
const handleDelete = (id: number) => {
  //删除二次确认
  ElMessageBox.confirm('此操作将永久删除该数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await PublicityApi.batchDelete([id])
    handleList()
    message.success('删除成功')
  })
}

const exportLoading = ref(false) // 导出的加载中

/** 查看历史更新记录 */
const handleHistory = () => {
  historyDrawerRef.value?.open()
}

/** 双击打开编辑抽屉 */
const handleEdit: any = (row: any) => {
  editDrawerRef.value?.open('update', row.id, row)
}

// 当前正在编辑的信息
const currentEditInfo = ref({ rowId: null, field: null })

const onEditActived = ({ row, column }) => {
  const field = column.field
  const rowId = row.id
  console.log('编辑激活:', field, rowId)

  // 记录当前编辑信息
  currentEditInfo.value = { rowId, field }
}

const onEditDisabled = ({ row, column }) => {
  const field = column.field
  const rowId = row.id
  console.log('编辑结束:', field, rowId)

  // 清除当前编辑信息
  if (currentEditInfo.value.rowId === rowId && currentEditInfo.value.field === field) {
    currentEditInfo.value = { rowId: null, field: null }
  }
}

// 处理单元格双击事件
const handleCellDbClick = ({ row, column, cell }) => {
  // const field = column.field
  
  // // 需要排除的字段（这些字段已经有双击编辑功能）
  // const excludedFields = [
  //   'productsType', 
  //   'sceneType', 
  //   'technologyType', 
  //   'lifeCycle', 
  //   'customizedNeutral'
  // ]
  
  // // 如果是排除的字段，不执行打开编辑页面的操作
  // if (excludedFields.includes(field)) {
  //   handleDbEdit({ row, column })
  //   return
  // }
  
  // 对于其他字段，打开编辑抽屉
  handleEdit(row)
}

// 保留原有的双击编辑处理函数
const handleDbEdit = ({ row, column }) => {
  const field = column.field
  console.log('双击编辑:', field, row[field])

  // 如果正在保存，不允许新的编辑
  if (isSaving.value) {
    console.log('正在保存中，不允许新的编辑')
    return
  }

  // 仅允许编辑指定字段
  if (
    ['productsType', 'sceneType', 'technologyType', 'lifeCycle', 'customizedNeutral'].includes(
      field
    )
  ) {
    const $table = tableRef.value

    // 安全检查：确保table实例存在
    if (!$table) {
      console.error('表格实例不存在')
      return
    }

    // 直接设置编辑单元格
    try {
      $table.setEditCell(row, field)
    } catch (error) {
      console.error('设置编辑单元格失败:', error)
    }
  }
}
// 防止重复保存的标志
const isSaving = ref(false)
// 记录上次编辑的信息，用于防重复
const lastEditInfo = ref({ rowId: null, field: null, value: null, timestamp: 0 })
// 保存队列，用于防止快速切换时的重复保存
const saveQueue = ref(new Set())

const saveEdit = async ({ row, column }) => {
  const field = column.field
  const currentValue = row[field]
  const rowId = row.id
  const timestamp = Date.now()

  // 创建唯一标识
  const saveKey = `${rowId}-${field}-${currentValue}`

  console.log('保存编辑:', field, '新值:', currentValue, '行ID:', rowId, '时间戳:', timestamp)
  console.log('当前编辑信息:', currentEditInfo.value)

  // 检查是否是重复的保存请求
  if (isSaving.value) {
    console.log('正在保存中，跳过重复请求')
    return
  }

  // 检查保存队列中是否已存在相同的保存请求
  if (saveQueue.value.has(saveKey)) {
    console.log('保存队列中已存在相同请求，跳过保存')
    return
  }

  // 检查是否与上次编辑信息相同，并且时间间隔很短（防止快速切换触发的重复保存）
  const lastEdit = lastEditInfo.value
  const timeDiff = timestamp - lastEdit.timestamp
  if (
    lastEdit.rowId === rowId &&
    lastEdit.field === field &&
    lastEdit.value === currentValue &&
    timeDiff < 1000
  ) {
    console.log('与上次编辑信息相同且时间间隔很短，跳过保存')
    return
  }

  // 关键检查：只有当前正在编辑的字段才允许保存
  // 这可以防止切换到其他可编辑字段时触发的误保存
  if (currentEditInfo.value.rowId !== null && currentEditInfo.value.field !== null) {
    if (currentEditInfo.value.rowId !== rowId || currentEditInfo.value.field !== field) {
      console.log(
        '不是当前正在编辑的字段，跳过保存。当前编辑:',
        currentEditInfo.value,
        '触发保存:',
        { rowId, field }
      )
      return
    }
  }

  if (
    ['productsType', 'sceneType', 'technologyType', 'lifeCycle', 'customizedNeutral'].includes(
      field
    )
  ) {
    try {
      // 添加到保存队列
      saveQueue.value.add(saveKey)
      isSaving.value = true

      // 记录本次编辑信息
      lastEditInfo.value = { rowId, field, value: currentValue, timestamp }

      // 调用API更新数据
      //判断是否有权限
      if (!checkPermission(['report:prducts:update'])) {
        message.error('您没有修改数据的权限，请联系管理员！')
        return
      } else {
        const result = await PublicityApi.update(row)
        if (result) {
          ElMessage.success('更新成功')
          // 刷新列表数据
          await handleList()
        }
      }
    } catch (error) {
      ElMessage.error('更新失败')
      console.error('保存失败:', error)
      // 刷新列表以恢复原始数据
      await handleList()
    } finally {
      isSaving.value = false

      // 从保存队列中移除
      saveQueue.value.delete(saveKey)

      // 确保编辑状态被清除
      const $table = tableRef.value
      if ($table) {
        try {
          $table.clearEdit()
        } catch (error) {
          console.warn('清除编辑状态失败:', error)
        }
      }

      // 延迟清除编辑信息，避免快速切换时的问题
      setTimeout(() => {
        // 只有当前保存队列为空时才清除编辑信息
        if (saveQueue.value.size === 0) {
          lastEditInfo.value = { rowId: null, field: null, value: null, timestamp: 0 }
          currentEditInfo.value = { rowId: null, field: null }
        }
      }, 500)
    }
  }
}

/** 导出产品数据按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PublicityApi.exportDay(queryParams)
    download.excel(data, `产品数据_版本时间${versionTime.value}.xlsx`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 禁止 tooltip 复制
const disableTooltipCopy = () => {
  // 使用事件委托，监听所有 tooltip 的复制事件
  document.addEventListener('copy', (event) => {
    const selection = window.getSelection()
    if (selection && selection.toString()) {
      // 检查选中的文本是否来自 tooltip
      const anchorNode = selection.anchorNode
      if (anchorNode) {
        const tooltipElement = anchorNode.parentElement?.closest('.vxe-tooltip--wrapper')
        if (tooltipElement) {
          // 如果是来自 tooltip 的复制，阻止默认行为
          event.preventDefault()
          return false
        }
      }
    }
  })
}

// 禁止 tooltip 文本选择
const disableTooltipSelection = () => {
  const style = document.createElement('style')
  style.innerHTML = `
    .vxe-tooltip--wrapper,
    .vxe-tooltip--content {
      user-select: none !important;
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
    }
  `
  document.head.appendChild(style)
}
/** 初始化 **/
onMounted(() => {
  handleList()
  disableTooltipCopy()
  disableTooltipSelection()
})
</script>

<style scoped>
.change-log {
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  margin-bottom: -5px;
}

.tooltip-content {
  white-space: normal;
  /* 允许多行文本 */
  word-break: break-all;
  /* 防止长单词溢出 */
  padding: 8px;
  line-height: 1.5;
}

:deep(.vxe-cell) {
  min-height: 30px !important;
}

:deep(.row-retired) {
  color: #d3d3d3 !important;
}
:deep(.row-retired .el-tag.el-tag--primary) {
  --el-tag-text-color: inherit !important;
  color: inherit !important;
}

.path-with-copy {
  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.path-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 5px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.copy-btn {
  opacity: 0;
  transition: opacity 0.3s;
  padding: 2px;
}

.path-with-copy:hover .copy-btn {
  opacity: 1;
}
</style>
