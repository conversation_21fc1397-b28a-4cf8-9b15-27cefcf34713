<template>
  <vxe-toolbar class="h-40px" ref="toolbarRef" custom>
    <template #buttons>
      <el-radio-group size="small" v-model="queryParams.type" @change="getList">
        <el-radio-button label="仅显示单头" :value="1" />
        <el-radio-button label="明细" :value="2" />
      </el-radio-group>

      <el-switch
        v-model="queryParams.showTop"
        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
        inline-prompt
        active-text="只看最新版本"
        inactive-text="显示所有版本"
        size="small"
        @change="handleList"
      >
        <template #open>启用</template>
        <template #close>禁用</template>
      </el-switch>

      <el-button type="success" size="small" @click="handleList">刷新</el-button>
      <el-button type="primary" size="small" @click="routeFormRef?.openForm()">新增</el-button>
    </template>
  </vxe-toolbar>
  <div class="h-[calc(100%-80px)]">
    <vxe-table
      height="100%"
      :header-cell-style="{ padding: 0, height: '30px' }"
      :cell-style="{ padding: 0, height: '30px', cursor: 'pointer',color: '#232323' }"
      :row-config="{ isHover: true, isCurrent: true,height: 30 }"
      align="center"
      ref="tableRef"
      border
      stripe
      :loading="loading"
      :data="list"
      @cell-click="({ row }: any) => routeFormRef?.openForm(row.id)"
      :menu-config="menuConfig"
      @menu-click="menuClickEvent"
    >
      <vxe-column title="路线编码" field="routeCode">
        <template #header>
          <div>路线编码</div>
          <el-input v-model="queryParams.routeCode" size="small" @change="handleList" />
        </template>
      </vxe-column>
      <vxe-column title="路线名称" field="routeName">
        <template #header>
          <div>路线名称</div>
          <el-input v-model="queryParams.routeName" size="small" @change="handleList" />
        </template>
      </vxe-column>
      <vxe-column title="路线版本" field="routeVersion" />
      <vxe-column title="路线描述" field="routeDescription" />
      <template v-if="queryParams.type === 2">
        <vxe-column title="工艺编码" field="defineCode" />
        <vxe-column title="工艺名称" field="defineName" />
        <vxe-column title="标准工时（分）" field="timeConsuming" />
        <vxe-column title="工艺备注" field="remarks" />
      </template>
    </vxe-table>
  </div>
  <Pagination
    class="h-40px !m-b-0"
    size="small"
    :total="total"
    v-model:page="queryParams.pageNo"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
  <RouteForm ref="routeFormRef" :define-list="defineList" @success="getList" />
</template>

<script lang="ts" setup>
import { DefineApi } from '@/api/report/technology/define'
import { RouteApi } from '@/api/report/technology/route'
import RouteForm from './RouteForm.vue'
const queryParams = reactive({
  type: 1,
  showTop: true,
  pageNo: 1,
  pageSize: 30,
  routeCode: '',
  routeName: ''
})
const total = ref(0)
const toolbarRef = ref()
const tableRef = ref()
const defineList = ref<any[]>([])
const routeFormRef = ref()
const loading = ref(false)
const list = ref<any[]>([])

const handleList = () => {
  queryParams.pageNo = 1
  getList()
}

const getList = async () => {
  loading.value = true
  try {
    const res = await RouteApi.getRoutePage(queryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const onListDefine = async () => {
  const res = await DefineApi.listDefine()
  defineList.value = res
}

const menuConfig = reactive<any>({
  body: {
    options: [[{ code: 'selected', name: '当前工艺路线的物料' }]]
  }
})

const menuClickEvent = ({ menu, row }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'selected':
      emits('search', row.id)
      break
  }
}

const emits = defineEmits(['search'])

onMounted(() => {
  unref(tableRef)?.connect(unref(toolbarRef))
  onListDefine()
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.vxe-header--column .vxe-cell) {
  padding: 0 !important;
}

:deep(.row--stripe){
  background-color: #f9f9f9 !important;
}
</style>
