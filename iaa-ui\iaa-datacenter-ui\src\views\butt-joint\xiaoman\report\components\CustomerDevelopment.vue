<template>
  <div style="height: 50px">
    <vxe-toolbar ref="toolbarRef" custom size="small" class="h-full">
      <template #buttons>
        <el-form inline>
          <el-form-item label="商机时间范围">
            <DateRange
              v-model="queryParams.opportunityTime"
              selected="last3Month"
              @change="handleList"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="onListCustomerDevelopment">查询</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #tools>
        <vxe-tooltip content="导出">
          <vxe-button
            icon="vxe-icon-save"
            circle
            :loading="exportLoading"
            @click="handleExport"
            v-hasPermi="['xiaoman:report:export-customer-development']"
          />
        </vxe-tooltip>
      </template>
    </vxe-toolbar>
  </div>
  <div style="height: calc(100vh - 200px - 60px)">
    <vxe-table
      :header-cell-style="{ padding: 0, fontSize: '12px', height: '24px' }"
      :cell-style="{ padding: 0, fontSize: '12px' }"
      :row-config="{ isCurrent: true, isHover: true,height: 30 }"
      :column-config="{ isCurrent: true }"
      :custom-config="customConfig"
      :export-config="{}"
      :filter-config="{ remote: true }"
      :data="list"
      :loading="loading||exportLoading"
      @cell-click="(el: any) => tableCellClick(el, tableRef)"
      @filter-change="filterMethod"
      height="100%"
      border
      align="center"
      stripe
      ref="tableRef"
      id="vxe-table-customer-development"
    >
      <vxe-column
        title="业务员"
        width="100"
        field="salesperson"
        fixed="left"
        :filters="props.userList"
      >
        <template #default="{ row }">
          {{ row.salesperson?.join(',') }}
        </template>
      </vxe-column>
      <vxe-column
        title="客户所属组别"
        field="departmentName"
        :filters="props.departmentList"
        width="110"
        fixed="left"
      />
      <vxe-column
        title="客户名称"
        field="customerName"
        align="left"
        width="140"
        :key="3"
        fixed="left"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      >
        <template #default="{ row }">
          <el-link
            type="primary"
            size="small"
            link
            v-if="row.customerName"
            @click="openOrderForm(row)"
            :underline="false"
          >
            {{ row.customerName }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        title="国家/地区"
        field="countryName"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="洲/省"
        field="regionOrProvince"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="城市"
        field="city"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column
        title="客户画像"
        field="customerPortraits"
        width="100"
        :filters="props.customerPortrait"
      />
      <vxe-column
        title="客户性质"
        field="nature"
        width="100"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column title="合作阶段" field="trailStatus" width="100" :filters="props.customerStage" />
      <vxe-column
        title="客户官网"
        field="homepage"
        align="left"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      >
        <template #default="{ row }">
          <el-link
            size="small"
            link
            underline
            :href="row.homepage"
            target="_blank"
            v-if="row.homepage"
          >
            {{ row.homepage }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column
        title="客户营收规模评估"
        field="revenueScale"
        width="150"
        :filters="props.customerRevenue"
      />
      <vxe-column
        title="香氛产品规模评估"
        field="fragranceRevenueScale"
        width="150"
        :filters="props.customerOilRevenue"
      />
      <vxe-column title="预估年销售额(万元)" field="estimatedAnnualSales" width="100" />
      <vxe-column title="开发类型" field="developmentType" width="100" :filters="developmentType" />
      <vxe-column
        title="作战产品"
        field="combatProduct"
        width="200"
        :filters="textOption"
        :filter-render="{ name: 'MyTableFilterInput' }"
      />
      <vxe-column title="开发目标" field="developmentTarget" width="100" />
      <vxe-column title="预计开发天数" field="planDevelopmentDays" width="100" />
      <vxe-column title="开始时间" field="createTime" width="140" />
      <vxe-column title="已开发天数" field="developmentDays" width="100" />
      <vxe-column title="时间进度" field="progress" width="150">
        <template #default="{ row }">
          <el-progress :percentage="row.progress" :color="row.hasOverdue ? 'red' : 'green'" />
        </template>
      </vxe-column>
      <vxe-column title="进展" field="stageInfo" width="200" :filters="opportunityStage" />
      <vxe-column title="结果" field="result" width="100">
        <template #default="{ row }">
          <el-tag type="success" v-if="row.result === '赢单'">{{ row.result }}</el-tag>
          <el-tag type="danger" v-if="row.result === '输单'">{{ row.result }}</el-tag>
        </template>
      </vxe-column>
      <vxe-column title="失败原因" field="failTypeName" width="100" />
      <vxe-column title="失败描述" field="failRemark" width="200" />
      <vxe-column title="实际完成日期" field="accountDate" width="140" />
      <vxe-column title="实际用时(天)" field="accountDays" width="100" />
    </vxe-table>
    <!-- <vxe-grid v-bind="gridOptions" /> -->
    <!-- 分页 -->
    <Pagination
      :total="total"
      size="small"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="onListCustomerDevelopment"
      class="!float-left"
    />
  </div>
  <OrderForm ref="orderFormRef" />
</template>

<script setup lang="ts">
import { customConfig, tableCellClick } from '@/utils/vxeCustom'
import * as ReportApi from '@/api/butt-joint/xiaoman/report'
import OrderForm from './OrderForm.vue'
import dayjs from 'dayjs'
import { propTypes } from '@/utils/propTypes'
import { getStrDictOptions } from '@/utils/dict'
import download from '@/utils/download'

const tableRef = ref()
const toolbarRef = ref()
const orderFormRef = ref()

const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  opportunityTime: [
    dayjs().subtract(3, 'months').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ],
  salesperson: [] as string[],
  departmentName: [] as number[],
  customerName: '',
  countryName: '',
  regionOrProvince: '',
  city: '',
  trailStatus: [] as string[],
  customerPortraits: [] as string[],
  nature: '',
  homepage: '',
  revenueScale: [] as string[],
  fragranceRevenueScale: [] as string[],
  developmentType: [] as string[],
  stageInfo: [] as string[],
  combatProduct: ''
})
const list = ref<any[]>([])
const total = ref(0)
const loading = ref(false)

const props = defineProps({
  userList: propTypes.oneOfType([Array<any>]).isRequired,
  departmentList: propTypes.oneOfType([Array<any>]).isRequired,
  customerPortrait: propTypes.oneOfType([Array<any>]).isRequired,
  customerStage: propTypes.oneOfType([Array<any>]).isRequired,
  customerRevenue: propTypes.oneOfType([Array<any>]).isRequired,
  customerOilRevenue: propTypes.oneOfType([Array<any>]).isRequired
})
const handleList = () => {
  queryParams.value.pageNo = 1
  onListCustomerDevelopment()
}

const onListCustomerDevelopment = async () => {
  loading.value = true
  try {
    const res = await ReportApi.pageCustomerDevelopment(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 链接 toolbar 和 table */
const connectToolbarAndTable = () => {
  const $table = tableRef.value
  const $toolbar = toolbarRef.value
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
}

const openOrderForm = (row: any) => {
  orderFormRef.value.open(row, 'customer')
}

const textOption = ref([{ data: '' }])
const filterMethod: any = (filter: any) => {
  if (typeof queryParams.value[filter.field] === 'string') {
    queryParams.value[filter.field] = filter.datas.join(',')
  } else {
    queryParams.value[filter.field] = filter.values
  }
  handleList()
}

const developmentType = computed(() => {
  return getStrDictOptions('xiaoman_development_type').map((item) => {
    return { label: item.label, value: item.value }
  })
})

const opportunityStage = computed(() => {
  return getStrDictOptions('xiaoman_opportunity_stage').map((item) => {
    return { label: item.label, value: item.value }
  })
})

const exportLoading = ref()

const message = useMessage()

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ReportApi.exportCustomerDevelopment(queryParams.value)
    download.excel(data, `小满销售作战客户开发明细表${dayjs().unix()}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}
onMounted(() => {
  onListCustomerDevelopment()
  connectToolbarAndTable()
})
</script>
