<template>
  <el-popover placement="bottom" trigger="click" width="300" :visible="visiable">
    <template #reference>
      <div
        :class="{
          'date-value': true,
          'w-180px': props.size == 'default',
          'w-160px': props.size == 'small',
          'h-30px leading-30px': props.size == 'default',
          'h-22px leading-22px': props.size == 'small',
          'px-10px': true,
          'text-12px': props.size == 'small'
        }"
        @click="visiable = !visiable"
      >
        {{
          shortcutsSelected
            ? shortcuts.find((item) => item.value === shortcutsSelected)?.label
            : props.type === 'date'
              ? `${valueDate[0]}至${valueDate[1]}`
              : `${dayjs(valueDate[0]).format('YYYY-MM')}至${dayjs(valueDate[1]).format('YYYY-MM')}`
        }}
      </div>
    </template>
    <el-radio-group size="small" v-model="shortcutsSelected" @change="shortcutsSelectedChange">
      <el-radio
        v-for="(item, index) in shortcuts"
        :key="index"
        :label="item.label"
        :value="item.value"
        class="w-33% !mr-0"
        v-show="!props.hideRadio || !props.hideRadio.includes(item.value)"
      />
    </el-radio-group>
    <el-date-picker
      :type="props.type"
      ref="startDateRef"
      size="small"
      class="!w-120px"
      v-model="valueDate[0]"
      :clearable="false"
      :value-format="formatStr"
      @change="dateChange(1)"
    />
    --
    <el-date-picker
      :type="props.type"
      ref="endDateRef"
      size="small"
      class="!w-120px"
      v-model="valueDate[1]"
      :clearable="false"
      :value-format="formatStr"
      @change="dateChange(2)"
    />
    <el-button class="!mt-5px float-right" size="small" type="primary" @click="comfrim">
      确定
    </el-button>
  </el-popover>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import dayjs from 'dayjs'
dayjs.locale('zh-cn')

const props = defineProps({
  modelValue: propTypes.oneOfType([Array<string>]).def([]),
  size: propTypes.oneOf<string>(['default', 'small']).def('default'),
  selected: propTypes
    .oneOf<string>([
      'today',
      'yesterday',
      'thisWeek',
      'lastWeek',
      'thisMonth',
      'lastMonth',
      'last3Month',
      'after3Month',
      'last6Month',
      'last1Year',
      'thisYear',
      'lastYear'
    ])
    .def('today'),
  type: propTypes.oneOf<string>(['date', 'month']).def('date') as any,
  hideRadio: propTypes.oneOfType([Array<string>]).def([])
})

const emit = defineEmits(['update:modelValue', 'change'])

const visiable = ref(false)
const shortcutsSelected = ref()
const valueDate = ref<string[] | undefined[]>([])

const startDateRef = ref()
const endDateRef = ref()

const dateChange = (type: number) => {
  // 结束日期小于开始日期
  // console.log(dayjs(valueDate[1]).isBefore(dayjs(valueDate[0])))
  if (type == 2 && dayjs(valueDate.value[1]).isBefore(dayjs(valueDate.value[0]))) {
    startDateRef.value.focus()
    valueDate.value[0] = undefined
  }
  // 开始日期大于结束日期
  else if (type == 1 && dayjs(valueDate.value[0]).isAfter(dayjs(valueDate.value[1]))) {
    endDateRef.value.focus()
    valueDate.value[1] = undefined
  } else {
    shortcutsSelected.value = ''
  }
}

const setVal = (val:string)=>{
  const item = shortcuts.value.find((item) => item.value === val)!
  valueDate.value = [item.date.start, item.date.end]
}

// 快捷选项选中
const shortcutsSelectedChange = (val: string) => {
  setVal(val)
  visiable.value = false
  emit('update:modelValue', valueDate.value)
  emit('change', valueDate.value)
}
// 确认选中当前日期
const comfrim = () => {
  if (
    valueDate.value[0] &&
    valueDate.value[1] &&
    (dayjs(valueDate.value[0]).isBefore(valueDate.value[1]) || valueDate[0] == valueDate[1])
  ) {
    emit('update:modelValue', valueDate.value)
    emit('change', valueDate.value)
    visiable.value = false
  }
}

// 快捷选项
const formatStr = 'YYYY-MM-DD'
const shortcuts = ref([
  {
    label: '今天',
    value: 'today',
    date: {
      start: dayjs().format(formatStr),
      end: dayjs().format(formatStr)
    }
  },
  {
    label: '昨天',
    value: 'yesterday',
    date: {
      start: dayjs().subtract(1, 'days').format(formatStr),
      end: dayjs().subtract(1, 'days').format(formatStr)
    }
  },
  {
    label: '本周',
    value: 'thisWeek',
    date: {
      start: dayjs().startOf('weeks').format(formatStr),
      end: dayjs().endOf('weeks').format(formatStr)
    }
  },
  {
    label: '上周',
    value: 'lastWeek',
    date: {
      start: dayjs().subtract(1, 'weeks').startOf('weeks').format(formatStr),
      end: dayjs().subtract(1, 'weeks').endOf('weeks').format(formatStr)
    }
  },
  {
    label: '本月',
    value: 'thisMonth',
    date: {
      start: dayjs().startOf('months').format(formatStr),
      end: dayjs().endOf('months').format(formatStr)
    }
  },
  {
    label: '上月',
    value: 'lastMonth',
    date: {
      start: dayjs().subtract(1, 'months').startOf('months').format(formatStr),
      end: dayjs().subtract(1, 'months').endOf('months').format(formatStr)
    }
  },
  {
    label: '前三月',
    value: 'last3Month',
    date: {
      start: dayjs().subtract(3, 'months').format(formatStr),
      end: dayjs().format(formatStr)
    }
  },
  {
    label: '后三月',
    value: 'after3Month',
    date: {
      start: dayjs().format(formatStr),
      end: dayjs().add(3, 'months').format(formatStr)
    }
  },
  {
    label: '前半年',
    value: 'last6Month',
    date: {
      start: dayjs().subtract(6, 'months').format(formatStr),
      end: dayjs().format(formatStr)
    }
  },
  {
    label: '前一年',
    value: 'last1Year',
    date: {
      start: dayjs().subtract(12, 'months').format(formatStr),
      end: dayjs().format(formatStr)
    }
  },
  {
    label: '本年',
    value: 'thisYear',
    date: {
      start: dayjs().startOf('years').format(formatStr),
      end: dayjs().endOf('years').format(formatStr)
    }
  },
  {
    label: '去年',
    value: 'lastYear',
    date: {
      start: dayjs().subtract(1, 'years').startOf('years').format(formatStr),
      end: dayjs().subtract(1, 'years').endOf('years').format(formatStr)
    }
  }
])
// 啥都不干直接隐藏
const hide = () => {
  visiable.value = false
}

defineExpose({ hide })

watch(
  () => props.modelValue,
  (val) => {
    valueDate.value = val
  }
)

watch(
  () => props.selected,
  (val) => {
    shortcutsSelected.value = val
    setVal(val)
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.date-value {
  border: 0.3px solid #e4e7ed;
  border-radius: 5px;
  cursor: pointer;
}
</style>
