import request from '@/config/axios'

// 收款信息 API
export const InformationApi = {
  // 查询收款信息分页
  getInformationPage: async (params: any) => {
    return await request.post({ url: `/collection/information/page`, data: params })
  },

  // 查询收款信息详情
  getInformation: async (id: any ) => {
    return await request.post({ url: `/collection/information/get`,data: id })
  },

  // 获取暂存/继续认领的预填信息（传收款信息ID）
  getSuspended: async (id: number) => {
    return await request.get({ url: `/collection/information/getSuspended`, params: { id } })
  },

  // 新增收款信息
  createInformation: async (data: any) => {
    return await request.post({ url: `/collection/information/create`, data })
  },

  // 删除收款信息
  deleteInformation: async (ids: any) => {
    return await request.post({ url: `/collection/information/deletes`,data:ids })
  },

  // 导出收款信息 Excel
  exportInformation: async (params) => {
    return await request.download({ url: `/collection/information/export-excel`, params })
  },

    //获取下载模版
  exportTemplate: () => {
    return request.download({ url: `/collection/information/export-template` })
  },
}
