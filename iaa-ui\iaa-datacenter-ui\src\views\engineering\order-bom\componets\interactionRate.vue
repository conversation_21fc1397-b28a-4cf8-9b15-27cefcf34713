<template>
  <div class="interaction-rate-container">
    <!-- 月度BOM准时交付率统计 -->
    <div class="delivery-rate-section mb-6">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span class="text-lg font-medium">
              <span v-if="timeType === 'week'"
                >第{{ getWeekNumber(queryParams.week) }}周BOM准时交付率统计</span
              >
              <span v-else>月度BOM准时交付率统计</span>
            </span>
            <span class="text-sm text-gray-500 ml-2">
              <div class="flex items-center gap-4">
                <!-- 添加周/月切换按钮 -->
                <el-radio-group v-model="timeType" size="small" @change="handleTimeTypeChange">
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                </el-radio-group>
                <!-- 周选择器 -->
                <el-date-picker
                  v-if="timeType === 'week'"
                  v-model="weekDatePicker"
                  type="week"
                  :format="weekStart + ' 至 ' + weekEnd + '第 WW 周'"
                  size="small"
                  @change="handleWeekChange"
                  :clearable="false"
                />

                <!-- 月选择器 -->
                <el-date-picker
                  v-if="timeType === 'month'"
                  v-model="queryParams.month"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  size="small"
                  @change="handleQuery"
                  :clearable="false"
                />
                <el-button type="primary" size="small" @click="handleQuery">查询</el-button>
              </div>
            </span>
          </div>
        </template>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div
            v-for="item in deliveryRateData"
            :key="item.type"
            class="stat-card p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
            @click="openDetailDialog(item)"
          >
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600 mb-2"> {{ item.rate }}% </div>
              <div class="text-sm text-gray-600 mb-1">{{ item.name }}</div>
              <div class="text-xs text-gray-400"> {{ item.completed }}/{{ item.current }} </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 月度设计交付率统计 -->
    <div class="design-rate-section mb-6">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span class="text-lg font-medium">
              <span v-if="timeType === 'week'"
                >第{{ getWeekNumber(queryParams.week) }}周设计交付率统计</span
              >
              <span v-else>月度设计交付率统计</span>
            </span>
            <span class="text-sm text-gray-500 ml-2">
              <span v-if="timeType === 'week'"> {{ weekStart }} 到 {{ weekEnd }} </span>
              <span v-else>
                {{ queryParams.month || '当月' }}
              </span>
            </span>
            <span
              ><el-button
                @click="handleExport()"
                type="success"
                style="margin-left: 30px"
                size="small"
                >导出</el-button
              ></span
            >
          </div>
        </template>
        <div class="overflow-x-auto">
          <el-table :data="designRateData" border size="small" class="w-full">
            <el-table-column prop="name" label="板块" min-width="120" align="center" />
            <el-table-column
              prop="total"
              :label="timeType === 'week' ? '周总任务数' : '月度总任务数'"
              min-width="120"
              align="center"
            />
            <el-table-column prop="current" label="当前应完成数" min-width="120" align="center" />
            <el-table-column prop="completed" label="如期完成数" min-width="120" align="center" />
            <el-table-column prop="rate" label="交付率" min-width="100" align="center">
              <template #default="{ row }">
                <span class="font-medium" :class="getRateColor(row.rate)"> {{ row.rate }}% </span>
              </template>
            </el-table-column>
            <el-table-column prop="delayed" label="延期数" min-width="100" align="center" />
            <el-table-column prop="unfinished" label="未到期数" min-width="100" align="center" />
            <el-table-column label="状态" min-width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.rate)" size="small">
                  {{ getStatusText(row.rate) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 年度趋势图表 -->
    <div class="trend-chart-section">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span class="text-lg font-medium">年度准时交付率趋势图</span>
            <span class="text-sm text-gray-500 ml-2">
              <div class="flex items-center gap-4">
                <!-- 年选择器 -->
                <el-date-picker
                  v-if="trendType === 'year'"
                  v-model="queryParams.year"
                  type="year"
                  placeholder="选择年份"
                  format="YYYY"
                  value-format="YYYY"
                  size="small"
                  @change="handleQueryYear"
                  :clearable="false"
                />
                <el-button type="primary" size="small" @click="handleQueryYear"> 查询 </el-button>
              </div>
            </span>
          </div>
        </template>
        <div class="chart-container" style="height: 400px">
          <Echart :options="chartOptions" height="400px" />
        </div>
      </el-card>
    </div>
  </div>

  <!-- 添加详情弹出层 -->
  <el-dialog
    v-model="detailDialogVisible"
    :title="selectedItem?.name + '详情'"
    width="90%"
    align-center
    append-to-body
    :draggable="true"
  >
    <template #header>
      <div class="flex justify-between items-center w-full">
        <span>{{ selectedItem?.name }}详情</span>
        <el-button type="success" size="small" @click="handleExportDetail" :loading="exportLoading">
          导出
        </el-button>
      </div>
    </template>
    <div v-if="typeTable === 'main'" class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 30 }"
        id="mainTable"
        :header-cell-style="{ padding: 0, height: '30px' }"
        :cell-style="{ padding: 0, height: '30px' }"
        :checkbox-config="{ labelField: 'check', range: true }"
        :column-config="{ resizable: true, maxFixedSize: 0 }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        align="center"
        border
        show-overflow
        height="100%"
        :data="dataList"
        ref="tableRef"
        size="small"
        show-header-overflow
        show-overflow-tooltip
        :loading="loading"
      >
        <vxe-column field="taskStatus" width="120" title="任务状态">
          <template #header>
            <div class="w-100%">任务状态</div>
            <el-select
              v-model="queryTableParams.taskStatus"
              clearable
              collapse-tags
              size="small"
              class="!w-100%"
              @change="getList"
            >
              <el-option label="未到期" value="未到期" />
              <el-option label="超期未完成" value="超期未完成" />
              <el-option label="超期完成" value="超期完成" />
              <el-option label="按期完成" value="按期完成" />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="businessDate" width="120" title="日期" />
        <vxe-column field="seller" width="100" title="业务员" />
        <vxe-column field="customerName" width="120" title="客户" />
        <vxe-column field="itemCode" width="120" title="品号" />
        <vxe-column field="itemName" width="140" title="品名" />
        <vxe-column field="spec" min-width="200" title="规格" />
        <vxe-column field="lastOrderQty" width="120" title="最后下单数量">
          <template #header>
            <div>最后下单数量</div>
          </template>
        </vxe-column>
        <vxe-column field="lastOrderDate" width="120" title="最后下单日期">
          <template #header>
            <div>最后下单日期</div>
          </template>
        </vxe-column>
        <vxe-column field="packing" width="120" title="定制包材类">
          <template #default="{ row }">
            <el-tag type="success" v-if="!row.packing || row?.packing == '99'">无定制</el-tag>
            <DetailDate :data="row.packingDetail" v-else />
          </template>
        </vxe-column>
        <vxe-column field="logo" width="120" title="定制logo类">
          <template #default="{ row }">
            <el-tag type="success" v-if="!row.logo || row?.logo == '99'">无定制</el-tag>
            <DetailDate :data="row.logoDetail" v-else />
          </template>
        </vxe-column>
        <vxe-column field="instruction" width="120" title="定制说明书类">
          <template #default="{ row }">
            <el-tag type="success" v-if="!row.instruction || row?.instruction == '99'"
              >无定制</el-tag
            >
            <DetailDate :data="row.instructionDetail" v-else />
          </template>
        </vxe-column>
        <vxe-column field="program" width="120" title="定制程序类">
          <template #default="{ row }">
            <el-tag type="success" v-if="!row.program || row?.program == '99'">无定制</el-tag>
            <DetailDate :data="row.programDetail" v-else />
          </template>
        </vxe-column>
        <vxe-column field="structure" width="120" title="定制结构类">
          <template #default="{ row }">
            <el-tag type="success" v-if="!row.structure || row?.structure == '99'">无定制</el-tag>
            <DetailDate :data="row.structureDetail" v-else />
          </template>
        </vxe-column>
        <vxe-column field="remark" width="120" title="备注" />
        <vxe-column field="planBomCompleteDate" width="200" title="BOM计划完成日期" />
        <vxe-column field="planBomCompleteDateChange" width="200" title="BOM计划完成变更日期" />
        <vxe-column field="planCompleteDate" width="200" title="计划完成日期" />
        <vxe-column field="actualCompleteDate" width="200" title="实际完成日期" />
      </vxe-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryTableParams.pageSize"
        v-model:page="queryTableParams.pageNo"
        :total="total"
        @pagination="getList"
        size="small"
      />
    </div>
    <div v-if="typeTable === 'logo'" class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 30 }"
        :header-cell-style="{ padding: 0, height: '30px' }"
        id="logoTable"
        align="center"
        border
        show-overflow
        height="100%"
        :data="dataList"
        ref="tableRef"
        size="small"
        :column-config="{ resizable: true, maxFixedSize: 0 }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        show-header-overflow
        show-overflow-tooltip
        :loading="loading"
        v-if="typeTable === 'logo'"
      >
        <vxe-column field="taskStatus" width="100" title="任务状态">
          <template #header>
            <div class="w-100%">任务状态</div>
            <el-select
              v-model="queryTableParams.taskStatus"
              clearable
              collapse-tags
              size="small"
              class="!w-100%"
              @change="getSonList"
            >
              <el-option label="未到期" value="未到期" />
              <el-option label="超期未完成" value="超期未完成" />
              <el-option label="超期完成" value="超期完成" />
              <el-option label="按期完成" value="按期完成" />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="businessDate" width="200" title="日期" />
        <vxe-column field="seller" width="100" title="业务员" />
        <vxe-column field="customerName" width="120" title="客户" />

        <vxe-column field="itemCode" width="120" title="品号" />
        <vxe-column field="itemName" width="140" title="品名" />
        <vxe-column field="spec" min-width="200" title="规格" />
        <vxe-column field="custom" width="120" title="定制内容">
          <template #default="{ row }">
            {{ getDictLabel('eng_logo_dict', row.custom) }}
          </template>
        </vxe-column>
        <vxe-column field="description" width="200" title="任务" />
        <vxe-column field="planReceiptDate" width="200" title="计划资料接收日期" />
        <vxe-column field="actualReceiptDate" width="200" title="实际资料接收日期" />
        <vxe-column field="planDesignDate" width="200" title="计划设计日期" />
        <vxe-column field="actualDesignDate" width="200" title="实际设计日期" />
        <vxe-column field="planTestingDate" width="200" title="计划打样日期" />
        <vxe-column field="actualTestingDate" width="200" title="实际打样日期" />
        <vxe-column field="planAdmitDate" width="200" title="计划确认日期" />
        <vxe-column field="actualAdmitDate" width="200" title="实际确认日期" />
        <vxe-column field="planCompleteDate" width="200" title="计划完成日期" />
        <vxe-column field="actualCompleteDate" width="200" title="实际完成日期" />
        <vxe-column field="remark" width="200" title="备注" />
      </vxe-table>
      <Pagination
        v-model:limit="queryTableParams.pageSize"
        v-model:page="queryTableParams.pageNo"
        :total="total"
        @pagination="getSonList"
        size="small"
      />
    </div>
    <div v-if="typeTable === 'program'" class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 30 }"
        :header-cell-style="{ padding: 0, height: '30px' }"
        id="programTable"
        align="center"
        border
        show-overflow
        height="100%"
        :data="dataList"
        ref="tableRef"
        size="small"
        :column-config="{ resizable: true, maxFixedSize: 0 }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        show-header-overflow
        show-overflow-tooltip
        :loading="loading"
        v-if="typeTable === 'program'"
      >
        <vxe-column field="taskStatus" width="100" title="任务状态">
          <template #header>
            <div class="w-100%">任务状态</div>
            <el-select
              v-model="queryTableParams.taskStatus"
              clearable
              collapse-tags
              size="small"
              class="!w-100%"
              @change="getSonList"
            >
              <el-option label="未到期" value="未到期" />
              <el-option label="超期未完成" value="超期未完成" />
              <el-option label="超期完成" value="超期完成" />
              <el-option label="按期完成" value="按期完成" />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="businessDate" width="200" title="日期" />
        <vxe-column field="seller" width="100" title="业务员" />
        <vxe-column field="customerName" width="120" title="客户" />
        <vxe-column field="itemCode" width="120" title="品号" />
        <vxe-column field="itemName" width="140" title="品名" />
        <vxe-column field="spec" min-width="200" title="规格" />
        <vxe-column field="custom" width="120" title="定制内容">
          <template #default="{ row }">
            {{ getDictLabel('eng_program_dict', row.custom) }}
          </template>
        </vxe-column>
        <vxe-column field="description" width="200" title="任务" />
        <vxe-column field="planReceiptDate" width="200" title="计划接收日期" />
        <vxe-column field="actualReceiptDate" width="200" title="实际接收日期" />
        <vxe-column field="planDesignDate" width="200" title="计划设计日期" />
        <vxe-column field="actualDesignDate" width="200" title="实际设计日期" />
        <vxe-column field="planTestingDate" width="200" title="计划测试日期" />
        <vxe-column field="actualTestingDate" width="200" title="实际测试日期" />
        <vxe-column field="planCompleteDate" width="200" title="计划完成日期" />
        <vxe-column field="actualCompleteDate" width="200" title="实际完成日期" />
        <vxe-column field="remark" width="200" title="备注" />
      </vxe-table>
      <Pagination
        v-model:limit="queryTableParams.pageSize"
        v-model:page="queryTableParams.pageNo"
        :total="total"
        @pagination="getSonList"
        size="small"
      />
    </div>
    <div v-if="typeTable === 'structure'" class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 30 }"
        :header-cell-style="{ padding: 0, height: '30px' }"
        id="structureTable"
        align="center"
        border
        show-overflow
        height="100%"
        :data="dataList"
        ref="tableRef"
        size="small"
        :column-config="{ resizable: true, maxFixedSize: 0 }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        show-header-overflow
        show-overflow-tooltip
        :loading="loading"
        v-if="typeTable === 'structure'"
      >
        <vxe-column field="taskStatus" width="100" title="任务状态">
          <template #header>
            <div class="w-100%">任务状态</div>
            <el-select
              v-model="queryTableParams.taskStatus"
              clearable
              collapse-tags
              size="small"
              class="!w-100%"
              @change="getSonList"
            >
              <el-option label="未到期" value="未到期" />
              <el-option label="超期未完成" value="超期未完成" />
              <el-option label="超期完成" value="超期完成" />
              <el-option label="按期完成" value="按期完成" />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="businessDate" width="200" title="日期" />
        <vxe-column field="seller" width="100" title="业务员" />
        <vxe-column field="customerName" width="120" title="客户" />

        <vxe-column field="itemCode" width="120" title="品号" />
        <vxe-column field="itemName" width="140" title="品名" />
        <vxe-column field="spec" min-width="200" title="规格" />
        <vxe-column field="custom" width="120" title="定制内容">
          <template #default="{ row }">
            {{ getDictLabel('eng_structure_dict', row.custom) }}
          </template>
        </vxe-column>
        <vxe-column field="description" width="200" title="任务" />
        <vxe-column field="planReceiptDate" width="200" title="计划接收日期" />
        <vxe-column field="actualReceiptDate" width="200" title="实际接收日期" />
        <vxe-column field="planDesignDate" width="200" title="计划设计日期" />
        <vxe-column field="actualDesignDate" width="200" title="实际设计日期" />
        <vxe-column field="planTestingDate" width="200" title="计划打样日期" />
        <vxe-column field="actualTestingDate" width="200" title="实际打样日期" />
        <vxe-column field="planAdmitDate" width="200" title="计划承认日期" />
        <vxe-column field="actualAdmitDate" width="200" title="实际承认日期" />
        <vxe-column field="planCompleteDate" width="200" title="计划完成日期" />
        <vxe-column field="actualCompleteDate" width="200" title="实际完成日期" />
        <vxe-column field="remark" width="200" title="备注" />
      </vxe-table>
      <Pagination
        v-model:limit="queryTableParams.pageSize"
        v-model:page="queryTableParams.pageNo"
        :total="total"
        @pagination="getSonList"
        size="small"
      />
    </div>
    <div v-if="typeTable === 'packing'" class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 30 }"
        :header-cell-style="{ padding: 0, height: '30px' }"
        id="packingTable"
        align="center"
        border
        show-overflow
        height="100%"
        :data="dataList"
        ref="tableRef"
        size="small"
        :column-config="{ resizable: true, maxFixedSize: 0 }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        show-header-overflow
        show-overflow-tooltip
        :loading="loading"
        v-if="typeTable === 'packing'"
      >
        <vxe-column field="taskStatus" width="100" title="任务状态">
          <template #header>
            <div class="w-100%">任务状态</div>
            <el-select
              v-model="queryTableParams.taskStatus"
              clearable
              collapse-tags
              size="small"
              class="!w-100%"
              @change="getSonList"
            >
              <el-option label="未到期" value="未到期" />
              <el-option label="超期未完成" value="超期未完成" />
              <el-option label="超期完成" value="超期完成" />
              <el-option label="按期完成" value="按期完成" />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="businessDate" width="200" title="日期" />
        <vxe-column field="seller" width="100" title="业务员" />
        <vxe-column field="customerName" width="120" title="客户" />
        <vxe-column field="itemCode" width="120" title="品号" />
        <vxe-column field="itemName" width="140" title="品名" />
        <vxe-column field="spec" min-width="200" title="规格" />
        <vxe-column field="custom" width="120" title="定制内容">
          <template #default="{ row }">
            {{ getDictLabel('eng_packing_dict', row.custom) }}
          </template>
        </vxe-column>
        <vxe-column field="description" width="200" title="任务" />
        <vxe-column field="planReceiptDate" width="200" title="计划接收日期" />
        <vxe-column field="actualReceiptDate" width="200" title="实际接收日期" />
        <vxe-column field="planDesignDate" width="200" title="计划设计日期" />
        <vxe-column field="actualDesignDate" width="200" title="实际设计日期" />
        <vxe-column field="planQuotationDate" width="200" title="计划设计稿确认" />
        <vxe-column field="actualQuotationDate" width="200" title="实际设计稿确认" />
        <vxe-column field="planTestingDate" width="200" title="计划打样日期" />
        <vxe-column field="actualTestingDate" width="200" title="实际打样日期" />
        <vxe-column field="planAdmitDate" width="200" title="计划确认日期" />
        <vxe-column field="actualAdmitDate" width="200" title="实际确认日期" />
        <vxe-column field="planCompleteDate" width="200" title="计划完成日期" />
        <vxe-column field="actualCompleteDate" width="200" title="实际完成日期" />
        <vxe-column field="planBomCompleteDate" width="200" title="BOM计划完成日期" />
        <vxe-column field="planBomCompleteDateChange" width="200" title="BOM计划变更日期" />
        <vxe-column field="remark" width="200" title="备注" />
      </vxe-table>
      <Pagination
        v-model:limit="queryTableParams.pageSize"
        v-model:page="queryTableParams.pageNo"
        :total="total"
        @pagination="getSonList"
        size="small"
      />
    </div>
    <div v-if="typeTable === 'instruction'" class="h-[calc(100vh-260px)]">
      <vxe-table
        :row-config="{ height: 30 }"
        :header-cell-style="{ padding: 0, height: '30px' }"
        id="instructionTable"
        align="center"
        border
        show-overflow
        height="100%"
        :data="dataList"
        ref="tableRef"
        size="small"
        :column-config="{ resizable: true, maxFixedSize: 0 }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        show-header-overflow
        show-overflow-tooltip
        :loading="loading"
        v-if="typeTable === 'instruction'"
      >
        <vxe-column field="taskStatus" width="100" title="任务状态">
          <template #header>
            <div class="w-100%">任务状态</div>
            <el-select
              v-model="queryTableParams.taskStatus"
              clearable
              collapse-tags
              size="small"
              class="!w-100%"
              @change="getSonList"
            >
              <el-option label="未到期" value="未到期" />
              <el-option label="超期未完成" value="超期未完成" />
              <el-option label="超期完成" value="超期完成" />
              <el-option label="按期完成" value="按期完成" />
            </el-select>
          </template>
        </vxe-column>
        <vxe-column field="businessDate" width="200" title="日期" />
        <vxe-column field="seller" width="100" title="业务员" />
        <vxe-column field="customerName" width="120" title="客户" />

        <vxe-column field="itemCode" width="120" title="品号" />
        <vxe-column field="itemName" width="140" title="品名" />
        <vxe-column field="spec" min-width="200" title="规格" />
        <vxe-column field="custom" width="120" title="定制内容">
          <template #default="{ row }">
            {{ getDictLabel('eng_instruction_dict', row.custom) }}
          </template>
        </vxe-column>
        <vxe-column field="description" width="200" title="任务" />
        <vxe-column field="planReceiptDate" width="200" title="计划接收日期" />
        <vxe-column field="actualReceiptDate" width="200" title="实际接收日期" />
        <vxe-column field="planDesignDate" width="200" title="计划设计日期" />
        <vxe-column field="actualDesignDate" width="200" title="实际设计日期" />
        <vxe-column field="planTestingDate" width="200" title="计划打样日期" />
        <vxe-column field="actualTestingDate" width="200" title="实际打样日期" />
        <vxe-column field="planAdmitDate" width="200" title="计划确认日期" />
        <vxe-column field="actualAdmitDate" width="200" title="实际确认日期" />
        <vxe-column field="planCompleteDate" width="200" title="计划完成日期" />
        <vxe-column field="actualCompleteDate" width="200" title="实际完成日期" />
        <vxe-column field="remark" width="200" title="备注" />
      </vxe-table>
      <Pagination
        v-model:limit="queryTableParams.pageSize"
        v-model:page="queryTableParams.pageNo"
        :total="total"
        @pagination="getSonList"
        size="small"
      />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { Echart } from '@/components/Echart'
import { ElMessage } from 'element-plus'
import download from '@/utils/download'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { CustomerApi } from '@/api/report/erp/order-bom/index'
import { getDictLabel, getStrDictOptions } from '@/utils/dict'
import DetailDate from './DetailDate.vue'

// 添加详情弹窗相关变量
const detailDialogVisible = ref(false)
const selectedItem = ref<any>(null)
const loading = ref(false)
const typeTable = ref()
// 打开详情弹窗方法
const openDetailDialog = (item: any) => {
  dataList.value = []
  queryTableParams.value.type = ''
  queryTableParams.value.taskStatus = ''
  console.log('item', item.name)
  selectedItem.value = item
  // 根据时间类型设置日期范围
  if (timeType.value === 'month') {
    // 月份模式：设置月第一天和最后一天
    const monthStart = dayjs(queryParams.month).startOf('month').format('YYYY-MM-DD')
    const monthEnd = dayjs(queryParams.month).endOf('month').format('YYYY-MM-DD')
    queryTableParams.value.planCompleteDate = [monthStart, monthEnd]
  } else {
    // 周模式：设置周开始日期和结束日期
    queryTableParams.value.planCompleteDate = [weekStart.value, weekEnd.value]
  }
  queryTableParams.value.pageNo = 1
  queryTableParams.value.pageSize = 30
  detailDialogVisible.value = true
  switch (item.name) {
    case '主表':
      typeTable.value = 'main'
      getList()
      break
    case '面板':
      typeTable.value = 'logo'
      queryTableParams.value.type = 'logo'
      getSonList()
      break
    case '包材':
      typeTable.value = 'packing'
      queryTableParams.value.type = 'packing'
      getSonList()
      break
    case '说明书':
      typeTable.value = 'instruction'
      queryTableParams.value.type = 'instruction'
      getSonList()
      break
    case '程序':
      typeTable.value = 'program'
      queryTableParams.value.type = 'program'
      getSonList()
      break
    case '结构':
      typeTable.value = 'structure'
      queryTableParams.value.type = 'structure'
      getSonList()
      break
  }
}

// 扩展 dayjs 以支持 isoWeek 功能
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)

// 查询参数
const queryParams = reactive({
  month: dayjs().format('YYYY-MM'),
  year: dayjs().format('YYYY'),
  week: dayjs().format('YYYY-WW')
})

const dataList = ref<any[]>([])

const total = ref(0)
const queryTableParams = ref({
  pageNo: 1,
  pageSize: 30,
  planCompleteDate: [] as string[],
  taskStatus: '',
  showAll: false,
  type: ''
})

/** 获取主表BOM任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await CustomerApi.pageTask(queryTableParams.value)
    dataList.value = res.list
    total.value = res.total
    loading.value = false
  } finally {
    loading.value = false
  }
}

const getSonList = async () => {
  loading.value = true
  try {
    const res = await CustomerApi.pageDetail(queryTableParams.value)
    dataList.value = res.list
    total.value = res.total
    loading.value = false
  } finally {
    loading.value = false
  }
}

const handleExportDetail = async () => {
  if (typeTable.value === 'main') {
    const data = await CustomerApi.export(queryTableParams.value)
    download.excel(data, `订单BOM跟进数据${queryParams.month}.xlsx`)
  } else {
    //导出子表
    const data = await CustomerApi.exportDetail(queryTableParams.value)
    download.excel(data, selectedItem.value?.name + queryParams.month + '.xlsx')
  }
}
// 时间类型：week 或 month
const timeType = ref('month')
// 趋势图类型：year 或 week
const trendType = ref('year')

const weekDatePicker = ref()
//趋势图 周范围选择
const weekRange = ref()

// 添加周的开始和结束日期
const weekStart = ref()

const weekEnd = ref()

const trendStart = ref()
const trendEnd = ref()

const message = useMessage() // 消息弹窗
const exportLoading = ref(false) // 导出的加载中
// 趋势图数据
const trendData = ref({
  main: [32, 88, 92, 89, 91, 94, 87, 90, 93, 88, 91, 89],
  program: [78, 82, 85, 88, 84, 87, 90, 86, 89, 91, 88, 85],
  structure: [82, 85, 88, 91, 87, 90, 93, 89, 92, 94, 90, 88],
  packing: [75, 78, 82, 85, 81, 84, 87, 83, 86, 89, 85, 82],
  instruction: [88, 91, 94, 92, 95, 93, 96, 94, 97, 95, 93, 91],
  logo: [80, 83, 86, 89, 85, 88, 91, 87, 90, 93, 89, 86]
})

// 月度BOM准时交付率数据
const deliveryRateData = ref([
  { type: 'main', name: '主表', total: 0, completed: 0, current: 0, rate: 0 },
  { type: 'program', name: '程序', total: 0, completed: 0, current: 0, rate: 0 },
  { type: 'structure', name: '结构', total: 0, completed: 0, current: 0, rate: 0 },
  { type: 'packing', name: '包材', total: 0, completed: 0, current: 0, rate: 0 },
  { type: 'instruction', name: '说明书', total: 0, completed: 0, current: 0, rate: 0 },
  { type: 'logo', name: '面板', total: 0, completed: 0, current: 0, rate: 0 }
])

// 月度设计交付率数据
const designRateData = ref([
  { type: 'main', name: '主表', total: 0, completed: 0, delayed: 0, unfinished: 0, rate: 0 },
  { type: 'program', name: '程序', total: 0, completed: 0, delayed: 0, unfinished: 0, rate: 0 },
  { type: 'structure', name: '结构', total: 0, completed: 0, delayed: 0, unfinished: 0, rate: 0 },
  { type: 'packing', name: '包材', total: 0, completed: 0, delayed: 0, unfinished: 0, rate: 0 },
  {
    type: 'instruction',
    name: '说明书',
    total: 0,
    completed: 0,
    delayed: 0,
    unfinished: 0,
    rate: 0
  },
  { type: 'logo', name: '面板', total: 0, completed: 0, delayed: 0, unfinished: 0, rate: 0 }
])
/** 导出日报或异常工时按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CustomerApi.exportDay(queryParams)
    download.excel(data, `月度设计交付率统计_${queryParams.month || dayjs().format('YYYY-MM')}.xls`)
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 获取周数
const getWeekNumber = (weekStr: string) => {
  if (!weekStr) return ''
  return weekStr.split('-')[1]
}

// 时间类型切换处理
const handleTimeTypeChange = () => {
  if (timeType.value === 'week') {
    // 设置为当前周
    weekDatePicker.value = dayjs().startOf('isoWeek').format('YYYY-MM-DD')
    queryParams.week = dayjs().format('YYYY-WW')
    handleWeekChange()
  } else {
    queryParams.month = dayjs().format('YYYY-MM')
    handleQuery()
  }
}

// 周选择器变化处理
const handleWeekChange = () => {
  queryParams.week = dayjs(weekDatePicker.value).format('YYYY-WW')
  weekStart.value = dayjs(weekDatePicker.value).startOf('isoWeek').format('YYYY-MM-DD')
  weekEnd.value = dayjs(weekDatePicker.value).endOf('isoWeek').format('YYYY-MM-DD')
  handleQuery()
}

// 趋势图类型切换处理
const handleTrendTypeChange = () => {
  if (trendType.value === 'week') {
    weekRange.value = dayjs().startOf('isoWeek').format('YYYY-MM-DD')
    handleQueryWeekRange()
  } else {
    queryParams.year = dayjs().format('YYYY')
  }
  // 重新加载数据
  if (trendType.value === 'year') {
    handleQueryYear()
  }
}

// 处理周范围查询
const handleQueryWeekRange = () => {
  trendStart.value = dayjs(weekRange.value).startOf('isoWeek').format('YYYY-MM-DD')
  trendEnd.value = dayjs(weekRange.value).endOf('isoWeek').format('YYYY-MM-DD')
}
// 图表配置
// 图表配置
const chartOptions:any = computed(() => {
  let xAxisData: string[] = []
  let seriesData = {
    main: [] as number[],
    program: [] as number[],
    structure: [] as number[],
    packing: [] as number[],
    instruction: [] as number[],
    logo: [] as number[]
  }

  // 根据趋势图类型设置x轴数据
  if (trendType.value === 'year') {
    xAxisData = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月'
    ]
    seriesData = trendData.value
  } else {
    // 周模式下，生成日期范围内的每日列表
    const startDate = dayjs(trendStart.value)
    const endDate = dayjs(trendEnd.value)
    const dates: string[] = []

    let current = startDate
    while (current.isSameOrBefore(endDate)) {
      dates.push(current.format('YYYY-MM-DD'))
      current = current.add(1, 'day')
    }
    xAxisData = dates

    // 使用实际数据
    seriesData = trendData.value
  }

  return {
    title: {
      text: trendType.value === 'year' ? '各板块月度准时交付率趋势' : '各板块周准时交付率趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 400 
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((item: any) => {
          result += `${item.marker}${item.seriesName}: ${item.value}%<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['主表', '程序', '结构', '包材', '说明书', '面板'],
      top: 30,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      name: '交付率(%)',
      min: 0,
      max: 100,
      axisLine: {
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#606266',
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa'
        }
      }
    },
    series: [
      {
        name: '主表',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        itemStyle: { color: '#409EFF' },
        data: seriesData.main
      },
      {
        name: '程序',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        itemStyle: { color: '#67C23A' },
        data: seriesData.program
      },
      {
        name: '结构',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        itemStyle: { color: '#E6A23C' },
        data: seriesData.structure
      },
      {
        name: '包材',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        itemStyle: { color: '#F56C6C' },
        data: seriesData.packing
      },
      {
        name: '说明书',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        itemStyle: { color: '#909399' },
        data: seriesData.instruction
      },
      {
        name: '面板',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { width: 2 },
        itemStyle: { color: '#9C27B0' },
        data: seriesData.logo
      }
    ]
  }
})

// 获取交付率颜色样式
const getRateColor = (rate: number) => {
  if (rate >= 90) return 'text-green-600'
  if (rate >= 80) return 'text-yellow-600'
  return 'text-red-600'
}

// 获取状态类型
const getStatusType = (rate: number) => {
  if (rate >= 90) return 'success'
  if (rate >= 80) return 'warning'
  return 'danger'
}

// 获取状态文本
const getStatusText = (rate: number) => {
  if (rate >= 90) return '优秀'
  if (rate >= 80) return '良好'
  return '需改进'
}

// 查询数据
const handleQuery = async () => {
  try {
    await Promise.all([fetchDeliveryRateData()])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('数据查询失败')
  }
}

// 查询数据
const handleQueryYear = async () => {
  try {
    await Promise.all([fetchTrendData()])
    ElMessage.success('年度交付率趋势图刷新成功')
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('数据查询失败')
  }
}

// 获取月度BOM准时交付率数据
// 获取月度设计交付率数据
const fetchDeliveryRateData = async () => {
  try {
    let response
    if (timeType.value === 'week') {
      // 传递周的开始和结束日期给后端
      response = await CustomerApi.getDeliveryRate({
        weekStart: weekStart.value,
        weekEnd: weekEnd.value
      })
    } else {
      response = await CustomerApi.getDeliveryRate({ month: queryParams.month })
    }
    deliveryRateData.value = response || []
    designRateData.value = response || []
  } catch (error) {
    console.error('获取交付率数据失败:', error)
  }
}

// 获取趋势图数据
const fetchTrendData = async () => {
  try {
    const response = await CustomerApi.getTrendData({ year: queryParams.year })
    if (response) {
      trendData.value = response
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    // 保持默认的模拟数据
  }
}

// 初始化
onMounted(() => {
  handleQuery()
  fetchTrendData()
})
</script>

<style lang="scss" scoped>
.interaction-rate-container {
  padding: 16px;
}

.filter-section {
  .el-card {
    border: 1px solid #e4e7ed;
  }
}

.stat-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.chart-container {
  width: 100%;
  min-height: 400px;
}

:deep(.el-table) {
  .el-table__header {
    background-color: #f5f7fa;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

:deep(.el-card__body) {
  padding: 20px;
}
:deep(.vxe-cell--title) {
  width: 100% !important;
}
</style>
