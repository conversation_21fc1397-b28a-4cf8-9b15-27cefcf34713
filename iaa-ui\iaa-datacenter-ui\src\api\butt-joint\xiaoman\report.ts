import request from '@/config/axios'

/** 获取小满用户分页数据 */
export const pageOpprotunity = async (data: any) => {
  return await request.post({ url: '/butt-joint/xiaoman/report/page-opportunity', data })
}

// 导出字典类型数据
export const exportOpprotunity = (data: any) => {
  return request.downloadPost({url: '/butt-joint/xiaoman/report/export-opportunity',data:JSON.stringify(data)})
}

/** 获取小满客户开发分页数据 */
export const pageCustomerDevelopment = async (data: any) => {
  return await request.post({ url: '/butt-joint/xiaoman/report/page-customer-development', data })
}

// 导出字典类型数据
export const exportCustomerDevelopment = (data: any) => {
  return request.downloadPost({url: '/butt-joint/xiaoman/report/export-customer-development',data:JSON.stringify(data)})
}


/** 获取小满滚动订单分页数据 */
export const pageOrder = async (data: any) => {
  return await request.post({ url: '/butt-joint/xiaoman/report/page-order', data })
}


// 导出字典类型数据
export const exportOrder = (data: any) => {
  return request.downloadPost({url: '/butt-joint/xiaoman/report/export-order',data:JSON.stringify(data)})
}
